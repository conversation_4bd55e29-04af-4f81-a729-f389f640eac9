<?php

/**
 * Script d'exportation des données réelles de la base de données
 */

echo "🗄️  EXPORTATION DE TOUTES VOS DONNÉES\n";
echo "=====================================\n\n";

try {
    // Connexion à la base de données principale
    $pdo = new PDO('sqlite:database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connexion réussie à database.sqlite\n\n";
    
    // 1. UTILISATEURS
    echo "👥 VOS UTILISATEURS :\n";
    echo "====================\n";
    try {
        // D'abord, découvrir la structure de la table users
        $stmt = $pdo->query("PRAGMA table_info(users)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $userColumns = [];
        foreach ($columns as $col) {
            $userColumns[] = $col['name'];
        }
        
        $stmt = $pdo->query("SELECT * FROM users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Structure de la table users : " . implode(', ', $userColumns) . "\n\n";
        
        if (count($users) > 0) {
            foreach ($users as $user) {
                echo "• Utilisateur ID: {$user['id']}\n";
                foreach ($user as $key => $value) {
                    if ($key !== 'password') { // Ne pas afficher le mot de passe
                        echo "  $key: $value\n";
                    }
                }
                echo "  ---\n";
            }
        } else {
            echo "Aucun utilisateur trouvé.\n";
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 2. RÔLES
    echo "🔐 VOS RÔLES :\n";
    echo "=============\n";
    try {
        $stmt = $pdo->query("SELECT * FROM role");
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($roles) > 0) {
            foreach ($roles as $role) {
                echo "• Rôle ID: {$role['id']}\n";
                foreach ($role as $key => $value) {
                    echo "  $key: $value\n";
                }
                echo "  ---\n";
            }
        } else {
            echo "Aucun rôle trouvé.\n";
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 3. PERMISSIONS
    echo "🔑 VOS PERMISSIONS :\n";
    echo "===================\n";
    try {
        $stmt = $pdo->query("SELECT * FROM permission");
        $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($permissions) > 0) {
            foreach ($permissions as $permission) {
                echo "• Permission ID: {$permission['id']}\n";
                foreach ($permission as $key => $value) {
                    echo "  $key: $value\n";
                }
                echo "  ---\n";
            }
        } else {
            echo "Aucune permission trouvée.\n";
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 4. STRUCTURES
    echo "🏢 VOS STRUCTURES :\n";
    echo "==================\n";
    try {
        $stmt = $pdo->query("SELECT * FROM structure");
        $structures = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($structures) > 0) {
            foreach ($structures as $structure) {
                echo "• Structure ID: {$structure['id']}\n";
                foreach ($structure as $key => $value) {
                    echo "  $key: $value\n";
                }
                echo "  ---\n";
            }
        } else {
            echo "Aucune structure trouvée.\n";
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 5. TYPES DE RÉFORMES
    echo "📋 VOS TYPES DE RÉFORMES :\n";
    echo "=========================\n";
    try {
        $stmt = $pdo->query("SELECT * FROM type_reforme");
        $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($types) > 0) {
            foreach ($types as $type) {
                echo "• Type ID: {$type['id']}\n";
                foreach ($type as $key => $value) {
                    echo "  $key: $value\n";
                }
                echo "  ---\n";
            }
        } else {
            echo "Aucun type trouvé.\n";
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 6. RÉFORMES
    echo "🔄 VOS RÉFORMES :\n";
    echo "================\n";
    try {
        // Découvrir la structure de la table reformes
        $stmt = $pdo->query("PRAGMA table_info(reformes)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $reformeColumns = [];
        foreach ($columns as $col) {
            $reformeColumns[] = $col['name'];
        }
        
        echo "Structure de la table reformes : " . implode(', ', $reformeColumns) . "\n\n";
        
        $stmt = $pdo->query("SELECT * FROM reformes");
        $reformes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($reformes) > 0) {
            foreach ($reformes as $reforme) {
                echo "• Réforme ID: {$reforme['id']}\n";
                foreach ($reforme as $key => $value) {
                    echo "  $key: $value\n";
                }
                echo "  ---\n";
            }
        } else {
            echo "Aucune réforme trouvée.\n";
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 7. INDICATEURS
    echo "📊 VOS INDICATEURS :\n";
    echo "===================\n";
    try {
        // Découvrir la structure de la table indicateurs
        $stmt = $pdo->query("PRAGMA table_info(indicateurs)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $indicateurColumns = [];
        foreach ($columns as $col) {
            $indicateurColumns[] = $col['name'];
        }
        
        echo "Structure de la table indicateurs : " . implode(', ', $indicateurColumns) . "\n\n";
        
        $stmt = $pdo->query("SELECT * FROM indicateurs");
        $indicateurs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($indicateurs) > 0) {
            foreach ($indicateurs as $indicateur) {
                echo "• Indicateur ID: {$indicateur['id']}\n";
                foreach ($indicateur as $key => $value) {
                    echo "  $key: $value\n";
                }
                echo "  ---\n";
            }
        } else {
            echo "Aucun indicateur trouvé.\n";
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 8. ACTIVITÉS RÉFORMES
    echo "📝 VOS ACTIVITÉS RÉFORMES :\n";
    echo "===========================\n";
    try {
        $stmt = $pdo->query("SELECT * FROM activites_reformes");
        $activites = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($activites) > 0) {
            foreach ($activites as $activite) {
                echo "• Activité ID: {$activite['id']}\n";
                foreach ($activite as $key => $value) {
                    echo "  $key: $value\n";
                }
                echo "  ---\n";
            }
        } else {
            echo "Aucune activité trouvée.\n";
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 9. SUIVI ACTIVITÉS
    echo "📈 SUIVI DE VOS ACTIVITÉS :\n";
    echo "===========================\n";
    try {
        $stmt = $pdo->query("SELECT * FROM suivi_activites");
        $suivis = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($suivis) > 0) {
            foreach ($suivis as $suivi) {
                echo "• Suivi ID: {$suivi['id']}\n";
                foreach ($suivi as $key => $value) {
                    echo "  $key: $value\n";
                }
                echo "  ---\n";
            }
        } else {
            echo "Aucun suivi trouvé.\n";
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 10. ASSOCIATIONS RÔLE-PERMISSION
    echo "🔗 ASSOCIATIONS RÔLE-PERMISSION :\n";
    echo "=================================\n";
    try {
        $stmt = $pdo->query("SELECT rp.*, r.nom as role_nom, p.nom as permission_nom 
                            FROM role_permission rp 
                            LEFT JOIN role r ON rp.role_id = r.id 
                            LEFT JOIN permission p ON rp.permission_id = p.id");
        $associations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($associations) > 0) {
            foreach ($associations as $assoc) {
                echo "• Association ID: {$assoc['id']}\n";
                echo "  Rôle: {$assoc['role_nom']} (ID: {$assoc['role_id']})\n";
                echo "  Permission: {$assoc['permission_nom']} (ID: {$assoc['permission_id']})\n";
                echo "  ---\n";
            }
        } else {
            echo "Aucune association trouvée.\n";
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 11. ASSOCIATIONS USER-ROLE
    echo "👤 ASSOCIATIONS UTILISATEUR-RÔLE :\n";
    echo "==================================\n";
    try {
        $stmt = $pdo->query("SELECT ur.*, u.email as user_email, r.nom as role_nom 
                            FROM user_role ur 
                            LEFT JOIN users u ON ur.user_id = u.id 
                            LEFT JOIN role r ON ur.role_id = r.id");
        $userRoles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($userRoles) > 0) {
            foreach ($userRoles as $ur) {
                echo "• Association ID: {$ur['id']}\n";
                echo "  Utilisateur: {$ur['user_email']} (ID: {$ur['user_id']})\n";
                echo "  Rôle: {$ur['role_nom']} (ID: {$ur['role_id']})\n";
                echo "  ---\n";
            }
        } else {
            echo "Aucune association trouvée.\n";
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 12. STATISTIQUES FINALES
    echo "📊 RÉSUMÉ DE VOS DONNÉES :\n";
    echo "=========================\n";
    
    $stats = [
        'users' => 'Utilisateurs',
        'role' => 'Rôles',
        'permission' => 'Permissions',
        'structure' => 'Structures',
        'type_reforme' => 'Types de réformes',
        'reformes' => 'Réformes',
        'indicateurs' => 'Indicateurs',
        'activites_reformes' => 'Activités réformes',
        'suivi_activites' => 'Suivis d\'activités',
        'role_permission' => 'Associations rôle-permission',
        'user_role' => 'Associations utilisateur-rôle',
        'user_sessions' => 'Sessions utilisateurs'
    ];
    
    foreach ($stats as $table => $label) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
            echo "• $label : $count\n";
        } catch (Exception $e) {
            echo "• $label : Erreur - " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erreur de connexion : " . $e->getMessage() . "\n";
}

echo "\n🎉 EXPORTATION TERMINÉE\n";
echo "=======================\n";

?>
