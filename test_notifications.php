<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Notification;
use App\Helpers\NotificationHelper;

echo "=== TEST DU SYSTÈME DE NOTIFICATIONS ===\n\n";

try {
    // Test 1: Création d'une notification simple
    echo "1. Test de création d'une notification simple...\n";
    
    // Simuler un utilisateur (ID 1)
    $userId = 1;
    
    $notification = Notification::create([
        'user_id' => $userId,
        'message' => 'Test de notification système',
        'url' => '/dashboard',
        'date_notification' => now(),
        'statut' => 'N'
    ]);
    
    if ($notification) {
        echo "   ✅ Notification créée avec succès (ID: {$notification->id})\n";
    } else {
        echo "   ❌ Échec de création de la notification\n";
    }
    
    // Test 2: Utilisation du helper
    echo "\n2. Test du NotificationHelper...\n";
    
    $helperResult = NotificationHelper::create('Test via helper', '/test', $userId);
    if ($helperResult) {
        echo "   ✅ Helper fonctionne correctement\n";
    } else {
        echo "   ❌ Problème avec le helper\n";
    }
    
    // Test 3: Notifications typées
    echo "\n3. Test des notifications typées...\n";
    
    NotificationHelper::success('Test de succès', null, $userId);
    NotificationHelper::error('Test d\'erreur', null, $userId);
    NotificationHelper::info('Test d\'information', null, $userId);
    NotificationHelper::warning('Test d\'avertissement', null, $userId);
    
    echo "   ✅ Notifications typées créées\n";
    
    // Test 4: Comptage des notifications
    echo "\n4. Test du comptage des notifications...\n";
    
    $count = Notification::where('user_id', $userId)->where('statut', 'N')->count();
    echo "   📊 Nombre de notifications non lues pour l'utilisateur {$userId}: {$count}\n";
    
    // Test 5: Marquage comme lu
    echo "\n5. Test du marquage comme lu...\n";
    
    $updated = Notification::where('user_id', $userId)
        ->where('statut', 'N')
        ->limit(2)
        ->update(['statut' => 'L']);
    
    echo "   ✅ {$updated} notifications marquées comme lues\n";
    
    // Test 6: Comptage final
    echo "\n6. Comptage final...\n";
    
    $finalCount = Notification::where('user_id', $userId)->where('statut', 'N')->count();
    echo "   📊 Notifications non lues restantes: {$finalCount}\n";
    
    // Test 7: Nettoyage
    echo "\n7. Nettoyage des notifications de test...\n";
    
    $deleted = Notification::where('user_id', $userId)
        ->where('message', 'LIKE', '%Test%')
        ->delete();
    
    echo "   🗑️ {$deleted} notifications de test supprimées\n";
    
    echo "\n=== TOUS LES TESTS TERMINÉS AVEC SUCCÈS ===\n";
    
} catch (Exception $e) {
    echo "\n❌ ERREUR LORS DES TESTS: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
