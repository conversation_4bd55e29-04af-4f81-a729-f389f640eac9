<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Afficher les notifications de l'utilisateur connecté
     */
    public function index()
    {
        $notifications = Notification::where('user_id', Auth::id())
            ->orderBy('date_notification', 'desc')
            ->limit(10)
            ->get();

        $notificationsNonLues = Notification::where('user_id', Auth::id())
            ->where('statut', 'N')
            ->count();

        return response()->json([
            'notifications' => $notifications,
            'nonLues' => $notificationsNonLues
        ]);
    }

    /**
     * Marquer une notification comme lue
     */
    public function marquerLue(Request $request, $id)
    {
        $notification = Notification::where('user_id', Auth::id())
            ->where('id', $id)
            ->first();

        if ($notification) {
            $notification->marquerCommeLue();
            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false], 404);
    }

    /**
     * Marquer toutes les notifications comme lues
     */
    public function marquerToutesLues()
    {
        try {
            $updated = Notification::where('user_id', Auth::id())
                ->where('statut', 'N')
                ->update(['statut' => 'L']);

            return response()->json([
                'success' => true,
                'message' => "Toutes les notifications ont été marquées comme lues",
                'updated_count' => $updated
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour des notifications'
            ], 500);
        }
    }

    /**
     * Compter les notifications non lues
     */
    public function countNonLues()
    {
        try {
            $count = Notification::where('user_id', Auth::id())
                ->where('statut', 'N')
                ->count();

            return response()->json([
                'success' => true,
                'count' => $count
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors du comptage des notifications'
            ], 500);
        }
    }

    /**
     * Créer une nouvelle notification (pour les administrateurs)
     */
    public function store(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:500',
            'url' => 'nullable|string|max:255',
            'user_ids' => 'nullable|array',
            'user_ids.*' => 'exists:users,id',
            'send_to_all' => 'nullable|boolean'
        ]);

        try {
            if ($request->send_to_all) {
                // Envoyer à tous les utilisateurs
                $users = \App\Models\User::all();
                foreach ($users as $user) {
                    Notification::create([
                        'user_id' => $user->id,
                        'message' => $request->message,
                        'url' => $request->url,
                        'date_notification' => now(),
                        'statut' => 'N'
                    ]);
                }
                $message = 'Notification envoyée à tous les utilisateurs';
            } elseif ($request->user_ids) {
                // Envoyer aux utilisateurs sélectionnés
                foreach ($request->user_ids as $userId) {
                    Notification::create([
                        'user_id' => $userId,
                        'message' => $request->message,
                        'url' => $request->url,
                        'date_notification' => now(),
                        'statut' => 'N'
                    ]);
                }
                $message = 'Notification envoyée aux utilisateurs sélectionnés';
            } else {
                // Envoyer à l'utilisateur connecté
                Notification::create([
                    'user_id' => Auth::id(),
                    'message' => $request->message,
                    'url' => $request->url,
                    'date_notification' => now(),
                    'statut' => 'N'
                ]);
                $message = 'Notification créée avec succès';
            }

            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la création de la notification'
            ], 500);
        }
    }

    /**
     * Supprimer une notification
     */
    public function destroy($id)
    {
        try {
            $notification = Notification::where('user_id', Auth::id())
                ->where('id', $id)
                ->first();

            if (!$notification) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification non trouvée'
                ], 404);
            }

            $notification->delete();

            return response()->json([
                'success' => true,
                'message' => 'Notification supprimée avec succès'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la suppression de la notification'
            ], 500);
        }
    }

    /**
     * Obtenir les statistiques des notifications pour l'admin
     */
    public function getStats()
    {
        try {
            $stats = [
                'total_notifications' => Notification::count(),
                'notifications_non_lues' => Notification::where('statut', 'N')->count(),
                'notifications_lues' => Notification::where('statut', 'L')->count(),
                'utilisateurs_avec_notifications' => Notification::distinct('user_id')->count(),
                'notifications_aujourd_hui' => Notification::whereDate('date_notification', today())->count(),
                'notifications_cette_semaine' => Notification::whereBetween('date_notification', [
                    now()->startOfWeek(),
                    now()->endOfWeek()
                ])->count()
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques'
            ], 500);
        }
    }


} 