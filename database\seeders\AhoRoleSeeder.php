<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Models\PermissionMenu;
use App\Services\PermissionService;

class AhoRoleSeeder extends Seeder
{
    /**
     * Créer le rôle 'aho' et assigner ses permissions
     */
    public function run(): void
    {
        $this->command->info('🔄 Création du rôle "aho" et assignation des permissions...');

        // 1. Créer le rôle 'aho' s'il n'existe pas
        $ahoRole = Role::firstOrCreate(
            ['role_name' => 'aho'],
            ['role_name' => 'aho']
        );

        $this->command->info("✅ Rôle 'aho' créé/trouvé avec ID: {$ahoRole->id}");

        // 2. Utiliser le PermissionService pour synchroniser les permissions
        $permissionService = new PermissionService();
        
        try {
            // Synchroniser toutes les permissions pour le rôle aho
            $permissionService->syncRolePermissions('aho');
            $this->command->info("✅ Permissions synchronisées pour le rôle 'aho'");

            // 3. Vérifier les permissions assignées
            $totalPermissions = $ahoRole->permissionMenus()->count();
            $this->command->info("📊 Total des permissions assignées au rôle 'aho': {$totalPermissions}");

            // 4. Afficher le détail des permissions par menu
            $menus = Menu::all();
            foreach ($menus as $menu) {
                $permissions = $ahoRole->permissionMenus()
                    ->where('menu_id', $menu->id)
                    ->with('permission')
                    ->get();
                
                if ($permissions->count() > 0) {
                    $permissionNames = $permissions->pluck('permission.lib')->toArray();
                    $this->command->info("  📋 {$menu->lib}: " . implode(', ', $permissionNames));
                }
            }

        } catch (\Exception $e) {
            $this->command->error("❌ Erreur lors de la synchronisation des permissions: " . $e->getMessage());
            
            // Fallback: assignation manuelle des permissions critiques
            $this->assignManualPermissions($ahoRole);
        }

        $this->command->info('🎉 Rôle "aho" configuré avec succès !');
    }

    /**
     * Assignation manuelle des permissions en cas d'échec de la synchronisation
     */
    private function assignManualPermissions(Role $ahoRole): void
    {
        $this->command->info('🔧 Assignation manuelle des permissions...');

        // Permissions critiques pour le rôle aho
        $criticalPermissions = [
            'Dashboard' => ['Lire'],
            'Gestion des réformes' => ['Créer', 'Lire', 'Modifier', 'Supprimer'],
            'Activités' => ['Créer', 'Lire', 'Modifier', 'Supprimer'],
            'Indicateurs' => ['Créer', 'Lire', 'Modifier', 'Supprimer'],
        ];

        foreach ($criticalPermissions as $menuName => $permissionNames) {
            $menu = Menu::where('lib', $menuName)->first();
            if (!$menu) {
                $this->command->warn("⚠️  Menu '{$menuName}' non trouvé");
                continue;
            }

            foreach ($permissionNames as $permissionName) {
                $permission = Permission::where('lib', $permissionName)->first();
                if (!$permission) {
                    $this->command->warn("⚠️  Permission '{$permissionName}' non trouvée");
                    continue;
                }

                // Créer l'association si elle n'existe pas
                $permissionMenu = PermissionMenu::firstOrCreate([
                    'role_id' => $ahoRole->id,
                    'menu_id' => $menu->id,
                    'permission_id' => $permission->id,
                ]);

                $this->command->info("✅ Permission '{$permissionName}' assignée au menu '{$menuName}'");
            }
        }
    }
}
