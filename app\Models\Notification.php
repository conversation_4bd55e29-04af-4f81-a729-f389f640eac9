<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;

    protected $table = 'notifications';

    protected $fillable = [
        'user_id',
        'message',
        'url',
        'date_notification',
        'statut'
    ];

    protected $casts = [
        'date_notification' => 'datetime',
    ];

    /**
     * Relation avec l'utilisateur
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope pour les notifications non lues
     */
    public function scopeNonLues($query)
    {
        return $query->where('statut', 'N');
    }

    /**
     * Scope pour les notifications lues
     */
    public function scopeLues($query)
    {
        return $query->where('statut', 'L');
    }

    /**
     * Marquer comme lue
     */
    public function marquerCommeLue()
    {
        $this->update(['statut' => 'L']);
    }

    /**
     * Formater la date pour l'affichage
     */
    public function getDateFormateeAttribute()
    {
        return $this->date_notification->format('d M');
    }
} 