<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Role;
use App\Models\Personne;
use App\Models\Menu;
use App\Models\Permission;
use App\Models\PermissionMenu;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class TestRoleController extends Controller
{
    /**
     * Test de la gestion des rôles et permissions
     */
    public function testRoleSystem()
    {
        try {
            DB::beginTransaction();

            // 1. Vérifier la structure existante
            $rolesCount = Role::count();
            $permissionsCount = Permission::count();
            $menusCount = Menu::count();
            $usersCount = User::count();

            // 2. Tester les méthodes du modèle User si un utilisateur existe
            $testResults = [];
            
            if ($usersCount > 0) {
                $user = User::with('roles')->first();
                
                $testResults['user_methods'] = [
                    'user_id' => $user->id,
                    'roles' => $user->roles->pluck('role_name')->toArray(),
                    'has_admin_role' => $user->hasRole('Administrateur'),
                    'has_any_role' => $user->hasAnyRole(['Administrateur', 'Gestionnaire']),
                    'accessible_menus_count' => $user->getAccessibleMenus()->count(),
                ];
            }

            // 3. Tester l'authentification et les permissions
            if (Auth::check()) {
                $currentUser = Auth::user();
                $testResults['current_user'] = [
                    'id' => $currentUser->id,
                    'roles' => $currentUser->roles->pluck('role_name')->toArray(),
                    'accessible_menus' => $currentUser->getAccessibleMenus()->pluck('libelle')->toArray(),
                ];
            }

            // 4. Statistiques générales
            $stats = [
                'roles_count' => $rolesCount,
                'permissions_count' => $permissionsCount,
                'menus_count' => $menusCount,
                'users_count' => $usersCount,
                'permission_menus_count' => PermissionMenu::count(),
                'role_permissions_count' => DB::table('role_permission')->count(),
                'user_roles_count' => DB::table('user_role')->count(),
            ];

            DB::rollback(); // Ne pas modifier les données

            return response()->json([
                'success' => true,
                'message' => 'Test du système de rôles et permissions',
                'stats' => $stats,
                'test_results' => $testResults,
                'timestamp' => now()->toDateTimeString()
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'message' => 'Erreur lors du test du système de rôles'
            ]);
        }
    }

    /**
     * Test de création d'utilisateurs avec rôles
     */
    public function createTestUsers()
    {
        try {
            DB::beginTransaction();

            $testUsers = [
                [
                    'nom' => 'Admin',
                    'prenom' => 'Test',
                    'fonction' => 'Administrateur Système',
                    'tel' => '9999999990',
                    'email' => '<EMAIL>',
                    'password' => 'admin123',
                    'role' => 'Administrateur'
                ],
                [
                    'nom' => 'Manager',
                    'prenom' => 'Test',
                    'fonction' => 'Gestionnaire',
                    'tel' => '9999999991',
                    'email' => '<EMAIL>',
                    'password' => 'manager123',
                    'role' => 'Gestionnaire'
                ],
                [
                    'nom' => 'User',
                    'prenom' => 'Test',
                    'fonction' => 'Utilisateur Simple',
                    'tel' => '9999999992',
                    'email' => '<EMAIL>',
                    'password' => 'user123',
                    'role' => 'Utilisateur'
                ]
            ];

            $createdUsers = [];

            foreach ($testUsers as $userData) {
                // Vérifier si l'utilisateur existe déjà
                $existingPersonne = Personne::where('email', $userData['email'])->first();
                if ($existingPersonne) {
                    continue; // Passer si l'utilisateur existe déjà
                }

                // Créer la personne
                $personne = Personne::create([
                    'nom' => $userData['nom'],
                    'prenom' => $userData['prenom'],
                    'fonction' => $userData['fonction'],
                    'tel' => $userData['tel'],
                    'email' => $userData['email'],
                ]);

                // Créer l'utilisateur
                $user = User::create([
                    'personne_id' => $personne->id,
                    'pwd' => Hash::make($userData['password']),
                    'status' => 1,
                ]);

                // Assigner le rôle
                $role = Role::where('role_name', $userData['role'])->first();
                if ($role) {
                    $user->roles()->attach($role->id);
                }

                $createdUsers[] = [
                    'user_id' => $user->id,
                    'email' => $userData['email'],
                    'role' => $userData['role'],
                    'personne_id' => $personne->id
                ];
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Utilisateurs de test créés avec succès',
                'created_users' => $createdUsers,
                'count' => count($createdUsers)
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erreur lors de la création des utilisateurs de test'
            ]);
        }
    }

    /**
     * Test des permissions d'accès aux menus
     */
    public function testMenuAccess()
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non connecté'
            ]);
        }

        $user = Auth::user();
        $menus = Menu::all();
        $accessResults = [];

        foreach ($menus as $menu) {
            $accessResults[] = [
                'menu_id' => $menu->id,
                'menu_name' => $menu->libelle,
                'menu_url' => $menu->url,
                'can_access' => $user->canAccessMenu($menu->id),
                'has_read_permission' => $user->hasPermission($menu->id, 'Lire'),
                'has_create_permission' => $user->hasPermission($menu->id, 'Créer'),
                'has_edit_permission' => $user->hasPermission($menu->id, 'Modifier'),
                'has_delete_permission' => $user->hasPermission($menu->id, 'Supprimer'),
            ];
        }

        return response()->json([
            'success' => true,
            'user_id' => $user->id,
            'user_roles' => $user->roles->pluck('role_name')->toArray(),
            'menu_access_results' => $accessResults,
            'accessible_menus' => $user->getAccessibleMenus()->pluck('libelle')->toArray()
        ]);
    }
}
