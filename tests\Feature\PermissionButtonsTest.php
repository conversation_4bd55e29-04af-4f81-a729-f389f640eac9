<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PermissionButtonsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer les rôles de base
        $this->artisan('db:seed', ['--class' => 'RoleSeeder']);
        $this->artisan('db:seed', ['--class' => 'MenuSeeder']);
        $this->artisan('db:seed', ['--class' => 'PermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'PermissionMenuSeeder']);
        $this->artisan('db:seed', ['--class' => 'RolePermissionMenuSeeder']);
        $this->artisan('db:seed', ['--class' => 'SyncAhoRoleSeeder']);
    }

    /** @test */
    public function aho_role_can_access_activities_page()
    {
        // Créer un utilisateur avec le rôle "aho"
        $ahoRole = Role::where('role_name', 'aho')->first();
        $user = User::factory()->create();
        $user->roles()->attach($ahoRole->id);

        // Tester l'accès à la page des activités
        $response = $this->actingAs($user)->get('/activites');
        
        $response->assertStatus(200);
        $response->assertDontSee('Accès refusé');
    }

    /** @test */
    public function aho_role_can_see_add_buttons_on_activities_page()
    {
        // Créer un utilisateur avec le rôle "aho"
        $ahoRole = Role::where('role_name', 'aho')->first();
        $user = User::factory()->create();
        $user->roles()->attach($ahoRole->id);

        // Tester que le bouton Ajouter est visible
        $response = $this->actingAs($user)->get('/activites');
        
        $response->assertStatus(200);
        $response->assertSee('Ajouter');
        $response->assertSee('fa-plus');
    }

    /** @test */
    public function aho_role_can_access_reforme_page()
    {
        // Créer un utilisateur avec le rôle "aho"
        $ahoRole = Role::where('role_name', 'aho')->first();
        $user = User::factory()->create();
        $user->roles()->attach($ahoRole->id);

        // Tester l'accès à la page des réformes
        $response = $this->actingAs($user)->get('/reforme');
        
        $response->assertStatus(200);
        $response->assertDontSee('Accès refusé');
    }

    /** @test */
    public function utilisateur_role_cannot_see_add_buttons()
    {
        // Créer un utilisateur avec le rôle "Utilisateur" (lecture seule)
        $utilisateurRole = Role::where('role_name', 'Utilisateur')->first();
        $user = User::factory()->create();
        $user->roles()->attach($utilisateurRole->id);

        // Tester que le bouton Ajouter n'est PAS visible
        $response = $this->actingAs($user)->get('/activites');
        
        $response->assertStatus(200);
        $response->assertSee('Vous n\'avez pas la permission de créer');
    }

    /** @test */
    public function middleware_redirects_work_correctly()
    {
        // Créer un utilisateur avec le rôle "aho"
        $ahoRole = Role::where('role_name', 'aho')->first();
        $user = User::factory()->create();
        $user->roles()->attach($ahoRole->id);

        // Tester que l'utilisateur n'est PAS redirigé vers le dashboard
        $response = $this->actingAs($user)->get('/activites');
        
        $response->assertStatus(200);
        $response->assertViewIs('activitesreformes');
    }
}
