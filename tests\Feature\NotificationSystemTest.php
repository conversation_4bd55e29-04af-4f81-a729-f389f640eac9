<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Notification;
use App\Helpers\NotificationHelper;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class NotificationSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer un utilisateur normal
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);

        // Créer un administrateur
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
    }

    /** @test */
    public function test_user_can_view_notifications()
    {
        // Créer quelques notifications pour l'utilisateur
        Notification::create([
            'user_id' => $this->user->id,
            'message' => 'Test notification 1',
            'date_notification' => now(),
            'statut' => 'N'
        ]);

        Notification::create([
            'user_id' => $this->user->id,
            'message' => 'Test notification 2',
            'date_notification' => now(),
            'statut' => 'L'
        ]);

        $response = $this->actingAs($this->user)
                         ->get(route('notifications.index'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'notifications' => [
                '*' => [
                    'id',
                    'message',
                    'statut',
                    'date_formatee'
                ]
            ],
            'nonLues'
        ]);
    }

    /** @test */
    public function test_user_can_mark_notification_as_read()
    {
        $notification = Notification::create([
            'user_id' => $this->user->id,
            'message' => 'Test notification',
            'date_notification' => now(),
            'statut' => 'N'
        ]);

        $response = $this->actingAs($this->user)
                         ->post(route('notifications.marquer-lue', $notification->id));

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('notifications', [
            'id' => $notification->id,
            'statut' => 'L'
        ]);
    }

    /** @test */
    public function test_user_can_mark_all_notifications_as_read()
    {
        // Créer plusieurs notifications non lues
        Notification::create([
            'user_id' => $this->user->id,
            'message' => 'Test notification 1',
            'date_notification' => now(),
            'statut' => 'N'
        ]);

        Notification::create([
            'user_id' => $this->user->id,
            'message' => 'Test notification 2',
            'date_notification' => now(),
            'statut' => 'N'
        ]);

        $response = $this->actingAs($this->user)
                         ->post(route('notifications.marquer-toutes-lues'));

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseMissing('notifications', [
            'user_id' => $this->user->id,
            'statut' => 'N'
        ]);
    }

    /** @test */
    public function test_user_can_get_unread_count()
    {
        // Créer des notifications (2 non lues, 1 lue)
        Notification::create([
            'user_id' => $this->user->id,
            'message' => 'Test notification 1',
            'date_notification' => now(),
            'statut' => 'N'
        ]);

        Notification::create([
            'user_id' => $this->user->id,
            'message' => 'Test notification 2',
            'date_notification' => now(),
            'statut' => 'N'
        ]);

        Notification::create([
            'user_id' => $this->user->id,
            'message' => 'Test notification 3',
            'date_notification' => now(),
            'statut' => 'L'
        ]);

        $response = $this->actingAs($this->user)
                         ->get(route('notifications.count-non-lues'));

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'count' => 2
        ]);
    }

    /** @test */
    public function test_admin_can_create_notification_for_all_users()
    {
        $response = $this->actingAs($this->admin)
                         ->post(route('notifications.store'), [
                             'message' => 'Notification pour tous',
                             'send_to_all' => true
                         ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->user->id,
            'message' => 'Notification pour tous'
        ]);

        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->admin->id,
            'message' => 'Notification pour tous'
        ]);
    }

    /** @test */
    public function test_admin_can_create_notification_for_specific_users()
    {
        $response = $this->actingAs($this->admin)
                         ->post(route('notifications.store'), [
                             'message' => 'Notification spécifique',
                             'user_ids' => [$this->user->id]
                         ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->user->id,
            'message' => 'Notification spécifique'
        ]);

        $this->assertDatabaseMissing('notifications', [
            'user_id' => $this->admin->id,
            'message' => 'Notification spécifique'
        ]);
    }

    /** @test */
    public function test_admin_can_get_notification_stats()
    {
        // Créer quelques notifications
        Notification::create([
            'user_id' => $this->user->id,
            'message' => 'Test notification 1',
            'date_notification' => now(),
            'statut' => 'N'
        ]);

        Notification::create([
            'user_id' => $this->user->id,
            'message' => 'Test notification 2',
            'date_notification' => now(),
            'statut' => 'L'
        ]);

        $response = $this->actingAs($this->admin)
                         ->get(route('notifications.stats'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'stats' => [
                'total_notifications',
                'notifications_non_lues',
                'notifications_lues',
                'utilisateurs_avec_notifications',
                'notifications_aujourd_hui',
                'notifications_cette_semaine'
            ]
        ]);
    }

    /** @test */
    public function test_notification_helper_creates_notification()
    {
        $result = NotificationHelper::create('Test message', '/test-url', $this->user->id);

        $this->assertInstanceOf(Notification::class, $result);
        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->user->id,
            'message' => 'Test message',
            'url' => '/test-url',
            'statut' => 'N'
        ]);
    }

    /** @test */
    public function test_notification_helper_creates_for_all_users()
    {
        $count = NotificationHelper::createForAll('Message pour tous');

        $this->assertEquals(2, $count); // 2 utilisateurs créés dans setUp
        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->user->id,
            'message' => 'Message pour tous'
        ]);
        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->admin->id,
            'message' => 'Message pour tous'
        ]);
    }

    /** @test */
    public function test_notification_helper_creates_typed_notifications()
    {
        NotificationHelper::success('Succès', null, $this->user->id);
        NotificationHelper::error('Erreur', null, $this->user->id);
        NotificationHelper::info('Info', null, $this->user->id);
        NotificationHelper::warning('Attention', null, $this->user->id);

        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->user->id,
            'message' => '✅ Succès'
        ]);
        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->user->id,
            'message' => '❌ Erreur'
        ]);
        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->user->id,
            'message' => 'ℹ️ Info'
        ]);
        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->user->id,
            'message' => '⚠️ Attention'
        ]);
    }

    /** @test */
    public function test_user_can_delete_own_notification()
    {
        $notification = Notification::create([
            'user_id' => $this->user->id,
            'message' => 'Test notification',
            'date_notification' => now(),
            'statut' => 'N'
        ]);

        $response = $this->actingAs($this->user)
                         ->delete(route('notifications.destroy', $notification->id));

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseMissing('notifications', [
            'id' => $notification->id
        ]);
    }

    /** @test */
    public function test_user_cannot_delete_other_user_notification()
    {
        $notification = Notification::create([
            'user_id' => $this->admin->id,
            'message' => 'Admin notification',
            'date_notification' => now(),
            'statut' => 'N'
        ]);

        $response = $this->actingAs($this->user)
                         ->delete(route('notifications.destroy', $notification->id));

        $response->assertStatus(404);
        $this->assertDatabaseHas('notifications', [
            'id' => $notification->id
        ]);
    }
}
