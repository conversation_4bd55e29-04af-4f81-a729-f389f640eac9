@extends('layouts.app')

@section('title', 'Test du Système de Permissions')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-shield"></i> Test du Système de Permissions
                    </h3>
                </div>
                <div class="panel-body">
                    
                    <!-- Informations utilisateur -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="panel panel-info">
                                <div class="panel-heading">
                                    <h4>Utilisateur Connecté</h4>
                                </div>
                                <div class="panel-body">
                                    <p><strong>ID:</strong> {{ $userInfo['id'] }}</p>
                                    <p><strong>Email:</strong> {{ $userInfo['email'] }}</p>
                                    <p><strong>Rôles:</strong> 
                                        @if(empty($userInfo['roles']))
                                            <span class="label label-warning">Aucun rôle</span>
                                        @else
                                            @foreach($userInfo['roles'] as $role)
                                                <span class="label label-primary">{{ $role }}</span>
                                            @endforeach
                                        @endif
                                    </p>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <span class="label {{ $userInfo['has_admin_role'] ? 'label-success' : 'label-default' }}">
                                                Admin: {{ $userInfo['has_admin_role'] ? 'Oui' : 'Non' }}
                                            </span>
                                        </div>
                                        <div class="col-md-4">
                                            <span class="label {{ $userInfo['has_gestionnaire_role'] ? 'label-success' : 'label-default' }}">
                                                Gestionnaire: {{ $userInfo['has_gestionnaire_role'] ? 'Oui' : 'Non' }}
                                            </span>
                                        </div>
                                        <div class="col-md-4">
                                            <span class="label {{ $userInfo['has_utilisateur_role'] ? 'label-success' : 'label-default' }}">
                                                Utilisateur: {{ $userInfo['has_utilisateur_role'] ? 'Oui' : 'Non' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="panel panel-success">
                                <div class="panel-heading">
                                    <h4>Statistiques Système</h4>
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Utilisateurs:</strong> {{ $stats['total_users'] }}</p>
                                            <p><strong>Rôles:</strong> {{ $stats['total_roles'] }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Menus:</strong> {{ $stats['total_menus'] }}</p>
                                            <p><strong>Permissions:</strong> {{ $stats['total_permissions'] }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test d'accès aux URLs -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="panel panel-warning">
                                <div class="panel-heading">
                                    <h4>Test d'Accès aux URLs</h4>
                                </div>
                                <div class="panel-body">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>URL</th>
                                                <th>Accès</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($urlTests as $url => $hasAccess)
                                            <tr>
                                                <td><code>{{ $url }}</code></td>
                                                <td>
                                                    @if($hasAccess)
                                                        <span class="label label-success">
                                                            <i class="fa fa-check"></i> Autorisé
                                                        </span>
                                                    @else
                                                        <span class="label label-danger">
                                                            <i class="fa fa-times"></i> Refusé
                                                        </span>
                                                    @endif
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="panel panel-primary">
                                <div class="panel-heading">
                                    <h4>Test des Permissions CRUD</h4>
                                </div>
                                <div class="panel-body">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Action</th>
                                                <th>Permission</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($crudTests as $action => $hasPermission)
                                            <tr>
                                                <td>{{ str_replace('_', ' - ', ucfirst($action)) }}</td>
                                                <td>
                                                    @if($hasPermission)
                                                        <span class="label label-success">
                                                            <i class="fa fa-check"></i> Autorisé
                                                        </span>
                                                    @else
                                                        <span class="label label-danger">
                                                            <i class="fa fa-times"></i> Refusé
                                                        </span>
                                                    @endif
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test des directives Blade -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="panel panel-info">
                                <div class="panel-heading">
                                    <h4>Test des Directives Blade</h4>
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <h5>Créer Réforme:</h5>
                                            @canCreateUrl('/reforme')
                                                <button class="btn btn-success btn-sm">
                                                    <i class="fa fa-plus"></i> Visible (Autorisé)
                                                </button>
                                            @else
                                                <button class="btn btn-default btn-sm" disabled>
                                                    <i class="fa fa-ban"></i> Masqué (Refusé)
                                                </button>
                                            @endcanCreateUrl
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <h5>Modifier Activité:</h5>
                                            @canUpdateUrl('/activites')
                                                <button class="btn btn-warning btn-sm">
                                                    <i class="fa fa-edit"></i> Visible (Autorisé)
                                                </button>
                                            @else
                                                <button class="btn btn-default btn-sm" disabled>
                                                    <i class="fa fa-ban"></i> Masqué (Refusé)
                                                </button>
                                            @endcanUpdateUrl
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <h5>Supprimer Réforme:</h5>
                                            @canDeleteUrl('/reforme')
                                                <button class="btn btn-danger btn-sm">
                                                    <i class="fa fa-trash"></i> Visible (Autorisé)
                                                </button>
                                            @else
                                                <button class="btn btn-default btn-sm" disabled>
                                                    <i class="fa fa-ban"></i> Masqué (Refusé)
                                                </button>
                                            @endcanDeleteUrl
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <h5>Accès Admin:</h5>
                                            @canAccessUrl('/role')
                                                <button class="btn btn-info btn-sm">
                                                    <i class="fa fa-users"></i> Visible (Admin)
                                                </button>
                                            @else
                                                <button class="btn btn-default btn-sm" disabled>
                                                    <i class="fa fa-ban"></i> Masqué (Non-Admin)
                                                </button>
                                            @endcanAccessUrl
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions de test -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4>Actions de Test</h4>
                                </div>
                                <div class="panel-body">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-primary" onclick="checkSystemHealth()">
                                            <i class="fa fa-heartbeat"></i> Vérifier l'État du Système
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="generateDebugReport()">
                                            <i class="fa fa-bug"></i> Rapport de Débogage
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="testCustomPermission()">
                                            <i class="fa fa-test"></i> Test Personnalisé
                                        </button>
                                        <a href="{{ route('admin.permissions.index') }}" class="btn btn-success">
                                            <i class="fa fa-cogs"></i> Gestion Avancée
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Zone de résultats -->
                    <div class="row" id="test-results" style="display: none;">
                        <div class="col-md-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4>Résultats du Test</h4>
                                </div>
                                <div class="panel-body">
                                    <pre id="test-output"></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
function checkSystemHealth() {
    fetch('{{ route("test.permissions.system.check") }}')
        .then(response => response.json())
        .then(data => {
            document.getElementById('test-results').style.display = 'block';
            document.getElementById('test-output').textContent = JSON.stringify(data, null, 2);
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la vérification du système');
        });
}

function generateDebugReport() {
    fetch('{{ route("test.permissions.debug.report") }}')
        .then(response => response.json())
        .then(data => {
            document.getElementById('test-results').style.display = 'block';
            document.getElementById('test-output').textContent = JSON.stringify(data, null, 2);
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la génération du rapport');
        });
}

function testCustomPermission() {
    const url = prompt('URL à tester (ex: /reforme):');
    const permission = prompt('Permission à tester (ex: Créer):');
    
    if (url && permission) {
        fetch('{{ route("test.permissions.test") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                url: url,
                permission: permission
            })
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('test-results').style.display = 'block';
            document.getElementById('test-output').textContent = JSON.stringify(data, null, 2);
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du test personnalisé');
        });
    }
}
</script>
@endsection
