<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Models\User;
use App\Services\PermissionService;

class PermissionManagementController extends Controller
{
    protected $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->middleware('auth');
        $this->middleware('role.permission:role:Administrateur');
        $this->permissionService = $permissionService;
    }

    /**
     * Afficher la page de gestion des permissions
     */
    public function index()
    {
        $roles = Role::with(['users', 'permissionMenus.menu', 'permissionMenus.permission'])
                    ->where('is_active', true)
                    ->orderBy('role_name')
                    ->get();

        $menus = Menu::where('is_active', true)
                    ->orderBy('ordre')
                    ->get();

        $permissions = Permission::where('is_active', true)
                                ->orderBy('permission_name')
                                ->get();

        $users = User::with('roles')
                    ->orderBy('email')
                    ->get();

        return view('admin.permissions.index', compact('roles', 'menus', 'permissions', 'users'));
    }

    /**
     * Afficher le rapport de permissions pour un utilisateur
     */
    public function userReport($userId)
    {
        $user = User::findOrFail($userId);
        $report = $this->permissionService->generateUserPermissionReport($user);

        return response()->json($report);
    }

    /**
     * Synchroniser les permissions d'un rôle
     */
    public function syncRolePermissions($roleId)
    {
        try {
            $role = Role::findOrFail($roleId);
            
            DB::beginTransaction();
            
            $this->permissionService->assignPermissionsToRole($role);
            
            DB::commit();

            Log::info('Permissions synchronisées pour le rôle', [
                'role_id' => $role->id,
                'role_name' => $role->role_name,
                'admin_user' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => "Permissions synchronisées pour le rôle {$role->role_name}"
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            Log::error('Erreur lors de la synchronisation des permissions', [
                'role_id' => $roleId,
                'error' => $e->getMessage(),
                'admin_user' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la synchronisation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Synchroniser toutes les permissions
     */
    public function syncAllPermissions()
    {
        try {
            DB::beginTransaction();
            
            $this->permissionService->syncAllRolePermissions();
            
            DB::commit();

            Log::info('Toutes les permissions ont été synchronisées', [
                'admin_user' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Toutes les permissions ont été synchronisées avec succès'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            Log::error('Erreur lors de la synchronisation globale', [
                'error' => $e->getMessage(),
                'admin_user' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la synchronisation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Tester les permissions d'un utilisateur
     */
    public function testUserPermissions(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'menu_url' => 'required|string',
            'permission' => 'required|string'
        ]);

        $user = User::findOrFail($request->user_id);
        $hasPermission = $this->permissionService->userHasPermissionForUrl(
            $user, 
            $request->menu_url, 
            $request->permission
        );

        return response()->json([
            'user_id' => $user->id,
            'user_email' => $user->email,
            'menu_url' => $request->menu_url,
            'permission' => $request->permission,
            'has_permission' => $hasPermission,
            'user_roles' => $user->roles->pluck('role_name')->toArray()
        ]);
    }

    /**
     * Obtenir les statistiques du système de permissions
     */
    public function getStats()
    {
        $stats = [
            'total_users' => User::count(),
            'total_roles' => Role::where('is_active', true)->count(),
            'total_menus' => Menu::where('is_active', true)->count(),
            'total_permissions' => Permission::where('is_active', true)->count(),
            'users_without_roles' => User::whereDoesntHave('roles')->count(),
            'roles_without_users' => Role::whereDoesntHave('users')->where('is_active', true)->count(),
            'inactive_menus' => Menu::where('is_active', false)->count(),
        ];

        // Statistiques par rôle
        $roleStats = Role::where('is_active', true)
                        ->withCount('users')
                        ->get()
                        ->map(function ($role) {
                            return [
                                'role_name' => $role->role_name,
                                'user_count' => $role->users_count,
                                'permission_count' => $role->permissionMenus()->count()
                            ];
                        });

        $stats['role_breakdown'] = $roleStats;

        return response()->json($stats);
    }

    /**
     * Diagnostiquer les problèmes de permissions
     */
    public function diagnose()
    {
        $issues = [];

        // Vérifier les utilisateurs sans rôles
        $usersWithoutRoles = User::whereDoesntHave('roles')->get();
        if ($usersWithoutRoles->count() > 0) {
            $issues[] = [
                'type' => 'warning',
                'title' => 'Utilisateurs sans rôles',
                'description' => $usersWithoutRoles->count() . ' utilisateur(s) n\'ont aucun rôle assigné',
                'users' => $usersWithoutRoles->pluck('email')->toArray()
            ];
        }

        // Vérifier les rôles sans permissions
        $rolesWithoutPermissions = Role::whereDoesntHave('permissionMenus')
                                      ->where('is_active', true)
                                      ->get();
        if ($rolesWithoutPermissions->count() > 0) {
            $issues[] = [
                'type' => 'error',
                'title' => 'Rôles sans permissions',
                'description' => $rolesWithoutPermissions->count() . ' rôle(s) n\'ont aucune permission assignée',
                'roles' => $rolesWithoutPermissions->pluck('role_name')->toArray()
            ];
        }

        // Vérifier les menus sans associations
        $menusWithoutPermissions = Menu::whereDoesntHave('permissionMenus')
                                      ->where('is_active', true)
                                      ->get();
        if ($menusWithoutPermissions->count() > 0) {
            $issues[] = [
                'type' => 'warning',
                'title' => 'Menus sans permissions',
                'description' => $menusWithoutPermissions->count() . ' menu(s) n\'ont aucune association avec les permissions',
                'menus' => $menusWithoutPermissions->pluck('libelle')->toArray()
            ];
        }

        // Vérifier la cohérence avec la configuration
        $configRoles = array_keys(config('permissions.default_role_permissions', []));
        $dbRoles = Role::where('is_active', true)->pluck('role_name')->toArray();
        
        $missingRoles = array_diff($configRoles, $dbRoles);
        if (!empty($missingRoles)) {
            $issues[] = [
                'type' => 'error',
                'title' => 'Rôles manquants en base',
                'description' => 'Des rôles sont définis dans la configuration mais absents de la base de données',
                'roles' => $missingRoles
            ];
        }

        return response()->json([
            'total_issues' => count($issues),
            'issues' => $issues,
            'system_health' => count($issues) === 0 ? 'healthy' : (count($issues) < 3 ? 'warning' : 'critical')
        ]);
    }

    /**
     * Corriger automatiquement les problèmes détectés
     */
    public function autoFix()
    {
        try {
            DB::beginTransaction();
            
            $fixes = [];

            // Corriger les rôles sans permissions
            $rolesWithoutPermissions = Role::whereDoesntHave('permissionMenus')
                                          ->where('is_active', true)
                                          ->get();
            
            foreach ($rolesWithoutPermissions as $role) {
                $this->permissionService->assignPermissionsToRole($role);
                $fixes[] = "Permissions assignées au rôle: {$role->role_name}";
            }

            // Créer les associations permission-menu manquantes
            $this->permissionService->createPermissionMenuAssociations();
            $fixes[] = "Associations permission-menu vérifiées et créées si nécessaire";

            DB::commit();

            Log::info('Correction automatique des permissions effectuée', [
                'fixes' => $fixes,
                'admin_user' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Corrections appliquées avec succès',
                'fixes' => $fixes
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            Log::error('Erreur lors de la correction automatique', [
                'error' => $e->getMessage(),
                'admin_user' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la correction: ' . $e->getMessage()
            ], 500);
        }
    }
}
