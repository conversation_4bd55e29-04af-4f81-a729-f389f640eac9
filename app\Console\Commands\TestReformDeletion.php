<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Reforme;
use App\Models\Activitesreformes;
use Illuminate\Support\Facades\DB;

class TestReformDeletion extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:reform-deletion {id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test reform deletion with proper foreign key handling';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $id = $this->argument('id');

        $this->info("Testing deletion of reform ID: $id");
        $this->info("==========================================");

        try {
            // Check if reform exists
            $reforme = Reforme::find($id);
            if (!$reforme) {
                $this->error("Reform with ID $id not found!");
                return 1;
            }

            $this->info("Reform found: " . $reforme->titre);

            // Check related records
            $activitesCount = Activitesreformes::where('reforme_id', $id)->count();
            $this->info("Related activities: $activitesCount");

            $reformeIndicateursCount = DB::table('reformes_indicateurs')->where('reforme_id', $id)->count();
            $this->info("Related reform-indicators: $reformeIndicateursCount");

            $reformeStructureCount = DB::table('reformes_structure')->where('reforme_id', $id)->count();
            $this->info("Related reform-structures: $reformeStructureCount");

            $suiviActivitesCount = DB::table('suivi_activites')
                ->whereIn('activite_reforme_id', function($query) use ($id) {
                    $query->select('id')
                          ->from('activites_reformes')
                          ->where('reforme_id', $id);
                })
                ->count();
            $this->info("Related activity tracking records: $suiviActivitesCount");

            $totalRelated = $activitesCount + $reformeIndicateursCount + $reformeStructureCount + $suiviActivitesCount;
            $this->info("Total related records: $totalRelated");

            if ($totalRelated > 0) {
                $this->warn("This reform has $totalRelated related records that need to be deleted first.");

                if ($this->confirm('Do you want to proceed with the deletion?')) {
                    // Test the deletion logic from the controller
                    $this->info("Starting deletion process...");

                    // Simulate the controller's destroy method logic
                    $this->testDeletion($id);

                    $this->info("Deletion test completed successfully!");
                } else {
                    $this->info("Deletion cancelled.");
                }
            } else {
                $this->info("No related records found. Reform can be deleted safely.");
            }

        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }

    private function testDeletion($id)
    {
        DB::beginTransaction();

        try {
            $reforme = Reforme::findOrFail($id);

            // 1. Delete evolution indicators if they exist
            if (class_exists('App\Models\EvolutionIndicateur')) {
                $evolutionCount = \App\Models\EvolutionIndicateur::whereHas('reformeIndicateur', function($query) use ($id) {
                    $query->where('reforme_id', $id);
                })->count();

                if ($evolutionCount > 0) {
                    $this->info("Deleting $evolutionCount evolution indicators...");
                    \App\Models\EvolutionIndicateur::whereHas('reformeIndicateur', function($query) use ($id) {
                        $query->where('reforme_id', $id);
                    })->delete();
                }
            }

            // 2. Delete reform-indicator associations
            $reformeIndicateursCount = DB::table('reformes_indicateurs')->where('reforme_id', $id)->count();
            if ($reformeIndicateursCount > 0) {
                $this->info("Deleting $reformeIndicateursCount reform-indicator associations...");
                DB::table('reformes_indicateurs')->where('reforme_id', $id)->delete();
            }

            // 3. Delete activity tracking records
            $suiviCount = DB::table('suivi_activites')
                ->whereIn('activite_reforme_id', function($query) use ($id) {
                    $query->select('id')
                          ->from('activites_reformes')
                          ->where('reforme_id', $id);
                })
                ->count();

            if ($suiviCount > 0) {
                $this->info("Deleting $suiviCount activity tracking records...");
                DB::table('suivi_activites')
                    ->whereIn('activite_reforme_id', function($query) use ($id) {
                        $query->select('id')
                              ->from('activites_reformes')
                              ->where('reforme_id', $id);
                    })
                    ->delete();
            }

            // 4. Delete activities (sub-activities first)
            $activites = Activitesreformes::where('reforme_id', $id)->get();

            $sousActivitesCount = $activites->whereNotNull('parent')->count();
            if ($sousActivitesCount > 0) {
                $this->info("Deleting $sousActivitesCount sub-activities...");
                foreach ($activites->whereNotNull('parent') as $sousActivite) {
                    $sousActivite->delete();
                }
            }

            $activitesCount = $activites->whereNull('parent')->count();
            if ($activitesCount > 0) {
                $this->info("Deleting $activitesCount main activities...");
                foreach ($activites->whereNull('parent') as $activite) {
                    $activite->delete();
                }
            }

            // 5. Delete reform-structure associations
            $structureCount = DB::table('reformes_structure')->where('reforme_id', $id)->count();
            if ($structureCount > 0) {
                $this->info("Deleting $structureCount reform-structure associations...");
                DB::table('reformes_structure')->where('reforme_id', $id)->delete();
            }

            // 6. Finally, delete the reform itself
            $this->info("Deleting the reform...");
            $reforme->delete();

            // Rollback for testing purposes (we don't want to actually delete)
            DB::rollBack();
            $this->info("Transaction rolled back - this was just a test!");

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
