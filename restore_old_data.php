<?php

/**
 * Script de restauration des anciennes données
 * Migre les données de database_new.sqlite vers database.sqlite
 */

echo "🔄 RESTAURATION DES ANCIENNES DONNÉES\n";
echo "=====================================\n\n";

try {
    // Connexions aux deux bases de données
    $oldDb = new PDO('sqlite:database/database_new.sqlite');
    $currentDb = new PDO('sqlite:database/database.sqlite');
    
    $oldDb->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $currentDb->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connexions aux bases de données établies\n\n";
    
    // Vérifier les tailles des bases
    $oldSize = filesize('database/database_new.sqlite');
    $currentSize = filesize('database/database.sqlite');
    
    echo "📊 Tailles des bases :\n";
    echo "• Ancienne base (database_new.sqlite) : " . number_format($oldSize / 1024, 2) . " KB\n";
    echo "• Base actuelle (database.sqlite) : " . number_format($currentSize / 1024, 2) . " KB\n\n";
    
    // Tables à migrer dans l'ordre (pour respecter les contraintes)
    $tablesToMigrate = [
        'personne',
        'users', 
        'role',
        'permission',
        'user_role',
        'role_permission',
        'structure',
        'type_reforme',
        'reformes',
        'indicateurs',
        'activites_reformes',
        'suivi_activites',
        'reformes_structure',
        'reformes_indicateurs',
        'evolution_indicateurs',
        'menu',
        'permission_menu',
        'user_sessions',
        'notifications'
    ];
    
    $currentDb->exec('BEGIN TRANSACTION');
    
    foreach ($tablesToMigrate as $table) {
        echo "🔄 Migration de la table '$table'...\n";
        
        try {
            // Vérifier si la table existe dans l'ancienne base
            $checkTable = $oldDb->query("SELECT name FROM sqlite_master WHERE type='table' AND name='$table'");
            if (!$checkTable->fetch()) {
                echo "   ⚠️  Table '$table' n'existe pas dans l'ancienne base - ignorée\n";
                continue;
            }
            
            // Compter les enregistrements dans l'ancienne base
            $countOld = $oldDb->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            if ($countOld == 0) {
                echo "   ℹ️  Table '$table' vide dans l'ancienne base - ignorée\n";
                continue;
            }
            
            // Vider la table actuelle
            $currentDb->exec("DELETE FROM $table");
            echo "   🗑️  Table '$table' vidée dans la base actuelle\n";
            
            // Récupérer la structure de la table
            $columns = $oldDb->query("PRAGMA table_info($table)")->fetchAll(PDO::FETCH_ASSOC);
            $columnNames = array_column($columns, 'name');
            $columnList = implode(', ', $columnNames);
            $placeholders = ':' . implode(', :', $columnNames);
            
            // Préparer la requête d'insertion
            $insertSql = "INSERT INTO $table ($columnList) VALUES ($placeholders)";
            $insertStmt = $currentDb->prepare($insertSql);
            
            // Récupérer et insérer les données
            $selectStmt = $oldDb->query("SELECT * FROM $table");
            $inserted = 0;
            
            while ($row = $selectStmt->fetch(PDO::FETCH_ASSOC)) {
                $insertStmt->execute($row);
                $inserted++;
            }
            
            echo "   ✅ $inserted enregistrements migrés pour '$table'\n";
            
        } catch (Exception $e) {
            echo "   ❌ Erreur lors de la migration de '$table' : " . $e->getMessage() . "\n";
            // Continuer avec les autres tables
        }
    }
    
    $currentDb->exec('COMMIT');
    echo "\n🎉 MIGRATION TERMINÉE AVEC SUCCÈS !\n\n";
    
    // Vérification finale
    echo "📊 VÉRIFICATION FINALE :\n";
    echo "========================\n";
    
    $finalStats = [
        'personne' => $currentDb->query("SELECT COUNT(*) FROM personne")->fetchColumn(),
        'users' => $currentDb->query("SELECT COUNT(*) FROM users")->fetchColumn(),
        'reformes' => $currentDb->query("SELECT COUNT(*) FROM reformes")->fetchColumn(),
        'activites_reformes' => $currentDb->query("SELECT COUNT(*) FROM activites_reformes")->fetchColumn(),
        'indicateurs' => $currentDb->query("SELECT COUNT(*) FROM indicateurs")->fetchColumn(),
        'user_sessions' => $currentDb->query("SELECT COUNT(*) FROM user_sessions")->fetchColumn(),
    ];
    
    foreach ($finalStats as $table => $count) {
        echo "• $table : $count enregistrements\n";
    }
    
    // Afficher quelques données pour vérification
    echo "\n👥 UTILISATEURS RESTAURÉS :\n";
    echo "===========================\n";
    $users = $currentDb->query("
        SELECT u.id, p.nom, p.prenom, p.email, u.status 
        FROM users u 
        LEFT JOIN personne p ON u.personne_id = p.id 
        LIMIT 5
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($users as $user) {
        $status = $user['status'] ? '✅ Actif' : '❌ Inactif';
        echo "• ID {$user['id']}: {$user['nom']} {$user['prenom']} ({$user['email']}) - $status\n";
    }
    
    echo "\n🔄 RÉFORMES RESTAURÉES :\n";
    echo "=======================\n";
    $reformes = $currentDb->query("SELECT id, titre, statut, budget FROM reformes LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($reformes as $reforme) {
        $statutLabel = $reforme['statut'] == 'C' ? 'Complétée' : 'En cours';
        echo "• ID {$reforme['id']}: {$reforme['titre']} - $statutLabel - Budget: " . number_format($reforme['budget']) . "€\n";
    }
    
    echo "\n✅ RESTAURATION COMPLÈTE RÉUSSIE !\n";
    echo "Toutes vos anciennes données ont été restaurées dans la base actuelle.\n";
    
} catch (Exception $e) {
    if (isset($currentDb)) {
        $currentDb->exec('ROLLBACK');
    }
    echo "❌ ERREUR CRITIQUE : " . $e->getMessage() . "\n";
    echo "La transaction a été annulée pour préserver l'intégrité des données.\n";
}

echo "\n🎯 PROCHAINES ÉTAPES :\n";
echo "======================\n";
echo "1. Redémarrez votre serveur Laravel\n";
echo "2. Connectez-vous à votre application\n";
echo "3. Vérifiez que toutes vos données sont présentes\n";
echo "4. Testez les fonctionnalités principales\n";

?>
