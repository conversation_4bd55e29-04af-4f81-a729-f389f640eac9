@extends('layout.app')

@section('title', 'Test des Permissions Génériques')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-shield"></i> Test du Système de Permissions Génériques
                    </h3>
                    <div class="pull-right">
                        <a href="{{ route('admin.role.permissions') }}" class="btn btn-primary btn-xs">
                            <i class="fa fa-cog"></i> Gérer les Permissions
                        </a>
                    </div>
                </div>
                <div class="panel-body">
                    @if(isset($results['error']))
                        <div class="alert alert-danger">
                            <strong>Erreur:</strong> {{ $results['error'] }}
                            @if(isset($results['trace']))
                                <details style="margin-top: 10px;">
                                    <summary>Détails de l'erreur</summary>
                                    <pre style="font-size: 11px;">{{ $results['trace'] }}</pre>
                                </details>
                            @endif
                        </div>
                    @else
                        <!-- Statistiques générales -->
                        @if(isset($results['statistics']))
                        <div class="row">
                            <div class="col-md-3">
                                <div class="panel panel-primary">
                                    <div class="panel-body text-center">
                                        <h3>{{ $results['statistics']['total_roles'] }}</h3>
                                        <p>Rôles</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-info">
                                    <div class="panel-body text-center">
                                        <h3>{{ $results['statistics']['total_menus'] }}</h3>
                                        <p>Menus</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-success">
                                    <div class="panel-body text-center">
                                        <h3>{{ $results['statistics']['total_permissions'] }}</h3>
                                        <p>Permissions</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-warning">
                                    <div class="panel-body text-center">
                                        <h3>{{ count($results['statistics']['system_roles']) }}</h3>
                                        <p>Rôles Système</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4>Rôles Disponibles</h4>
                            </div>
                            <div class="panel-body">
                                @foreach($results['statistics']['roles_list'] as $role)
                                    <span class="label {{ in_array($role, $results['statistics']['system_roles']) ? 'label-warning' : 'label-default' }}">
                                        {{ $role }}
                                        @if(in_array($role, $results['statistics']['system_roles']))
                                            (Système)
                                        @endif
                                    </span>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Tests détaillés pour chaque menu -->
                        @if(isset($results['tests']))
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h4>Tests Détaillés par Menu</h4>
                            </div>
                            <div class="panel-body">
                                @foreach($results['tests'] as $menuUrl => $menuTest)
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h5>
                                            <i class="fa fa-folder"></i> 
                                            {{ $menuTest['menu']['libelle'] }} 
                                            <small>({{ $menuTest['menu']['url'] }})</small>
                                        </h5>
                                    </div>
                                    <div class="panel-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped table-condensed">
                                                <thead>
                                                    <tr>
                                                        <th>Rôle</th>
                                                        <th>Utilisateur</th>
                                                        <th>Créer</th>
                                                        <th>Lire</th>
                                                        <th>Modifier</th>
                                                        <th>Supprimer</th>
                                                        <th>Accès Menu</th>
                                                        <th>Super Admin</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($menuTest['roles'] as $roleName => $roleTest)
                                                    <tr>
                                                        <td>
                                                            <strong>{{ $roleName }}</strong>
                                                            @if(in_array($roleName, $results['statistics']['system_roles']))
                                                                <span class="label label-warning">Système</span>
                                                            @endif
                                                        </td>
                                                        <td><small>{{ $roleTest['user'] }}</small></td>
                                                        <td class="text-center">
                                                            {!! ($roleTest['permissions']['Créer'] ?? false) ? '<span class="label label-success">✓</span>' : '<span class="label label-default">✗</span>' !!}
                                                        </td>
                                                        <td class="text-center">
                                                            {!! ($roleTest['permissions']['Lire'] ?? false) ? '<span class="label label-success">✓</span>' : '<span class="label label-default">✗</span>' !!}
                                                        </td>
                                                        <td class="text-center">
                                                            {!! ($roleTest['permissions']['Modifier'] ?? false) ? '<span class="label label-success">✓</span>' : '<span class="label label-default">✗</span>' !!}
                                                        </td>
                                                        <td class="text-center">
                                                            {!! ($roleTest['permissions']['Supprimer'] ?? false) ? '<span class="label label-success">✓</span>' : '<span class="label label-default">✗</span>' !!}
                                                        </td>
                                                        <td class="text-center">
                                                            {!! $roleTest['generic_tests']['can_access_menu'] ? '<span class="label label-success">✓</span>' : '<span class="label label-default">✗</span>' !!}
                                                        </td>
                                                        <td class="text-center">
                                                            {!! $roleTest['generic_tests']['is_super_admin'] ? '<span class="label label-warning">✓</span>' : '<span class="label label-default">✗</span>' !!}
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Tests par URL -->
                        @if(isset($results['url_tests']))
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <h4>Tests des Méthodes Génériques par URL</h4>
                            </div>
                            <div class="panel-body">
                                @foreach($results['url_tests'] as $url => $urlTests)
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h5><i class="fa fa-link"></i> URL: {{ $url }}</h5>
                                    </div>
                                    <div class="panel-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped table-condensed">
                                                <thead>
                                                    <tr>
                                                        <th>Rôle</th>
                                                        <th>Créer</th>
                                                        <th>Lire</th>
                                                        <th>Modifier</th>
                                                        <th>Supprimer</th>
                                                        <th>Permissions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($urlTests as $roleName => $roleTest)
                                                    <tr>
                                                        <td><strong>{{ $roleName }}</strong></td>
                                                        <td class="text-center">
                                                            {!! $roleTest['can_create'] ? '<span class="label label-success">✓</span>' : '<span class="label label-default">✗</span>' !!}
                                                        </td>
                                                        <td class="text-center">
                                                            {!! $roleTest['can_read'] ? '<span class="label label-success">✓</span>' : '<span class="label label-default">✗</span>' !!}
                                                        </td>
                                                        <td class="text-center">
                                                            {!! $roleTest['can_edit'] ? '<span class="label label-success">✓</span>' : '<span class="label label-default">✗</span>' !!}
                                                        </td>
                                                        <td class="text-center">
                                                            {!! $roleTest['can_delete'] ? '<span class="label label-success">✓</span>' : '<span class="label label-default">✗</span>' !!}
                                                        </td>
                                                        <td>
                                                            @if(is_array($roleTest['permissions_for_url']) && count($roleTest['permissions_for_url']) > 0)
                                                                @foreach($roleTest['permissions_for_url'] as $perm)
                                                                    <span class="label label-info">{{ $perm }}</span>
                                                                @endforeach
                                                            @else
                                                                <span class="text-muted">Aucune</span>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <div class="alert alert-info">
                            <h4><i class="fa fa-info-circle"></i> Information</h4>
                            <p>Ce système de test est maintenant complètement générique et fonctionne avec tous les rôles existants et futurs sans modification de code.</p>
                            <ul>
                                <li>Les permissions sont configurées via <code>config/permissions.php</code></li>
                                <li>Les nouveaux rôles sont automatiquement pris en compte</li>
                                <li>Les tests s'adaptent dynamiquement aux rôles disponibles</li>
                            </ul>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
