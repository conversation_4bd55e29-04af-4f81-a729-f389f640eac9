<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Indicateur extends Model
{
    protected $table = 'indicateurs';

    protected $fillable = [
        'libelle',
        'unite'
    ];

    /**
     * Relation many-to-many avec les réformes
     */
    public function reformes()
    {
        return $this->belongsToMany(Reforme::class, 'reformes_indicateurs', 'indicateur_id', 'reforme_id');
    }

    /**
     * Relation avec les associations reforme-indicateur
     */
    public function reformeIndicateurs()
    {
        return $this->hasMany(ReformeIndicateur::class, 'indicateur_id');
    }

    /**
     * Obtenir toutes les évolutions de cet indicateur
     */
    public function evolutions()
    {
        return $this->hasManyThrough(
            EvolutionIndicateur::class,
            ReformeIndicateur::class,
            'indicateur_id',
            'reforme_indicateur_id'
        );
    }

    /**
     * Obtenir les évolutions pour une réforme spécifique
     */
    public function evolutionsPourReforme($reformeId)
    {
        return $this->evolutions()
                    ->whereHas('reformeIndicateur', function($query) use ($reformeId) {
                        $query->where('reforme_id', $reformeId);
                    })
                    ->orderBy('date_evolution');
    }

    /**
     * Vérifier si l'indicateur est utilisé dans des réformes
     */
    public function estUtilise()
    {
        return $this->reformeIndicateurs()->count() > 0;
    }

    /**
     * Obtenir le nombre total de mesures pour cet indicateur
     */
    public function getNombreTotalMesuresAttribute()
    {
        return $this->evolutions()->count();
    }

    /**
     * Obtenir la dernière valeur mesurée (toutes réformes confondues)
     */
    public function getDerniereValeurAttribute()
    {
        $derniereEvolution = $this->evolutions()
                                  ->orderBy('date_evolution', 'desc')
                                  ->first();

        return $derniereEvolution ? $derniereEvolution->valeur : null;
    }

    /**
     * Obtenir la date de la dernière mesure
     */
    public function getDateDerniereMesureAttribute()
    {
        $derniereEvolution = $this->evolutions()
                                  ->orderBy('date_evolution', 'desc')
                                  ->first();

        return $derniereEvolution ? $derniereEvolution->date_evolution : null;
    }

    /**
     * Obtenir les statistiques globales de l'indicateur
     */
    public function getStatistiquesGlobales()
    {
        $evolutions = $this->evolutions()->get();

        if ($evolutions->isEmpty()) {
            return null;
        }

        return [
            'nombre_reformes' => $this->reformes()->count(),
            'nombre_mesures' => $evolutions->count(),
            'valeur_min' => $evolutions->min('valeur'),
            'valeur_max' => $evolutions->max('valeur'),
            'valeur_moyenne' => round($evolutions->avg('valeur'), 2),
            'derniere_valeur' => $this->derniere_valeur,
            'date_derniere_mesure' => $this->date_derniere_mesure
        ];
    }

    /**
     * Scope pour les indicateurs avec des données
     */
    public function scopeAvecDonnees($query)
    {
        return $query->whereHas('evolutions');
    }

    /**
     * Scope pour les indicateurs sans données
     */
    public function scopeSansDonnees($query)
    {
        return $query->whereDoesntHave('evolutions');
    }
}
