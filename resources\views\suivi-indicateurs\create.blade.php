@extends('layout.app')

@section('title', 'Ajouter une Mesure - ' . $indicateur->libelle)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="breadcome-list single-page-breadcome">
                <div class="row">
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                        <div class="breadcome-heading">
                            <h4>Ajouter une Mesure</h4>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                        <ul class="breadcome-menu">
                            <li><a href="{{ route('dashboard') }}">Accueil</a> <span class="bread-slash">/</span></li>
                            <li><a href="{{ route('reforme.index') }}">Réformes</a> <span class="bread-slash">/</span></li>
                            <li><a href="{{ route('suivi-indicateurs.index', $reforme->id) }}">Suivi Indicateurs</a> <span class="bread-slash">/</span></li>
                            <li><span class="bread-blod">Ajouter Mesure</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Informations contextuelles -->
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-info-circle"></i> Contexte
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Réforme :</strong> {{ $reforme->titre }}</p>
                            <p><strong>Indicateur :</strong> {{ $indicateur->libelle }}</p>
                            <p><strong>Unité de mesure :</strong> <span class="label label-default">{{ $indicateur->unite }}</span></p>
                        </div>
                        <div class="col-md-6">
                            @if($derniereEvolution)
                                <div class="alert alert-info">
                                    <strong>Dernière mesure :</strong><br>
                                    <i class="fa fa-calendar"></i> {{ $derniereEvolution->date_formatee }}<br>
                                    <i class="fa fa-line-chart"></i> {{ number_format($derniereEvolution->valeur, 2, ',', ' ') }} {{ $indicateur->unite }}
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fa fa-exclamation-triangle"></i> 
                                    <strong>Première mesure</strong><br>
                                    Aucune donnée précédente pour cet indicateur.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages de feedback -->
    @if(session('error'))
        <div class="alert alert-danger alert-dismissible">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            <i class="fa fa-exclamation-triangle"></i> {{ session('error') }}
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            <strong>Erreurs de validation :</strong>
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <!-- Formulaire d'ajout -->
    <div class="row">
        <div class="col-lg-8 col-lg-offset-2">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-plus"></i> Nouvelle Mesure
                    </h3>
                </div>
                <div class="panel-body">
                    <form action="{{ route('suivi-indicateurs.store', [$reforme->id, $indicateur->id]) }}" 
                          method="POST" class="form-horizontal">
                        @csrf
                        
                        <!-- Date de mesure -->
                        <div class="form-group {{ $errors->has('date_evolution') ? 'has-error' : '' }}">
                            <label for="date_evolution" class="col-sm-3 control-label">
                                Date de mesure <span class="text-danger">*</span>
                            </label>
                            <div class="col-sm-9">
                                <input type="date" 
                                       class="form-control" 
                                       id="date_evolution" 
                                       name="date_evolution" 
                                       value="{{ old('date_evolution', date('Y-m-d')) }}"
                                       max="{{ date('Y-m-d') }}"
                                       required>
                                @if($errors->has('date_evolution'))
                                    <span class="help-block">{{ $errors->first('date_evolution') }}</span>
                                @endif
                                <span class="help-block">
                                    <i class="fa fa-info-circle"></i> 
                                    La date ne peut pas être dans le futur.
                                </span>
                            </div>
                        </div>

                        <!-- Valeur -->
                        <div class="form-group {{ $errors->has('valeur') ? 'has-error' : '' }}">
                            <label for="valeur" class="col-sm-3 control-label">
                                Valeur <span class="text-danger">*</span>
                            </label>
                            <div class="col-sm-9">
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control" 
                                           id="valeur" 
                                           name="valeur" 
                                           value="{{ old('valeur') }}"
                                           step="0.01"
                                           min="0"
                                           placeholder="Entrez la valeur mesurée"
                                           required>
                                    <span class="input-group-addon">{{ $indicateur->unite }}</span>
                                </div>
                                @if($errors->has('valeur'))
                                    <span class="help-block">{{ $errors->first('valeur') }}</span>
                                @endif
                                @if($derniereEvolution)
                                    <span class="help-block">
                                        <i class="fa fa-info-circle"></i> 
                                        Dernière valeur : {{ number_format($derniereEvolution->valeur, 2, ',', ' ') }} {{ $indicateur->unite }}
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Commentaire (optionnel) -->
                        <div class="form-group {{ $errors->has('commentaire') ? 'has-error' : '' }}">
                            <label for="commentaire" class="col-sm-3 control-label">
                                Commentaire
                            </label>
                            <div class="col-sm-9">
                                <textarea class="form-control" 
                                          id="commentaire" 
                                          name="commentaire" 
                                          rows="3"
                                          placeholder="Commentaire optionnel sur cette mesure...">{{ old('commentaire') }}</textarea>
                                @if($errors->has('commentaire'))
                                    <span class="help-block">{{ $errors->first('commentaire') }}</span>
                                @endif
                                <span class="help-block">
                                    <i class="fa fa-info-circle"></i> 
                                    Maximum 500 caractères.
                                </span>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="form-group">
                            <div class="col-sm-offset-3 col-sm-9">
                                <button type="submit" class="btn btn-success">
                                    <i class="fa fa-save"></i> Enregistrer la mesure
                                </button>
                                <a href="{{ route('suivi-indicateurs.index', $reforme->id) }}" 
                                   class="btn btn-default">
                                    <i class="fa fa-times"></i> Annuler
                                </a>
                                @if($derniereEvolution)
                                    <a href="{{ route('suivi-indicateurs.show', [$reforme->id, $indicateur->id]) }}" 
                                       class="btn btn-info">
                                        <i class="fa fa-eye"></i> Voir l'historique
                                    </a>
                                @endif
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Aide contextuelle -->
    <div class="row">
        <div class="col-lg-8 col-lg-offset-2">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-question-circle"></i> Aide
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fa fa-lightbulb-o"></i> Conseils de saisie</h5>
                            <ul class="list-unstyled">
                                <li><i class="fa fa-check text-success"></i> Vérifiez l'unité de mesure</li>
                                <li><i class="fa fa-check text-success"></i> Utilisez des valeurs précises</li>
                                <li><i class="fa fa-check text-success"></i> Respectez la chronologie</li>
                                <li><i class="fa fa-check text-success"></i> Ajoutez un commentaire si nécessaire</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fa fa-exclamation-triangle"></i> Attention</h5>
                            <ul class="list-unstyled">
                                <li><i class="fa fa-warning text-warning"></i> Une seule mesure par date</li>
                                <li><i class="fa fa-warning text-warning"></i> Les valeurs négatives ne sont pas autorisées</li>
                                <li><i class="fa fa-warning text-warning"></i> La date ne peut pas être future</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus sur le champ valeur
    document.getElementById('valeur').focus();
    
    // Validation côté client
    const form = document.querySelector('form');
    const valeurInput = document.getElementById('valeur');
    const dateInput = document.getElementById('date_evolution');
    
    form.addEventListener('submit', function(e) {
        let hasError = false;
        
        // Vérifier la valeur
        if (parseFloat(valeurInput.value) < 0) {
            alert('La valeur ne peut pas être négative.');
            hasError = true;
        }
        
        // Vérifier la date
        const selectedDate = new Date(dateInput.value);
        const today = new Date();
        today.setHours(23, 59, 59, 999); // Fin de journée
        
        if (selectedDate > today) {
            alert('La date ne peut pas être dans le futur.');
            hasError = true;
        }
        
        if (hasError) {
            e.preventDefault();
        }
    });
});
</script>
@endsection
