<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Reforme;
use App\Models\Activitesreformes;
use App\Models\Session;
use App\Models\Indicateur;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

class DashboardTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer des données de test
        $this->createTestData();
    }

    private function createTestData()
    {
        // Créer des utilisateurs
        $users = User::factory(5)->create();
        
        // Créer des sessions actives pour simuler des utilisateurs connectés
        foreach ($users->take(3) as $user) {
            Session::create([
                'user_id' => $user->id,
                'session_id' => 'test_session_' . $user->id,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Test Browser',
                'login_at' => Carbon::now()->subMinutes(10),
                'last_activity' => Carbon::now()->subMinutes(5),
                'status' => 'active'
            ]);
        }

        // Créer des réformes avec différents statuts
        $reformes = collect([
            ['titre' => 'Réforme 1', 'statut_manuel' => 'Achevé'],
            ['titre' => 'Réforme 2', 'statut_manuel' => 'En cours'],
            ['titre' => 'Réforme 3', 'date_fin' => Carbon::now()], // Achevé automatiquement
            ['titre' => 'Réforme 4', 'date_debut' => Carbon::now()->subDays(5)], // En cours automatiquement
        ]);

        foreach ($reformes as $reformeData) {
            Reforme::create(array_merge([
                'objectifs' => 'Objectifs de test',
                'budget' => 100000,
                'date_debut' => Carbon::now()->subDays(30),
                'date_fin_prevue' => Carbon::now()->addDays(30),
                'type_reforme' => 1,
                'created_by' => $users->first()->id,
            ], $reformeData));
        }

        // Créer des activités avec différents statuts
        $activites = collect([
            ['libelle' => 'Activité 1', 'statut' => 'A'], // Achevée
            ['libelle' => 'Activité 2', 'statut' => 'A'], // Achevée
            ['libelle' => 'Activité 3', 'statut' => 'C'], // En cours
            ['libelle' => 'Activité 4', 'statut' => 'P'], // En pause
            ['libelle' => 'Activité 5', 'statut' => 'C'], // En cours
        ]);

        foreach ($activites as $activiteData) {
            Activitesreformes::create(array_merge([
                'reforme_id' => Reforme::first()->id,
                'date_debut' => Carbon::now()->subDays(10),
                'date_fin_prevue' => Carbon::now()->addDays(10),
                'poids' => 1,
                'structure_responsable' => 1,
                'created_by' => $users->first()->id,
            ], $activiteData));
        }

        // Créer des indicateurs
        Indicateur::factory(3)->create();
    }

    /** @test */
    public function dashboard_page_loads_successfully()
    {
        $user = User::first();
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(200);
        $response->assertViewIs('dashboard');
    }

    /** @test */
    public function dashboard_displays_correct_connected_users_count()
    {
        $user = User::first();
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(200);
        $response->assertViewHas('utilisateursConnectes', 3); // 3 sessions actives créées
    }

    /** @test */
    public function dashboard_calculates_activities_validation_percentage_correctly()
    {
        $user = User::first();
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(200);
        
        // 2 activités achevées sur 5 total = 40%
        $response->assertViewHas('activitesValidees', 2);
        $response->assertViewHas('totalActivites', 5);
        $response->assertViewHas('pourcentageActivites', 40.0);
    }

    /** @test */
    public function dashboard_calculates_reforms_validation_percentage_correctly()
    {
        $user = User::first();
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(200);
        
        // 2 réformes achevées (1 manuelle + 1 automatique) sur 4 total = 50%
        $response->assertViewHas('reformesValidees', 2);
        $response->assertViewHas('totalReformes', 4);
        $response->assertViewHas('pourcentageReformes', 50.0);
    }

    /** @test */
    public function dashboard_api_returns_correct_json_stats()
    {
        $user = User::first();
        
        $response = $this->actingAs($user)->get('/dashboard/stats');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'utilisateurs_connectes',
            'activites_validees',
            'total_activites',
            'pourcentage_activites',
            'reformes_validees',
            'total_reformes',
            'pourcentage_reformes',
            'timestamp'
        ]);
        
        $data = $response->json();
        $this->assertEquals(3, $data['utilisateurs_connectes']);
        $this->assertEquals(2, $data['activites_validees']);
        $this->assertEquals(5, $data['total_activites']);
        $this->assertEquals(40.0, $data['pourcentage_activites']);
        $this->assertEquals(2, $data['reformes_validees']);
        $this->assertEquals(4, $data['total_reformes']);
        $this->assertEquals(50.0, $data['pourcentage_reformes']);
    }

    /** @test */
    public function dashboard_handles_empty_data_gracefully()
    {
        // Supprimer toutes les données
        Session::truncate();
        Activitesreformes::truncate();
        Reforme::truncate();
        
        $user = User::first();
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(200);
        $response->assertViewHas('utilisateursConnectes', 0);
        $response->assertViewHas('totalActivites', 0);
        $response->assertViewHas('pourcentageActivites', 0);
        $response->assertViewHas('totalReformes', 0);
        $response->assertViewHas('pourcentageReformes', 0);
    }

    /** @test */
    public function dashboard_requires_authentication()
    {
        $response = $this->get('/dashboard');
        
        $response->assertRedirect('/login');
    }

    /** @test */
    public function dashboard_shows_recent_activities()
    {
        $user = User::first();
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(200);
        $response->assertViewHas('activitesRecentes');
        
        $activitesRecentes = $response->viewData('activitesRecentes');
        $this->assertCount(5, $activitesRecentes); // Toutes les activités créées
    }

    /** @test */
    public function dashboard_responsive_design_elements_present()
    {
        $user = User::first();
        
        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(200);
        
        // Vérifier la présence des classes Bootstrap 3.4 responsives
        $response->assertSee('col-lg-3 col-md-6 col-sm-6 col-xs-12');
        $response->assertSee('dashboard-header');
        $response->assertSee('stat-card');
        $response->assertSee('refresh-btn');
    }
}
