<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Models\PermissionMenu;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class GranularPermissionsTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;
    protected $managerUser;
    protected $regularUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Exécuter les seeders nécessaires
        $this->artisan('db:seed', ['--class' => 'RolePermissionSeeder']);
        
        // Créer des utilisateurs de test
        $this->adminUser = User::factory()->create([
            'nom' => 'Admin',
            'prenom' => 'Test',
            'email' => '<EMAIL>',
            'pwd' => Hash::make('password'),
        ]);
        
        $this->managerUser = User::factory()->create([
            'nom' => 'Manager',
            'prenom' => 'Test',
            'email' => '<EMAIL>',
            'pwd' => Hash::make('password'),
        ]);
        
        $this->regularUser = User::factory()->create([
            'nom' => 'User',
            'prenom' => 'Test',
            'email' => '<EMAIL>',
            'pwd' => Hash::make('password'),
        ]);

        // Assigner les rôles
        $adminRole = Role::where('role_name', 'Administrateur')->first();
        $managerRole = Role::where('role_name', 'Gestionnaire')->first();
        $userRole = Role::where('role_name', 'Utilisateur')->first();

        $this->adminUser->roles()->attach($adminRole);
        $this->managerUser->roles()->attach($managerRole);
        $this->regularUser->roles()->attach($userRole);
    }

    /** @test */
    public function admin_has_all_permissions()
    {
        $activitesMenuId = Menu::where('url', '/activites')->first()->id;
        
        $this->assertTrue($this->adminUser->hasPermission($activitesMenuId, 'Créer'));
        $this->assertTrue($this->adminUser->hasPermission($activitesMenuId, 'Lire'));
        $this->assertTrue($this->adminUser->hasPermission($activitesMenuId, 'Modifier'));
        $this->assertTrue($this->adminUser->hasPermission($activitesMenuId, 'Supprimer'));
    }

    /** @test */
    public function manager_has_limited_permissions()
    {
        $activitesMenuId = Menu::where('url', '/activites')->first()->id;
        
        // Gestionnaire peut créer, lire et modifier mais pas supprimer
        $this->assertTrue($this->managerUser->hasPermission($activitesMenuId, 'Créer'));
        $this->assertTrue($this->managerUser->hasPermission($activitesMenuId, 'Lire'));
        $this->assertTrue($this->managerUser->hasPermission($activitesMenuId, 'Modifier'));
        $this->assertFalse($this->managerUser->hasPermission($activitesMenuId, 'Supprimer'));
    }

    /** @test */
    public function regular_user_has_read_only_permissions()
    {
        $activitesMenuId = Menu::where('url', '/activites')->first()->id;
        
        // Utilisateur peut seulement lire
        $this->assertFalse($this->regularUser->hasPermission($activitesMenuId, 'Créer'));
        $this->assertTrue($this->regularUser->hasPermission($activitesMenuId, 'Lire'));
        $this->assertFalse($this->regularUser->hasPermission($activitesMenuId, 'Modifier'));
        $this->assertFalse($this->regularUser->hasPermission($activitesMenuId, 'Supprimer'));
    }

    /** @test */
    public function manager_cannot_access_user_management()
    {
        $roleMenuId = Menu::where('url', '/role')->first()->id;
        
        // Gestionnaire ne peut pas accéder à la gestion des rôles
        $this->assertFalse($this->managerUser->hasPermission($roleMenuId, 'Créer'));
        $this->assertFalse($this->managerUser->hasPermission($roleMenuId, 'Lire'));
        $this->assertFalse($this->managerUser->hasPermission($roleMenuId, 'Modifier'));
        $this->assertFalse($this->managerUser->hasPermission($roleMenuId, 'Supprimer'));
    }

    /** @test */
    public function user_cannot_access_dashboard_management()
    {
        $dashboardMenuId = Menu::where('url', '/dashboard')->first()->id;
        
        // Utilisateur peut seulement lire le dashboard
        $this->assertFalse($this->regularUser->hasPermission($dashboardMenuId, 'Créer'));
        $this->assertTrue($this->regularUser->hasPermission($dashboardMenuId, 'Lire'));
        $this->assertFalse($this->regularUser->hasPermission($dashboardMenuId, 'Modifier'));
        $this->assertFalse($this->regularUser->hasPermission($dashboardMenuId, 'Supprimer'));
    }

    /** @test */
    public function convenience_methods_work_correctly()
    {
        $activitesMenuId = Menu::where('url', '/activites')->first()->id;
        
        // Test des méthodes de commodité pour l'administrateur
        $this->assertTrue($this->adminUser->canCreate($activitesMenuId));
        $this->assertTrue($this->adminUser->canEdit($activitesMenuId));
        $this->assertTrue($this->adminUser->canDelete($activitesMenuId));
        $this->assertTrue($this->adminUser->canRead($activitesMenuId));
        
        // Test des méthodes de commodité pour l'utilisateur régulier
        $this->assertFalse($this->regularUser->canCreate($activitesMenuId));
        $this->assertFalse($this->regularUser->canEdit($activitesMenuId));
        $this->assertFalse($this->regularUser->canDelete($activitesMenuId));
        $this->assertTrue($this->regularUser->canRead($activitesMenuId));
    }

    /** @test */
    public function url_based_permission_methods_work()
    {
        // Test des méthodes basées sur l'URL
        $this->assertTrue($this->adminUser->canCreateForUrl('/activites'));
        $this->assertTrue($this->adminUser->canEditForUrl('/activites'));
        $this->assertTrue($this->adminUser->canDeleteForUrl('/activites'));
        $this->assertTrue($this->adminUser->canReadForUrl('/activites'));
        
        // Utilisateur régulier
        $this->assertFalse($this->regularUser->canCreateForUrl('/activites'));
        $this->assertFalse($this->regularUser->canEditForUrl('/activites'));
        $this->assertFalse($this->regularUser->canDeleteForUrl('/activites'));
        $this->assertTrue($this->regularUser->canReadForUrl('/activites'));
    }
}
