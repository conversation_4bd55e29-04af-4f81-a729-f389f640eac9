<?php

/**
 * Test des permissions pour l'utilisateur 'aho'
 * Ce script vérifie que l'intégration dev-don + permissions fonctionne correctement
 */

// Simuler l'environnement Laravel
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/';

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Role;
use App\Models\Menu;
use App\Services\PermissionService;

echo "=== TEST DES PERMISSIONS POUR L'UTILISATEUR 'AHO' ===\n\n";

try {
    // Initialiser Laravel
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

    echo "✅ Laravel initialisé avec succès\n\n";

    // Test 1: Vérifier que le rôle 'aho' existe
    echo "1. Vérification du rôle 'aho':\n";
    $ahoRole = Role::where('role_name', 'aho')->first();
    
    if ($ahoRole) {
        echo "   ✅ Rôle 'aho' trouvé (ID: {$ahoRole->id})\n";
        
        // Compter les permissions
        $permissionsCount = $ahoRole->permissionMenus()->count();
        echo "   📊 Nombre de permissions: {$permissionsCount}\n";
        
        if ($permissionsCount > 0) {
            echo "   ✅ Permissions assignées\n";
        } else {
            echo "   ⚠️  Aucune permission assignée - exécuter AhoRoleSeeder\n";
        }
    } else {
        echo "   ❌ Rôle 'aho' non trouvé - exécuter AhoRoleSeeder\n";
    }

    // Test 2: Vérifier l'utilisateur 'aho'
    echo "\n2. Vérification de l'utilisateur 'aho':\n";
    $ahoUser = User::where('username', 'aho')->first();
    
    if ($ahoUser) {
        echo "   ✅ Utilisateur 'aho' trouvé (ID: {$ahoUser->id})\n";
        
        // Vérifier les rôles de l'utilisateur
        $userRoles = $ahoUser->roles()->pluck('role_name')->toArray();
        echo "   👤 Rôles assignés: " . implode(', ', $userRoles) . "\n";
        
        if (in_array('aho', $userRoles)) {
            echo "   ✅ Rôle 'aho' assigné à l'utilisateur\n";
        } else {
            echo "   ⚠️  Rôle 'aho' non assigné à l'utilisateur\n";
        }
    } else {
        echo "   ❌ Utilisateur 'aho' non trouvé\n";
    }

    // Test 3: Tester le service de permissions
    echo "\n3. Test du service de permissions:\n";
    $permissionService = new PermissionService();
    
    if ($ahoUser && $ahoRole) {
        // Tester quelques permissions critiques
        $testPermissions = [
            '/dashboard' => 'Lire',
            '/activites' => 'Créer',
            '/reforme' => 'Modifier',
            '/indicateurs' => 'Supprimer'
        ];
        
        foreach ($testPermissions as $url => $action) {
            $hasPermission = $permissionService->hasPermission($ahoUser, $url, $action);
            $status = $hasPermission ? "✅" : "❌";
            echo "   $status Permission '$action' sur '$url'\n";
        }
    } else {
        echo "   ⚠️  Impossible de tester - utilisateur ou rôle manquant\n";
    }

    // Test 4: Tester la génération des menus
    echo "\n4. Test de génération des menus:\n";
    if ($ahoUser) {
        try {
            $accessibleMenus = $ahoUser->getAccessibleMenus();
            echo "   ✅ Méthode getAccessibleMenus() fonctionne\n";
            echo "   📋 Menus accessibles: " . $accessibleMenus->count() . "\n";
            
            foreach ($accessibleMenus as $menu) {
                echo "     - {$menu->lib} ({$menu->url})\n";
            }
        } catch (\Exception $e) {
            echo "   ❌ Erreur dans getAccessibleMenus(): " . $e->getMessage() . "\n";
        }
    }

    // Test 5: Vérifier les nouvelles fonctionnalités de dev-don
    echo "\n5. Vérification des fonctionnalités dev-don:\n";
    
    // Vérifier les modèles
    $modelsDevDon = [
        'App\Models\Notification' => 'Modèle Notification',
        'App\Http\Controllers\NotificationController' => 'Contrôleur Notification',
        'App\Http\Controllers\ProfileController' => 'Contrôleur Profile'
    ];
    
    foreach ($modelsDevDon as $class => $description) {
        if (class_exists($class)) {
            echo "   ✅ $description disponible\n";
        } else {
            echo "   ❌ $description manquant\n";
        }
    }

    echo "\n=== RÉSUMÉ DU TEST ===\n";
    
    if ($ahoRole && $ahoUser && in_array('aho', $userRoles ?? [])) {
        echo "🎉 SUCCÈS: L'intégration dev-don + permissions est fonctionnelle !\n";
        echo "\n📋 Actions recommandées:\n";
        echo "1. Se connecter avec l'utilisateur 'aho'\n";
        echo "2. Vérifier l'affichage des menus\n";
        echo "3. Tester les fonctionnalités CRUD\n";
        echo "4. Tester les notifications\n";
    } else {
        echo "⚠️  ATTENTION: Configuration incomplète détectée\n";
        echo "\n🔧 Actions correctives:\n";
        echo "1. Exécuter: php artisan db:seed --class=AhoRoleSeeder\n";
        echo "2. Vérifier l'assignation du rôle à l'utilisateur\n";
        echo "3. Relancer ce test\n";
    }

} catch (\Exception $e) {
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " (ligne " . $e->getLine() . ")\n";
    
    echo "\n🔧 Solutions possibles:\n";
    echo "1. Vérifier la configuration de la base de données\n";
    echo "2. Exécuter les migrations: php artisan migrate\n";
    echo "3. Exécuter les seeders: php artisan db:seed\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
