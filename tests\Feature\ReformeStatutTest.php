<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Reforme;
use App\Models\User;
use App\Models\TypeReforme;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class ReformeStatutTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $typeReforme;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->typeReforme = TypeReforme::factory()->create();
    }

    /** @test */
    public function test_statut_automatique_brouillon()
    {
        $reforme = Reforme::factory()->create([
            'date_debut' => null,
            'date_fin' => null,
            'statut_manuel' => null,
        ]);

        $this->assertEquals('Brouillon', $reforme->statut);
        $this->assertStringContainsString('label-default', $reforme->status_badge);
    }

    /** @test */
    public function test_statut_automatique_planifie()
    {
        $reforme = Reforme::factory()->create([
            'date_debut' => Carbon::tomorrow(),
            'date_fin' => null,
            'statut_manuel' => null,
        ]);

        $this->assertEquals('Planifié', $reforme->statut);
        $this->assertStringContainsString('label-info', $reforme->status_badge);
    }

    /** @test */
    public function test_statut_automatique_en_cours()
    {
        $reforme = Reforme::factory()->create([
            'date_debut' => Carbon::yesterday(),
            'date_fin' => null,
            'statut_manuel' => null,
        ]);

        $this->assertEquals('En cours', $reforme->statut);
        $this->assertStringContainsString('label-warning', $reforme->status_badge);
    }

    /** @test */
    public function test_statut_automatique_acheve()
    {
        $reforme = Reforme::factory()->create([
            'date_debut' => Carbon::yesterday(),
            'date_fin' => Carbon::today(),
            'statut_manuel' => null,
        ]);

        $this->assertEquals('Achevé', $reforme->statut);
        $this->assertStringContainsString('label-success', $reforme->status_badge);
    }

    /** @test */
    public function test_statut_manuel_en_pause()
    {
        $reforme = Reforme::factory()->create([
            'date_debut' => Carbon::yesterday(),
            'date_fin' => null,
            'statut_manuel' => 'En pause',
        ]);

        $this->assertEquals('En pause', $reforme->statut);
        $this->assertStringContainsString('label-danger', $reforme->status_badge);
        $this->assertStringContainsString('En pause', $reforme->status_badge);
    }

    /** @test */
    public function test_statut_manuel_prioritaire_sur_automatique()
    {
        // Réforme qui devrait être "Achevé" automatiquement mais est forcée "En pause"
        $reforme = Reforme::factory()->create([
            'date_debut' => Carbon::yesterday(),
            'date_fin' => Carbon::today(),
            'statut_manuel' => 'En pause',
        ]);

        $this->assertEquals('En pause', $reforme->statut);
        $this->assertStringContainsString('label-danger', $reforme->status_badge);
    }

    /** @test */
    public function test_couleurs_statuts_conformes_specifications()
    {
        // Test En cours → Jaune (label-warning)
        $reformeEnCours = Reforme::factory()->create([
            'statut_manuel' => 'En cours'
        ]);
        $this->assertStringContainsString('label-warning', $reformeEnCours->status_badge);

        // Test En pause → Rouge (label-danger)
        $reformeEnPause = Reforme::factory()->create([
            'statut_manuel' => 'En pause'
        ]);
        $this->assertStringContainsString('label-danger', $reformeEnPause->status_badge);

        // Test Achevé → Vert (label-success)
        $reformeAcheve = Reforme::factory()->create([
            'statut_manuel' => 'Achevé'
        ]);
        $this->assertStringContainsString('label-success', $reformeAcheve->status_badge);
    }

    /** @test */
    public function test_modification_statut_manuel_via_formulaire()
    {
        $this->actingAs($this->user);

        $reforme = Reforme::factory()->create([
            'statut_manuel' => null,
        ]);

        // Modifier le statut à "En pause"
        $response = $this->put(route('reforme.update', $reforme->id), [
            'titre' => $reforme->titre,
            'objectifs' => $reforme->objectifs,
            'budget' => $reforme->budget,
            'date_debut' => $reforme->date_debut,
            'date_fin_prevue' => $reforme->date_fin_prevue,
            'date_fin' => $reforme->date_fin,
            'pieces_justificatifs' => $reforme->pieces_justificatifs,
            'statut_manuel' => 'En pause',
            'type_reforme' => $reforme->type_reforme,
        ]);

        $reforme->refresh();
        $this->assertEquals('En pause', $reforme->statut_manuel);
        $this->assertEquals('En pause', $reforme->statut);
    }

    /** @test */
    public function test_suppression_statut_manuel_retour_automatique()
    {
        $reforme = Reforme::factory()->create([
            'date_debut' => Carbon::yesterday(),
            'date_fin' => null,
            'statut_manuel' => 'En pause',
        ]);

        // Vérifier que le statut manuel est prioritaire
        $this->assertEquals('En pause', $reforme->statut);

        // Supprimer le statut manuel
        $reforme->update(['statut_manuel' => null]);

        // Vérifier que le statut automatique reprend
        $this->assertEquals('En cours', $reforme->statut);
    }
}
