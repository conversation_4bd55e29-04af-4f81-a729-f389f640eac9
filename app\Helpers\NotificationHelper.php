<?php

namespace App\Helpers;

use App\Models\Notification;
use Illuminate\Support\Facades\Auth;

class NotificationHelper
{
    /**
     * Créer une notification pour l'utilisateur connecté
     */
    public static function create($message, $url = null, $userId = null)
    {
        $userId = $userId ?? Auth::id();
        
        if (!$userId) {
            return false;
        }

        return Notification::create([
            'user_id' => $userId,
            'message' => $message,
            'url' => $url,
            'date_notification' => now(),
            'statut' => 'N'
        ]);
    }

    /**
     * Créer une notification pour tous les utilisateurs
     */
    public static function createForAll($message, $url = null)
    {
        $users = \App\Models\User::all();

        foreach ($users as $user) {
            self::create($message, $url, $user->id);
        }

        return $users->count();
    }

    /**
     * Créer une notification pour des utilisateurs spécifiques
     */
    public static function createForUsers($userIds, $message, $url = null)
    {
        $created = 0;
        foreach ($userIds as $userId) {
            if (self::create($message, $url, $userId)) {
                $created++;
            }
        }
        return $created;
    }

    /**
     * Créer une notification pour les utilisateurs avec un rôle spécifique
     */
    public static function createForRole($roleName, $message, $url = null)
    {
        $users = \App\Models\User::whereHas('roles', function($query) use ($roleName) {
            $query->where('role_name', $roleName);
        })->get();

        $created = 0;
        foreach ($users as $user) {
            if (self::create($message, $url, $user->id)) {
                $created++;
            }
        }
        return $created;
    }

    /**
     * Créer une notification de succès
     */
    public static function success($message, $url = null, $userId = null)
    {
        return self::create("✅ " . $message, $url, $userId);
    }

    /**
     * Créer une notification d'erreur
     */
    public static function error($message, $url = null, $userId = null)
    {
        return self::create("❌ " . $message, $url, $userId);
    }

    /**
     * Créer une notification d'information
     */
    public static function info($message, $url = null, $userId = null)
    {
        return self::create("ℹ️ " . $message, $url, $userId);
    }

    /**
     * Créer une notification d'avertissement
     */
    public static function warning($message, $url = null, $userId = null)
    {
        return self::create("⚠️ " . $message, $url, $userId);
    }

    /**
     * Créer une notification pour une nouvelle réforme
     */
    public static function nouvelleReforme($reforme, $userId = null)
    {
        $message = "Nouvelle réforme créée : " . $reforme->titre;
        return self::success($message, "/reforme", $userId);
    }

    /**
     * Créer une notification pour une nouvelle activité
     */
    public static function nouvelleActivite($activite, $userId = null)
    {
        $message = "Nouvelle activité créée : " . $activite->libelle;
        return self::info($message, "/activites", $userId);
    }

    /**
     * Créer une notification pour un suivi d'activité
     */
    public static function suiviActivite($activite, $userId = null)
    {
        $message = "Nouveau suivi pour l'activité : " . $activite->libelle;
        return self::info($message, "/suivi", $userId);
    }

    /**
     * Créer une notification pour un changement de statut
     */
    public static function changementStatut($entity, $ancienStatut, $nouveauStatut, $userId = null)
    {
        $type = class_basename($entity);
        $message = "Statut de {$type} modifié : {$ancienStatut} → {$nouveauStatut}";
        return self::info($message, null, $userId);
    }

    /**
     * Nettoyer les anciennes notifications (plus de 30 jours)
     */
    public static function cleanOldNotifications()
    {
        $deleted = Notification::where('date_notification', '<', now()->subDays(30))->delete();
        return $deleted;
    }

    /**
     * Marquer toutes les notifications d'un utilisateur comme lues
     */
    public static function markAllAsRead($userId = null)
    {
        $userId = $userId ?? Auth::id();

        return Notification::where('user_id', $userId)
            ->where('statut', 'N')
            ->update(['statut' => 'L']);
    }

    /**
     * Obtenir le nombre de notifications non lues pour un utilisateur
     */
    public static function getUnreadCount($userId = null)
    {
        $userId = $userId ?? Auth::id();

        return Notification::where('user_id', $userId)
            ->where('statut', 'N')
            ->count();
    }
} 