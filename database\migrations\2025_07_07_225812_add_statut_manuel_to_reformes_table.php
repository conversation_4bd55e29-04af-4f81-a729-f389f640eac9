<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reformes', function (Blueprint $table) {
            $table->enum('statut_manuel', ['En cours', 'En pause', 'Achevé'])->nullable()->after('pieces_justificatifs');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reformes', function (Blueprint $table) {
            $table->dropColumn('statut_manuel');
        });
    }
};
