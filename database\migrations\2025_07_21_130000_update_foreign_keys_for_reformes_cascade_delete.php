<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Pour la table 'activites_reformes'
        Schema::table('activites_reformes', function (Blueprint $table) {
            $table->dropForeign(['reforme_id']);
            $table->foreign('reforme_id')
                  ->references('id')->on('reformes')
                  ->onDelete('cascade');
        });

        // Pour la table 'reformes_indicateurs'
        Schema::table('reformes_indicateurs', function (Blueprint $table) {
            $table->dropForeign(['reforme_id']);
            $table->foreign('reforme_id')
                  ->references('id')->on('reformes')
                  ->onDelete('cascade');
        });

        // Pour la table 'reformes_structure'
        Schema::table('reformes_structure', function (Blueprint $table) {
            $table->dropForeign(['reforme_id']);
            $table->foreign('reforme_id')
                  ->references('id')->on('reformes')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Pour la table 'activites_reformes'
        Schema::table('activites_reformes', function (Blueprint $table) {
            $table->dropForeign(['reforme_id']);
            $table->foreign('reforme_id')->references('id')->on('reformes');
        });

        // Pour la table 'reformes_indicateurs'
        Schema::table('reformes_indicateurs', function (Blueprint $table) {
            $table->dropForeign(['reforme_id']);
            $table->foreign('reforme_id')->references('id')->on('reformes');
        });

        // Pour la table 'reformes_structure'
        Schema::table('reformes_structure', function (Blueprint $table) {
            $table->dropForeign(['reforme_id']);
            $table->foreign('reforme_id')->references('id')->on('reformes');
        });
    }
}; 