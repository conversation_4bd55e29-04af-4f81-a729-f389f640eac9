@extends('layout.app')

@section('title', 'Test des Permissions Granulaires')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Test des Permissions Granulaires</h3>
                </div>
                <div class="panel-body">
                    @if(isset($results['error']))
                        <div class="alert alert-danger">
                            <h4>Erreur:</h4>
                            <p>{{ $results['error'] }}</p>
                            @if(isset($results['trace']))
                                <details>
                                    <summary>Stack Trace</summary>
                                    <pre>{{ $results['trace'] }}</pre>
                                </details>
                            @endif
                        </div>
                    @else
                        <!-- Test du menu Activités -->
                        @if(isset($results['activites']))
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h4>📋 Permissions pour le menu Activités (ID: {{ $results['activites']['menu_id'] }})</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h5>👤 ADMINISTRATEUR</h5>
                                        <p><strong>Email:</strong> {{ $results['activites']['admin']['user'] }}</p>
                                        <ul class="list-unstyled">
                                            <li>Créer: {!! $results['activites']['admin']['create'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                            <li>Lire: {!! $results['activites']['admin']['read'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                            <li>Modifier: {!! $results['activites']['admin']['update'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                            <li>Supprimer: {!! $results['activites']['admin']['delete'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h5>👤 GESTIONNAIRE</h5>
                                        <p><strong>Email:</strong> {{ $results['activites']['manager']['user'] }}</p>
                                        <ul class="list-unstyled">
                                            <li>Créer: {!! $results['activites']['manager']['create'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                            <li>Lire: {!! $results['activites']['manager']['read'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                            <li>Modifier: {!! $results['activites']['manager']['update'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                            <li>Supprimer: {!! $results['activites']['manager']['delete'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h5>👤 UTILISATEUR</h5>
                                        <p><strong>Email:</strong> {{ $results['activites']['user']['user'] }}</p>
                                        <ul class="list-unstyled">
                                            <li>Créer: {!! $results['activites']['user']['create'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                            <li>Lire: {!! $results['activites']['user']['read'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                            <li>Modifier: {!! $results['activites']['user']['update'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                            <li>Supprimer: {!! $results['activites']['user']['delete'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Test du menu Rôles -->
                        @if(isset($results['roles']))
                        <div class="panel panel-warning">
                            <div class="panel-heading">
                                <h4>🔐 Accès au menu Rôles (Admin uniquement) (ID: {{ $results['roles']['menu_id'] }})</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h5>Admin peut lire:</h5>
                                        {!! $results['roles']['admin']['read'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}
                                    </div>
                                    <div class="col-md-4">
                                        <h5>Manager peut lire:</h5>
                                        {!! $results['roles']['manager']['read'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}
                                    </div>
                                    <div class="col-md-4">
                                        <h5>User peut lire:</h5>
                                        {!! $results['roles']['user']['read'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Test des méthodes de commodité -->
                        @if(isset($results['convenience_methods']))
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <h4>🔧 Test des méthodes de commodité</h4>
                            </div>
                            <div class="panel-body">
                                <ul class="list-unstyled">
                                    <li><strong>Admin canCreateForUrl('/activites'):</strong> {!! $results['convenience_methods']['admin_can_create_activites'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                    <li><strong>User canCreateForUrl('/activites'):</strong> {!! $results['convenience_methods']['user_can_create_activites'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                    <li><strong>User canReadForUrl('/activites'):</strong> {!! $results['convenience_methods']['user_can_read_activites'] ? '<span class="label label-success">✅ OUI</span>' : '<span class="label label-danger">❌ NON</span>' !!}</li>
                                </ul>
                            </div>
                        </div>
                        @endif

                        <div class="alert alert-info">
                            <h4>Résultats attendus:</h4>
                            <ul>
                                <li><strong>Administrateur:</strong> Toutes les permissions (✅ partout)</li>
                                <li><strong>Gestionnaire:</strong> Créer, Lire, Modifier mais PAS Supprimer (❌ pour Supprimer uniquement)</li>
                                <li><strong>Utilisateur:</strong> Lire uniquement (✅ pour Lire, ❌ pour le reste)</li>
                                <li><strong>Menu Rôles:</strong> Accessible à l'Admin uniquement</li>
                            </ul>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
