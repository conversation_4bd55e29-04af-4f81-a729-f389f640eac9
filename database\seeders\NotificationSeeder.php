<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Notification;
use App\Models\User;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer le premier utilisateur (ou créer un utilisateur de test)
        $user = User::first();
        
        if (!$user) {
            $this->command->info('Aucun utilisateur trouvé. Créez d\'abord un utilisateur.');
            return;
        }

        // Supprimer les anciennes notifications de test
        Notification::where('user_id', $user->id)->delete();

        // Créer des notifications de test
        $notifications = [
            [
                'user_id' => $user->id,
                'message' => 'Nouvelle réforme ajoutée : Réforme administrative 2024',
                'url' => '/reforme',
                'date_notification' => now()->subHours(2),
                'statut' => 'N'
            ],
            [
                'user_id' => $user->id,
                'message' => 'Mise à jour du statut de la réforme économique',
                'url' => '/reforme',
                'date_notification' => now()->subHours(4),
                'statut' => 'N'
            ],
            [
                'user_id' => $user->id,
                'message' => 'Nouvel indicateur ajouté au tableau de bord',
                'url' => '/indicateur',
                'date_notification' => now()->subDay(),
                'statut' => 'L'
            ],
            [
                'user_id' => $user->id,
                'message' => 'Rapport mensuel des réformes disponible',
                'url' => '/dashboard',
                'date_notification' => now()->subDays(2),
                'statut' => 'L'
            ],
            [
                'user_id' => $user->id,
                'message' => 'Nouveau type de réforme créé : Réforme technologique',
                'url' => '/typereforme',
                'date_notification' => now()->subDays(3),
                'statut' => 'L'
            ]
        ];

        foreach ($notifications as $notification) {
            Notification::create($notification);
        }

        $this->command->info('Notifications de test créées avec succès !');
    }
} 