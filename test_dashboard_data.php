<?php

/**
 * Test rapide pour vérifier que le dashboard récupère bien les données
 */

require_once 'vendor/autoload.php';

// Charger Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🧪 TEST DES DONNÉES DU DASHBOARD\n";
echo "================================\n\n";

try {
    // Test des statistiques principales
    echo "📊 STATISTIQUES PRINCIPALES :\n";
    echo "=============================\n";
    
    // Utilisateurs connectés récemment
    $utilisateursConnectes = DB::table('user_sessions')
        ->where('created_at', '>=', Carbon\Carbon::now()->subMinutes(30))
        ->distinct('user_id')
        ->count('user_id');
    echo "• Utilisateurs connectés (30 min) : $utilisateursConnectes\n";
    
    // Activités
    $totalActivites = DB::table('activites_reformes')->count();
    $activitesValidees = DB::table('activites_reformes')->where('statut', 'A')->count();
    $pourcentageActivites = $totalActivites > 0 ? round(($activitesValidees / $totalActivites) * 100, 1) : 0;
    echo "• Total activités : $totalActivites\n";
    echo "• Activités validées : $activitesValidees\n";
    echo "• Pourcentage activités : $pourcentageActivites%\n";
    
    // Réformes
    $totalReformes = DB::table('reformes')->count();
    $reformesValidees = DB::table('reformes')->where('statut', 'C')->count();
    $pourcentageReformes = $totalReformes > 0 ? round(($reformesValidees / $totalReformes) * 100, 1) : 0;
    echo "• Total réformes : $totalReformes\n";
    echo "• Réformes validées : $reformesValidees\n";
    echo "• Pourcentage réformes : $pourcentageReformes%\n";
    
    // Autres statistiques
    $totalUtilisateurs = DB::table('users')->count();
    $totalIndicateurs = DB::table('indicateurs')->count();
    echo "• Total utilisateurs : $totalUtilisateurs\n";
    echo "• Total indicateurs : $totalIndicateurs\n";
    
    echo "\n";
    
    // Test des réformes par type
    echo "📋 RÉFORMES PAR TYPE :\n";
    echo "=====================\n";
    $reformesParType = DB::table('reformes')
        ->join('type_reforme', 'reformes.type_reforme', '=', 'type_reforme.id')
        ->select('type_reforme.lib', DB::raw('count(*) as total'))
        ->groupBy('type_reforme.lib')
        ->get();
    
    foreach ($reformesParType as $type) {
        echo "• {$type->lib} : {$type->total}\n";
    }
    
    echo "\n";
    
    // Test des activités récentes
    echo "📝 ACTIVITÉS RÉCENTES :\n";
    echo "======================\n";
    $activitesRecentes = DB::table('activites_reformes')
        ->join('reformes', 'activites_reformes.reforme_id', '=', 'reformes.id')
        ->select(
            'activites_reformes.libelle',
            'activites_reformes.statut',
            'activites_reformes.created_at',
            'reformes.titre as reforme_titre'
        )
        ->orderBy('activites_reformes.created_at', 'desc')
        ->limit(5)
        ->get();
    
    foreach ($activitesRecentes as $activite) {
        $statutLabel = $activite->statut == 'A' ? 'Achevée' : ($activite->statut == 'C' ? 'En cours' : 'Autre');
        echo "• {$activite->libelle} ({$activite->reforme_titre}) - $statutLabel - {$activite->created_at}\n";
    }
    
    echo "\n";
    
    // Test des sessions utilisateurs
    echo "🔐 SESSIONS RÉCENTES :\n";
    echo "=====================\n";
    $sessions = DB::table('user_sessions')
        ->join('users', 'user_sessions.user_id', '=', 'users.id')
        ->join('personne', 'users.personne_id', '=', 'personne.id')
        ->select(
            'personne.nom',
            'personne.prenom',
            'user_sessions.created_at',
            'user_sessions.ip_address'
        )
        ->orderBy('user_sessions.created_at', 'desc')
        ->limit(5)
        ->get();
    
    foreach ($sessions as $session) {
        echo "• {$session->nom} {$session->prenom} - {$session->created_at} - IP: {$session->ip_address}\n";
    }
    
    echo "\n";
    
    // Test des structures
    echo "🏢 STRUCTURES :\n";
    echo "==============\n";
    $structures = DB::table('structure')->select('lib_court', 'lib_long')->get();
    foreach ($structures as $structure) {
        echo "• {$structure->lib_court} - {$structure->lib_long}\n";
    }
    
    echo "\n✅ TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS !\n";
    echo "Vos données sont bien présentes et accessibles.\n";
    
} catch (Exception $e) {
    echo "❌ ERREUR : " . $e->getMessage() . "\n";
    echo "Trace : " . $e->getTraceAsString() . "\n";
}

echo "\n🎉 TEST TERMINÉ\n";
echo "===============\n";

?>
