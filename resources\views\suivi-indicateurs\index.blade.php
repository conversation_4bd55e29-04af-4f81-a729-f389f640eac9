@extends('layout.app')

@section('title', 'Suivi des Indicateurs - ' . $reforme->titre)

@section('content')
<div class="container-fluid">
    @include('components.page-header', [
        'title' => 'Suivi des Indicateurs',
        'breadcrumb' => [
            ['label' => 'Accueil', 'url' => route('dashboard')],
            ['label' => 'Réformes', 'url' => route('reforme.index')],
            ['label' => 'Suivi Indicateurs']
        ]
    ])

    <!-- Informations de la réforme -->
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-line-chart"></i> {{ $reforme->titre }}
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-8">
                            <p><strong>Objectifs :</strong> {{ $reforme->objectifs }}</p>
                            <p><strong>Période :</strong> {{ \Carbon\Carbon::parse($reforme->date_debut)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($reforme->date_fin_prevue)->format('d/m/Y') }}</p>
                        </div>
                        <div class="col-md-4 text-right">
                            <span class="label {{ $reforme->statut == 'En cours' ? 'label-warning' : ($reforme->statut == 'Achevé' ? 'label-success' : 'label-default') }}">
                                {{ $reforme->statut }}
                            </span>
                            <br><br>
                            <strong>{{ $indicateursAvecDonnees->count() }}</strong> indicateur(s) suivi(s)
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages de feedback -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            <i class="fa fa-check"></i> {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            <i class="fa fa-exclamation-triangle"></i> {{ session('error') }}
        </div>
    @endif

    <!-- Liste des indicateurs -->
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-list"></i> Indicateurs de Performance
                    </h3>
                    <div class="panel-actions">
                        <a href="{{ route('reforme.show', $reforme->id) }}" class="btn btn-default btn-xs">
                            <i class="fa fa-arrow-left"></i> Retour à la réforme
                        </a>
                    </div>
                </div>
                <div class="panel-body">
                    @if($indicateursAvecDonnees->isEmpty())
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 
                            Aucun indicateur n'est encore associé à cette réforme ou aucune donnée n'a été saisie.
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Indicateur</th>
                                        <th>Unité</th>
                                        <th>Valeur Actuelle</th>
                                        <th>Dernière Mesure</th>
                                        <th>Tendance</th>
                                        <th>Nb. Mesures</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($indicateursAvecDonnees as $reformeIndicateur)
                                        @php
                                            $indicateur = $reformeIndicateur->indicateur;
                                            $derniereEvolution = $reformeIndicateur->getDerniereEvolution();
                                        @endphp
                                        <tr>
                                            <td>
                                                <strong>{{ $indicateur->libelle }}</strong>
                                            </td>
                                            <td>
                                                <span class="label label-default">{{ $indicateur->unite }}</span>
                                            </td>
                                            <td>
                                                @if($derniereEvolution)
                                                    <span class="text-primary">
                                                        <strong>{{ number_format($derniereEvolution->valeur, 2, ',', ' ') }}</strong>
                                                    </span>
                                                @else
                                                    <span class="text-muted">Aucune donnée</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($derniereEvolution)
                                                    {{ $derniereEvolution->date_formatee }}
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($derniereEvolution)
                                                    {!! $derniereEvolution->icone_tendance !!}
                                                    <span class="small">{{ $derniereEvolution->tendance }}</span>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge">{{ $reformeIndicateur->nombre_mesures }}</span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-xs">
                                                    <a href="{{ route('suivi-indicateurs.show', [$reforme->id, $indicateur->id]) }}" 
                                                       class="btn btn-info" title="Voir détails">
                                                        <i class="fa fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('suivi-indicateurs.create', [$reforme->id, $indicateur->id]) }}" 
                                                       class="btn btn-success" title="Ajouter mesure">
                                                        <i class="fa fa-plus"></i>
                                                    </a>
                                                    @if($derniereEvolution)
                                                        <a href="{{ route('suivi-indicateurs.edit', [$reforme->id, $indicateur->id, $derniereEvolution->date_evolution]) }}" 
                                                           class="btn btn-warning" title="Modifier dernière mesure">
                                                            <i class="fa fa-edit"></i>
                                                        </a>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques de synthèse -->
    @if($indicateursAvecDonnees->isNotEmpty())
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-bar-chart"></i> Vue d'ensemble des Indicateurs
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            @foreach($indicateursAvecDonnees->take(4) as $reformeIndicateur)
                                @php
                                    $indicateur = $reformeIndicateur->indicateur;
                                    $statistiques = $reformeIndicateur->getStatistiquesCompletes();
                                @endphp
                                <div class="col-md-3 col-sm-6">
                                    <div class="panel panel-info">
                                        <div class="panel-body text-center">
                                            <h4 class="text-truncate" title="{{ $indicateur->libelle }}">
                                                {{ Str::limit($indicateur->libelle, 20) }}
                                            </h4>
                                            @if($statistiques)
                                                <div class="metric-value">
                                                    <span class="text-primary">
                                                        <strong>{{ number_format($statistiques['valeur_actuelle'], 2, ',', ' ') }}</strong>
                                                    </span>
                                                    <small class="text-muted">{{ $indicateur->unite }}</small>
                                                </div>
                                                <div class="metric-trend">
                                                    {!! $reformeIndicateur->icone_tendance_generale !!}
                                                    <span class="small">{{ $reformeIndicateur->progression }}%</span>
                                                </div>
                                                <div class="metric-stats small text-muted">
                                                    Min: {{ number_format($statistiques['valeur_min'], 1) }} | 
                                                    Max: {{ number_format($statistiques['valeur_max'], 1) }}
                                                </div>
                                            @else
                                                <div class="text-muted">
                                                    <i class="fa fa-exclamation-circle"></i>
                                                    Aucune donnée
                                                </div>
                                            @endif
                                        </div>
                                        <div class="panel-footer text-center">
                                            <a href="{{ route('suivi-indicateurs.show', [$reforme->id, $indicateur->id]) }}" 
                                               class="btn btn-info btn-xs">
                                                <i class="fa fa-eye"></i> Détails
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<style>
.metric-value {
    font-size: 24px;
    margin: 10px 0;
}

.metric-trend {
    margin: 5px 0;
}

.metric-stats {
    margin-top: 10px;
}

.panel-actions {
    float: right;
    margin-top: -5px;
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
@endsection
