<!doctype html>
<html class="no-js" lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Plateforme web de gestion et de suivi des reformes du MCVT</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- favicon
		============================================ -->
    <link rel="shortcut icon" type="image/x-icon" href="img/favicon.ico">
    <!-- Google Fonts
		============================================ -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,700,900" rel="stylesheet">
    <!-- Bootstrap CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/bootstrap.min.css')}}">
    <!-- Bootstrap CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/font-awesome.min.css')}}">
    <!-- owl.carousel CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/owl.carousel.css')}}">
    <link rel="stylesheet" href="{{ asset('css/owl.theme.css')}}">
    <link rel="stylesheet" href="{{ asset('css/owl.transitions.css')}}">
    <!-- animate CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/animate.css')}}">
    <!-- normalize CSS
		============================================ -->
    
    <!-- meanmenu icon CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/meanmenu.min.css')}}">
    <!-- main CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/main.css')}}">
    <!-- educate icon CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/educate-custon-icon.css')}}">
    <!-- morrisjs CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/morrisjs/morris.css')}}">
    <!-- mCustomScrollbar CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/scrollbar/jquery.mCustomScrollbar.min.css')}}">
    <!-- metisMenu CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/metisMenu/metisMenu.min.css')}}">
    <link rel="stylesheet" href="{{ asset('css/metisMenu/metisMenu-vertical.css')}}">
    <!-- calendar CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/calendar/fullcalendar.min.css')}}">
    <link rel="stylesheet" href="{{ asset('css/calendar/fullcalendar.print.min.css')}}">
    <!-- style CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('style.css')}}">
    <!-- responsive CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/responsive.css')}}">
    <!-- x-editor CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/editor/select2.css')}}">
    <link rel="stylesheet" href="{{ asset('css/editor/datetimepicker.css')}}">
    <link rel="stylesheet" href="{{ asset('css/editor/bootstrap-editable.css')}}">
    <link rel="stylesheet" href="{{ asset('css/editor/x-editor-style.css')}}">
    <!-- normalize CSS
		============================================ -->
    <link rel="stylesheet" href="{{ asset('css/data-table/bootstrap-table.css')}}">
    <link rel="stylesheet" href="{{ asset('css/data-table/bootstrap-editable.css')}}">

    <!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<!-- Notifications dynamiques CSS -->
<link rel="stylesheet" href="{{ asset('css/notifications-dynamiques.css')}}">

@livewireStyles

<style>
    /* Styles pour les labels (badges dans Bootstrap 3) */
    .label {
        display: inline;
        padding: .2em .6em .3em;
        font-size: 75%;
        font-weight: 700;
        line-height: 1;
        color: #fff;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: .25em;
    }

    /* Rendre les labels cliquables */
    .label[data-id] {
        cursor: pointer;
    }

    /* Styles pour les options de sélection */
    select option.text-success {
        color: #3c763d;
    }
    
    select option.text-warning {
        color: #8a6d3b;
    }
    
    select option.text-danger {
        color: #a94442;
    }
</style>
</head>

<body>
<!--[if lt IE 8]>
<p class="browserupgrade">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
<![endif]-->
<!-- Start Left menu area -->
<div class="left-sidebar-pro">
    <nav id="sidebar" class="">
        <div class="sidebar-header">
            <a href="{{ url('/') }}"><img class="main-logo" src="{{ asset('img/logo/logomcvt.png') }}" width="200"
            height="60" alt="" /></a>
            <strong><a href="{{ url('/') }}"><img src="{{ asset('img/logo/saut.png') }}" width="45"
            height="38" alt="" /></a></strong>
        </div>
        <div class="left-custom-menu-adp-wrap comment-scrollbar">
            @livewire('menu-component')
        </div>
    </nav>
</div>
<!-- End Left menu area -->
<!-- Start Welcome area -->
<div class="all-content-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="logo-pro">
                    <a href="{{ url('/') }}"><img class="main-logo" src="{{ asset('img/logo/logo.png') }}" alt="" /></a>
                </div>
            </div>
        </div>
    </div>
    <div class="header-advance-area">
        <div class="header-top-area">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="header-top-wraper">
                            <div class="row">
                                <div class="col-lg-1 col-md-0 col-sm-1 col-xs-12">
                                    <div class="menu-switcher-pro">
                                        <button type="button" id="sidebarCollapse" class="btn bar-button-pro header-drl-controller-btn btn-info navbar-btn">
                                            <i class="educate-icon educate-nav"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-7 col-sm-6 col-xs-12">
                                    <div class="header-top-menu tabl-d-n">
                                        <!-- Menu de navigation principal - vide pour l'instant -->
                                    </div>
                                </div>
                                <div class="col-lg-5 col-md-5 col-sm-12 col-xs-12">
                                    <div class="header-right-info">
                                        <ul class="nav navbar-nav mai-top-nav header-right-menu">
                                            
                                            <li class="nav-item"><a href="#" data-toggle="dropdown" role="button" aria-expanded="false" class="nav-link dropdown-toggle" id="notificationDropdown">
                                                <i class="fa fa-bell" style="font-size: 20px; color: aliceblue;"></i>
                                                <span class="indicator-nt" id="notificationCount"></span>
                                            </a>
                                                <div role="menu" class="notification-author dropdown-menu animated zoomIn">
                                                    <div class="notification-single-top">
                                                        <h1>Notifications</h1>
                                                        <button type="button" class="btn btn-sm btn-primary" id="marquerToutesLues">Marquer toutes comme lues</button>
                                                    </div>
                                                    <ul class="notification-menu" id="notificationList">
                                                        <li class="text-center">
                                                                <div class="notification-content">
                                                                <p>Chargement des notifications...</p>
                                                                </div>
                                                        </li>
                                                    </ul>
                                                    <div class="notification-view">
                                                        <a href="#" id="voirToutesNotifications">Voir toutes les notifications</a>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="nav-item header-icon-item">
                                                <a href="{{ route('profile.show') }}" title="Mon profil" class="nav-link">
                                                    <i class="fa fa-user"></i>
                                                </a>
                                                    </li>
                                            <li class="nav-item header-icon-item">
                                                        <form action="{{ route('logout') }}" method="POST" style="display: inline;">
                                                            @csrf
                                                    <button type="submit" title="Déconnexion" class="nav-link logout-btn">
                                                        <i class="fa fa-sign-out" style="font-size: 20px; color: aliceblue;"></i>
                                                            </button>
                                                        </form>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Mobile Menu start -->
        <div class="mobile-menu-area">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="mobile-menu">
                            <nav id="dropdown">
                                <ul class="mobile-menu-nav">
                                    <li><a href="/dashboard">Tableau de bord</a></li>
                                    <li><a href="/reforme">Réformes</a></li>
                                    <li><a href="/typereforme">Types de réformes</a></li>
                                    <li><a href="/indicateur">Indicateurs</a></li>
                                    <li><a href="/activites">Activités</a></li>
                                    <li><a href="/utilisateurs">Utilisateurs</a></li>
                                    <li><a href="/role">Rôles</a></li>
                                    <li>
                                        <form action="{{ route('logout') }}" method="POST" style="display: inline;">
                                            @csrf
                                            <button type="submit" style="background: none; border: none; color: inherit; text-decoration: none; cursor: pointer; padding: 10px 15px; width: 100%; text-align: left;">
                                                Déconnexion
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @yield('content')

    @include('layout.footer')
</div>

<!-- jquery DOIT être chargé en premier
    ============================================ -->
<script src="{{ asset('js/vendor/jquery-1.12.4.min.js') }}"></script>
<!-- bootstrap JS
    ============================================ -->
<script src="{{ asset('js/bootstrap.min.js') }}"></script>

<!-- Select2 JS (après jQuery) -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

@yield('script')

<!-- wow JS
    ============================================ -->
<script src="{{ asset('js/wow.min.js') }}"></script>
<!-- modernizr JS
		============================================ -->
<script src="{{ asset('js/vendor/modernizr-2.8.3.min.js')}}"></script>
<!-- price-slider JS
    ============================================ -->
<script src="{{ asset('js/jquery-price-slider.js') }}"></script>
<!-- meanmenu JS
    ============================================ -->
<script src="{{ asset('js/jquery.meanmenu.js') }}"></script>
<!-- owl.carousel JS
    ============================================ -->
<script src="{{ asset('js/owl.carousel.min.js') }}"></script>
<!-- sticky JS
    ============================================ -->
<script src="{{ asset('js/jquery.sticky.js') }}"></script>
<!-- scrollUp JS
    ============================================ -->
<script src="{{ asset('js/jquery.scrollUp.min.js') }}"></script>
<!-- counterup JS
    ============================================ -->
<script src="{{ asset('js/counterup/jquery.counterup.min.js') }}"></script>
<script src="{{ asset('js/counterup/waypoints.min.js') }}"></script>
<script src="{{ asset('js/counterup/counterup-active.js') }}"></script>
<!-- mCustomScrollbar JS
    ============================================ -->
<script src="{{ asset('js/scrollbar/jquery.mCustomScrollbar.concat.min.js') }}"></script>
<script src="{{ asset('js/scrollbar/mCustomScrollbar-active.js') }}"></script>
<!-- metisMenu JS
    ============================================ -->
<script src="{{ asset('js/metisMenu/metisMenu.min.js') }}"></script>
<script src="{{ asset('js/metisMenu/metisMenu-active.js') }}"></script>
<!-- data table JS
		============================================ -->
<script src="{{ asset('js/data-table/bootstrap-table.js') }}"></script>
<script src="{{ asset('js/data-table/tableExport.js') }}"></script>
<script src="{{ asset('js/data-table/data-table-active.js') }}"></script>
<script src="{{ asset('js/data-table/bootstrap-table-editable.js') }}"></script>
<script src="{{ asset('js/data-table/bootstrap-editable.js') }}"></script>
<script src="{{ asset('js/data-table/bootstrap-table-resizable.js') }}"></script>
<script src="{{ asset('js/data-table/colResizable-1.5.source.js') }}"></script>
<script src="{{ asset('js/data-table/bootstrap-table-export.js') }}"></script>
<!--  editable JS
	============================================ -->
<script src="{{ asset('js/editable/jquery.mockjax.js') }}"></script>
<script src="{{ asset('js/editable/mock-active.js') }}"></script>
<script src="{{ asset('js/editable/select2.js') }}"></script>
<script src="{{ asset('js/editable/moment.min.js') }}"></script>
<script src="{{ asset('js/editable/bootstrap-datetimepicker.js') }}"></script>
<script src="{{ asset('js/editable/bootstrap-editable.js') }}"></script>
<script src="{{ asset('js/editable/xediable-active.js') }}"></script>
<!-- Chart JS
	============================================ -->
<script src="{{ asset('js/chart/jquery.peity.min.js') }}"></script>
<script src="{{ asset('js/peity/peity-active.js') }}"></script>
<!-- tab JS
	============================================ -->
<script src="{{ asset('js/tab.js') }}"></script>

<!-- morrisjs JS
    ============================================ -->
<script src="{{ asset('js/morrisjs/raphael-min.js') }}"></script>
<script src="{{ asset('js/morrisjs/morris.js') }}"></script>
<script src="{{ asset('js/morrisjs/morris-active.js') }}"></script>
<!-- morrisjs JS
    ============================================ -->
<script src="{{ asset('js/sparkline/jquery.sparkline.min.js') }}"></script>
<script src="{{ asset('js/sparkline/jquery.charts-sparkline.js') }}"></script>
<script src="{{ asset('js/sparkline/sparkline-active.js') }}"></script>
<!-- calendar JS
    ============================================ -->
<script src="{{ asset('js/calendar/moment.min.js') }}"></script>
<script src="{{ asset('js/calendar/fullcalendar.min.js') }}"></script>
<script src="{{ asset('js/calendar/fullcalendar-active.js') }}"></script>
<!-- plugins JS
    ============================================ -->
<script src="{{ asset('js/plugins.js') }}"></script>
<!-- main JS
    ============================================ -->
<script src="{{ asset('js/main.js') }}"></script>


@livewireScripts

<script>
$(document).ready(function() {
    // Rendre les labels (badges) de statut cliquables pour afficher les détails
    $('.label[data-id]').click(function() {
        var id = $(this).data('id');
        $('#viewModal' + id).modal('show');
    });

    // Charger les notifications au chargement de la page
    chargerNotifications();

    // Charger les notifications toutes les 30 secondes
    setInterval(chargerNotifications, 30000);

    // Charger les notifications quand on clique sur le dropdown
    $('#notificationDropdown').on('click', function() {
        chargerNotifications();
    });

    // Marquer toutes les notifications comme lues
    $('#marquerToutesLues').on('click', function(e) {
        e.preventDefault();
        marquerToutesLues();
    });

    // Marquer une notification comme lue quand on clique dessus
    $(document).on('click', '.notification-item', function() {
        var notificationId = $(this).data('id');
        marquerNotificationLue(notificationId);
    });
});

function chargerNotifications() {
    $.ajax({
        url: '{{ route("notifications.index") }}',
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success !== false) {
                afficherNotifications(response.notifications || []);
                afficherCompteur(response.nonLues || 0);
            } else {
                console.error('Erreur dans la réponse:', response.message);
                afficherErreurNotifications();
            }
        },
        error: function(xhr, status, error) {
            console.error('Erreur lors du chargement des notifications:', error);
            afficherErreurNotifications();
        }
    });
}

function afficherErreurNotifications() {
    var html = '<li class="text-center"><div class="notification-content"><p style="color: #d9534f;"><i class="fa fa-exclamation-triangle"></i> Erreur de chargement</p></div></li>';
    $('#notificationList').html(html);
    $('#notificationCount').hide();
}

function afficherNotifications(notifications) {
    var html = '';

    if (notifications.length === 0) {
        html = '<li class="text-center"><div class="notification-content"><p>Aucune notification</p></div></li>';
    } else {
        notifications.forEach(function(notification) {
            var statutClass = notification.statut === 'N' ? 'non-lue' : 'lue';
            var iconClass = notification.statut === 'N' ? 'fa fa-bell' : 'fa fa-check-circle';

            html += '<li class="notification-item ' + statutClass + '" data-id="' + notification.id + '">';
            html += '<a href="' + (notification.url || '#') + '">';
            html += '<div class="notification-icon">';
            html += '<i class="' + iconClass + '" aria-hidden="true"></i>';
            html += '</div>';
            html += '<div class="notification-content">';
            html += '<span class="notification-date">' + notification.date_formatee + '</span>';
            html += '<p>' + notification.message + '</p>';
            html += '</div>';
            html += '</a>';
            html += '</li>';
        });
    }

    $('#notificationList').html(html);
}

function afficherCompteur(nonLues) {
    var $compteur = $('#notificationCount');

    if (nonLues > 0) {
        $compteur.text(nonLues).show();
    } else {
        $compteur.hide();
    }
}

function marquerNotificationLue(notificationId) {
    $.ajax({
        url: '/notifications/' + notificationId + '/marquer-lue',
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                // Recharger les notifications pour mettre à jour l'affichage
                chargerNotifications();
            }
        },
        error: function(xhr, status, error) {
            console.error('Erreur lors du marquage de la notification:', error);
        }
    });
}

function marquerToutesLues() {
    // Afficher un indicateur de chargement
    $('#marquerToutesLues').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Traitement...');

    $.ajax({
        url: '{{ route("notifications.marquer-toutes-lues") }}',
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                // Recharger les notifications pour mettre à jour l'affichage
                chargerNotifications();

                // Afficher un message de succès temporaire
                var $btn = $('#marquerToutesLues');
                $btn.html('<i class="fa fa-check"></i> Marquées!').removeClass('btn-primary').addClass('btn-success');

                setTimeout(function() {
                    $btn.html('Marquer toutes comme lues').removeClass('btn-success').addClass('btn-primary').prop('disabled', false);
                }, 2000);
            } else {
                console.error('Erreur:', response.message);
                restaurerBoutonMarquer();
            }
        },
        error: function(xhr, status, error) {
            console.error('Erreur lors du marquage de toutes les notifications:', error);
            restaurerBoutonMarquer();
        }
    });
}

function restaurerBoutonMarquer() {
    $('#marquerToutesLues').html('Marquer toutes comme lues').prop('disabled', false);
}

// Fonction pour actualiser manuellement les notifications
function rafraichirNotifications() {
    chargerNotifications();
}
</script>

<!-- Scripts personnalisés des pages -->
@yield('scripts')
</body>

</html>




