<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Services\PermissionService;
use Illuminate\Support\Facades\Validator;

class RolePermissionController extends Controller
{
    protected $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * Afficher la page de gestion des permissions
     */
    public function index()
    {
        $roles = Role::with('permissionMenus.menu', 'permissionMenus.permission')->get();
        $menus = Menu::orderBy('ordre')->get();
        $permissions = Permission::all();
        
        return view('admin.role-permissions', compact('roles', 'menus', 'permissions'));
    }

    /**
     * Récupérer les permissions d'un rôle via AJAX
     */
    public function getRolePermissions(Role $role)
    {
        $permissions = [];
        $menus = Menu::all();
        
        foreach ($menus as $menu) {
            $menuPermissions = $this->permissionService->getRolePermissionsForMenu($role, $menu);
            $permissions[$menu->id] = $menuPermissions->toArray();
        }
        
        return response()->json([
            'role' => $role,
            'permissions' => $permissions,
            'config' => $this->permissionService->getRoleConfiguration($role->role_name)
        ]);
    }

    /**
     * Mettre à jour les permissions d'un rôle
     */
    public function updateRolePermissions(Request $request, Role $role)
    {
        $validator = Validator::make($request->all(), [
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permission_menu,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Vérifier si c'est un rôle système
            if ($this->permissionService->isSystemRole($role->role_name)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Impossible de modifier les permissions d\'un rôle système'
                ], 403);
            }

            $role->permissionMenus()->sync($request->permissions);
            
            return response()->json([
                'success' => true,
                'message' => "Permissions mises à jour pour le rôle {$role->role_name}"
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Créer un nouveau rôle
     */
    public function createRole(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'role_name' => 'required|string|max:255|unique:role,role_name'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Nom de rôle invalide',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $role = Role::create(['role_name' => $request->role_name]);
            
            // Assigner les permissions de base
            $this->permissionService->assignPermissionsToRole($role);
            
            return response()->json([
                'success' => true,
                'message' => "Rôle '{$request->role_name}' créé avec succès",
                'role' => $role
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la création: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Supprimer un rôle
     */
    public function deleteRole(Role $role)
    {
        try {
            // Vérifier si c'est un rôle système
            if ($this->permissionService->isSystemRole($role->role_name)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Impossible de supprimer un rôle système'
                ], 403);
            }

            // Vérifier si des utilisateurs ont ce rôle
            if ($role->users()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Impossible de supprimer un rôle assigné à des utilisateurs'
                ], 403);
            }

            $roleName = $role->role_name;
            $role->delete();
            
            return response()->json([
                'success' => true,
                'message' => "Rôle '{$roleName}' supprimé avec succès"
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la suppression: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Synchroniser toutes les permissions
     */
    public function syncPermissions()
    {
        try {
            $this->permissionService->createPermissionMenusForAllMenus();
            $this->permissionService->syncAllRolePermissions();
            
            return response()->json([
                'success' => true,
                'message' => 'Permissions synchronisées avec succès'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la synchronisation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupérer la matrice des permissions (pour affichage)
     */
    public function getPermissionMatrix()
    {
        $roles = Role::all();
        $menus = Menu::orderBy('ordre')->get();
        $permissions = Permission::all();
        
        $matrix = [];
        
        foreach ($roles as $role) {
            $matrix[$role->id] = [
                'role' => $role,
                'menus' => []
            ];
            
            foreach ($menus as $menu) {
                $menuPermissions = $this->permissionService->getRolePermissionsForMenu($role, $menu);
                $matrix[$role->id]['menus'][$menu->id] = [
                    'menu' => $menu,
                    'permissions' => $menuPermissions->toArray()
                ];
            }
        }
        
        return response()->json([
            'matrix' => $matrix,
            'available_permissions' => $permissions,
            'system_roles' => config('permissions.system_roles', [])
        ]);
    }
}
