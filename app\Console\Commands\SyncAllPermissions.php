<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Models\PermissionMenu;
use App\Services\PermissionService;

class SyncAllPermissions extends Command
{
    protected $signature = 'permissions:sync-all';
    protected $description = 'Synchronise toutes les permissions selon la configuration';

    public function handle()
    {
        $this->info('🔄 Synchronisation de toutes les permissions...');
        
        $permissionService = new PermissionService();
        
        // 1. Créer les associations permission-menu manquantes
        $this->info('📋 Création des associations permission-menu...');
        $permissionService->createPermissionMenusForAllMenus();
        
        // 2. Synchroniser les permissions de tous les rôles
        $this->info('👥 Synchronisation des permissions des rôles...');
        $permissionService->syncAllRolePermissions();
        
        // 3. Vérifier les résultats
        $this->info('✅ Vérification des résultats...');
        
        $roles = Role::with('permissionMenus')->get();
        foreach ($roles as $role) {
            $permissionCount = $role->permissionMenus->count();
            $this->info("  - {$role->role_name}: {$permissionCount} permissions");
        }
        
        $this->info('🎉 Synchronisation terminée avec succès!');
        
        // Suggérer de tester avec un utilisateur spécifique
        $this->info('💡 Pour tester, utilisez: php artisan permissions:diagnose [user_id]');
    }
}
