<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Models\PermissionMenu;
use App\Services\PermissionService;

class FixAhoRole extends Command
{
    protected $signature = 'permissions:fix-aho';
    protected $description = 'Corrige spécifiquement les permissions du rôle aho';

    public function handle()
    {
        $this->info('🔧 Correction du rôle "aho"...');
        
        // 1. Vérifier/créer le rôle aho
        $ahoRole = Role::firstOrCreate(['role_name' => 'aho']);
        $this->info("✅ Rôle 'aho' vérifié/créé (ID: {$ahoRole->id})");
        
        // 2. Assigner les permissions selon la configuration
        $permissionService = new PermissionService();
        $permissionService->assignPermissionsToRole($ahoRole);
        
        // 3. Vérifier les permissions assignées
        $permissionMenus = $ahoRole->permissionMenus()->with(['menu', 'permission'])->get();
        $this->info("📋 Permissions assignées au rôle 'aho': {$permissionMenus->count()}");
        
        $groupedPermissions = $permissionMenus->groupBy('menu.url');
        foreach ($groupedPermissions as $url => $permissions) {
            $permissionNames = $permissions->pluck('permission.permission_name')->join(', ');
            $this->info("  - {$url}: {$permissionNames}");
        }
        
        // 4. Trouver les utilisateurs avec le rôle aho
        $ahoUsers = User::whereHas('roles', function($query) {
            $query->where('role_name', 'aho');
        })->get();
        
        $this->info("👤 Utilisateurs avec le rôle 'aho': {$ahoUsers->count()}");
        
        foreach ($ahoUsers as $user) {
            $this->info("  - {$user->name} (ID: {$user->id})");
            
            // Tester l'accès aux menus
            $accessibleMenus = $user->getAccessibleMenus();
            $this->info("    Menus accessibles: {$accessibleMenus->count()}");
            
            if ($accessibleMenus->isEmpty()) {
                $this->error("    ❌ PROBLÈME: Aucun menu accessible!");
            } else {
                foreach ($accessibleMenus as $menu) {
                    $this->info("      ✅ {$menu->libelle} ({$menu->url})");
                }
            }
        }
        
        // 5. Tester les permissions spécifiques
        if ($ahoUsers->isNotEmpty()) {
            $testUser = $ahoUsers->first();
            $this->info("\n🧪 Test des permissions pour {$testUser->name}:");
            
            $testUrls = ['/dashboard', '/activites', '/reforme', '/indicateurs'];
            foreach ($testUrls as $url) {
                $canRead = $testUser->hasPermissionForUrl($url, 'Lire');
                $canCreate = $testUser->hasPermissionForUrl($url, 'Créer');
                $status = $canRead ? '✅' : '❌';
                $createStatus = $canCreate ? '✅' : '❌';
                $this->info("  {$status} {$url} (Lire) {$createStatus} (Créer)");
            }
        }
        
        $this->info('🎉 Correction du rôle "aho" terminée!');
    }
}
