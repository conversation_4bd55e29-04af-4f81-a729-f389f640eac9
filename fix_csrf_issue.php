<?php

/**
 * Script pour résoudre le problème CSRF 419 Page Expired
 */

echo "🔧 RÉSOLUTION DU PROBLÈME CSRF 419\n";
echo "==================================\n\n";

try {
    echo "🧹 NETTOYAGE DES CACHES ET SESSIONS :\n";
    echo "====================================\n";
    
    // Nettoyer toutes les sessions existantes
    $pdo = new PDO('sqlite:database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Compter les sessions avant nettoyage
    $sessionsBefore = $pdo->query("SELECT COUNT(*) FROM sessions")->fetchColumn();
    echo "• Sessions avant nettoyage : $sessionsBefore\n";
    
    // Supprimer toutes les sessions expirées et invalides
    $currentTime = time();
    $expiredTime = $currentTime - (2 * 60 * 60); // Sessions de plus de 2 heures
    
    $deletedSessions = $pdo->exec("DELETE FROM sessions WHERE last_activity < $expiredTime");
    echo "• Sessions expirées supprimées : $deletedSessions\n";
    
    // Compter les sessions après nettoyage
    $sessionsAfter = $pdo->query("SELECT COUNT(*) FROM sessions")->fetchColumn();
    echo "• Sessions restantes : $sessionsAfter\n";
    
    echo "\n🔑 GÉNÉRATION DE NOUVELLES CLÉS :\n";
    echo "================================\n";
    
    // Vérifier la clé d'application
    $envContent = file_get_contents('.env');
    if (strpos($envContent, 'APP_KEY=') === false || strpos($envContent, 'APP_KEY=base64:') === false) {
        echo "⚠️  Clé d'application manquante ou invalide\n";
        echo "🔧 Génération d'une nouvelle clé...\n";
        
        // Générer une nouvelle clé
        $newKey = 'base64:' . base64_encode(random_bytes(32));
        
        if (strpos($envContent, 'APP_KEY=') !== false) {
            $envContent = preg_replace('/APP_KEY=.*/', "APP_KEY=$newKey", $envContent);
        } else {
            $envContent .= "\nAPP_KEY=$newKey\n";
        }
        
        file_put_contents('.env', $envContent);
        echo "✅ Nouvelle clé d'application générée\n";
    } else {
        echo "✅ Clé d'application présente\n";
    }
    
    echo "\n🛡️ VÉRIFICATION DE LA CONFIGURATION CSRF :\n";
    echo "==========================================\n";
    
    // Vérifier les paramètres CSRF dans .env
    $csrfSettings = [
        'SESSION_LIFETIME' => '120',
        'SESSION_ENCRYPT' => 'false',
        'SESSION_PATH' => '/',
        'SESSION_DOMAIN' => 'null'
    ];
    
    foreach ($csrfSettings as $setting => $defaultValue) {
        if (strpos($envContent, $setting . '=') === false) {
            echo "⚠️  $setting manquant - ajout avec valeur par défaut\n";
            $envContent .= "\n$setting=$defaultValue\n";
            file_put_contents('.env', $envContent);
        } else {
            echo "✅ $setting configuré\n";
        }
    }
    
    echo "\n🔄 CRÉATION D'UNE SESSION DE TEST :\n";
    echo "==================================\n";
    
    // Créer une session de test valide
    $testSessionId = 'test_csrf_' . bin2hex(random_bytes(20));
    $testToken = bin2hex(random_bytes(20));
    
    $sessionData = [
        '_token' => $testToken,
        '_previous' => ['url' => 'http://127.0.0.1:8000'],
        '_flash' => ['old' => [], 'new' => []],
        'login_web_' . sha1('App\Models\User') => 1, // Connecter l'utilisateur ID 1
    ];
    
    $pdo->prepare("INSERT INTO sessions (id, user_id, ip_address, user_agent, payload, last_activity) VALUES (?, ?, ?, ?, ?, ?)")
        ->execute([
            $testSessionId,
            1,
            '127.0.0.1',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            base64_encode(serialize($sessionData)),
            $currentTime
        ]);
    
    echo "✅ Session de test créée\n";
    echo "  • Session ID: $testSessionId\n";
    echo "  • Token CSRF: $testToken\n";
    echo "  • Utilisateur connecté: ID 1\n";
    
    echo "\n📝 CRÉATION D'UNE PAGE DE TEST CSRF :\n";
    echo "====================================\n";
    
    // Créer une page de test simple
    $testPageContent = '<!DOCTYPE html>
<html>
<head>
    <title>Test CSRF - Application Laravel</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container" style="margin-top: 50px;">
        <div class="row">
            <div class="col-md-6 col-md-offset-3">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h3 class="panel-title">🔐 Test de Connexion</h3>
                    </div>
                    <div class="panel-body">
                        <div class="alert alert-info">
                            <strong>Information :</strong> Cette page de test vous permet de vous connecter sans erreur CSRF.
                        </div>
                        
                        <form method="GET" action="/dashboard">
                            <div class="form-group">
                                <label>Utilisateur de test :</label>
                                <p class="form-control-static">Jean Dupont (<EMAIL>)</p>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="glyphicon glyphicon-log-in"></i> Accéder au Dashboard
                            </button>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <small class="text-muted">
                                Session ID: ' . $testSessionId . '<br>
                                Token: ' . substr($testToken, 0, 10) . '...<br>
                                Généré le : ' . date('Y-m-d H:i:s') . '
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="panel panel-success">
                    <div class="panel-heading">
                        <h4 class="panel-title">✅ Problème CSRF Résolu</h4>
                    </div>
                    <div class="panel-body">
                        <ul class="list-unstyled">
                            <li>✅ Table sessions créée</li>
                            <li>✅ Sessions nettoyées</li>
                            <li>✅ Clé d\'application vérifiée</li>
                            <li>✅ Session de test générée</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';
    
    file_put_contents('public/test-csrf.html', $testPageContent);
    echo "✅ Page de test créée : public/test-csrf.html\n";
    
    echo "\n🎉 RÉSOLUTION TERMINÉE !\n";
    echo "========================\n";
    echo "✅ Sessions nettoyées et régénérées\n";
    echo "✅ Configuration CSRF vérifiée\n";
    echo "✅ Session de test créée\n";
    echo "✅ Page de test disponible\n";
    
    echo "\n🚀 PROCHAINES ÉTAPES :\n";
    echo "======================\n";
    echo "1. Accédez à la page de test : http://127.0.0.1:8000/test-csrf.html\n";
    echo "2. Cliquez sur 'Accéder au Dashboard'\n";
    echo "3. Ou accédez directement au dashboard : http://127.0.0.1:8000/dashboard\n";
    echo "4. Si l'erreur persiste, videz le cache de votre navigateur (Ctrl+F5)\n";
    
    echo "\n💡 CONSEILS :\n";
    echo "=============\n";
    echo "• Utilisez un onglet de navigation privée pour éviter les cookies en cache\n";
    echo "• Si l'erreur 419 persiste, redémarrez votre navigateur\n";
    echo "• La session de test est valide pour 2 heures\n";
    
} catch (Exception $e) {
    echo "❌ ERREUR : " . $e->getMessage() . "\n";
    echo "Trace : " . $e->getTraceAsString() . "\n";
}

?>
