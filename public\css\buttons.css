/*----------------------------------------*/
/*  1.  Button CSS
/*----------------------------------------*/
.btn-mg-b-10{
	margin-bottom:10px;
}
.btn-custon-two, .btn-custon-three{
	border-radius:2px;
}
.btn-custon-four{
	border-radius:0px;
}
.btn-custon-three.btn-default{
	border-bottom:2px solid #ccc;
}
.btn-custon-three.btn-primary{
	border-bottom:2px solid #23547d;
}
.btn-custon-three.btn-success{
	border-bottom:2px solid #38883b;
}
.btn-custon-three.btn-info{
	border-bottom:2px solid #387c90;
}
.btn-custon-three.btn-warning{
	border-bottom:2px solid #a57736;
}
.btn-custon-three.btn-danger{
	border-bottom:2px solid #8a2f2d;
}
.btn-custon-rounded-four{
	border-radius:5px;
}
.btn-custon-rounded-two{
	border-radius:10px;
}
.btn-custon-rounded-three{
	border-radius:20px;
}
.btn-custom-groups .btn{
	border-radius:0px;
}
.btn-custom-groups-one .btn-primary{
	background-color: #fff;
	color:#303030;
	border-color: #ccc;
}
.btn-custom-groups-one .btn-primary:hover, .btn-custom-groups-one .btn-primary:active, .btn-custom-groups-one .btn-primary:focus{
	background-color: #ddd;
	outline:none;
}
.button-down-drop{
	font-size:14px;
}
.btn-dropdown-menu li a{
	font-size:14px;
	padding: 6px 20px;
}
.btn-dropdown-menu li .btn-setting-icon{
	width:100%;
	padding: 10px 16px;
}
.btn-dropdown-menu li .btn-setting-icon .btn-icon-st{
	display:inline-block;
	margin:0px 5px;
}
.btn-dropdown-menu li .btn-setting-icon .btn-icon-st a{
	padding: 5px 10px;
    display: inline-block;
    font-size: 18px;
	background: #204d74;
	color:#fff;
}
.btn-default-bg .btn-dropdown-menu li .btn-setting-icon .btn-icon-st a{
	background: #ddd;
	color:#303030;
}
.btn-default-bg .btn-dropdown-menu li .btn-setting-icon .btn-icon-st a:hover{
	background: #303030;
	color:#fff;
}
.btn-success-bg .btn-dropdown-menu li .btn-setting-icon .btn-icon-st a{
	background: #449d44;
	color:#fff;
}
.btn-success-bg .btn-dropdown-menu li .btn-setting-icon .btn-icon-st a:hover{
	background: #303030;
	color:#fff;
}
.btn-warning-bg .btn-dropdown-menu li .btn-setting-icon .btn-icon-st a{
	background: #f0ad4e;
	color:#fff;
}
.btn-warning-bg .btn-dropdown-menu li .btn-setting-icon .btn-icon-st a:hover{
	background: #303030;
	color:#fff;
}
.btn-danger-bg .btn-dropdown-menu li .btn-setting-icon .btn-icon-st a{
	background: #d9534f;
	color:#fff;
}
.btn-danger-bg .btn-dropdown-menu li .btn-setting-icon .btn-icon-st a:hover{
	background: #303030;
	color:#fff;
}
.btn-dropdown-menu li .btn-setting-icon .btn-icon-st a:hover{
	background: #303030;
}
.btn-default-bg .btn-dropdown-menu li a:hover{
	background: #ddd;
	color:#303030;
}
.btn-success-bg .btn-dropdown-menu li a:hover{
	background: #449d44;
	color:#fff;
}
.btn-warning-bg .btn-dropdown-menu li a:hover{
	background: #f0ad4e;
	color:#fff;
}
.btn-danger-bg .btn-dropdown-menu li a:hover{
	background: #d9534f;
	color:#fff;
}
.btn-dropdown-menu li a:hover{
	background-color: #204d74;
	color:#fff;
}
.dropdown-menu.another-drop-pro-one{
	top:79%;
	left:21%;
}
.another-drop-pro-seven{
	top:79%;
	left:7%;
}
.dropdown-menu.another-drop-pro-three{
	top:79%;
	left:37%;
}
.dropdown-menu.another-drop-pro-eight{
	top:79%;
	left:27%;
}
.dropdown-menu.another-drop-pro-four{
	top:79%;
	left:53%;
}
.dropdown-menu.another-drop-pro-nine{
	top:79%;
	left:48%;
}
.dropdown-menu.another-drop-pro-five{
	top:79%;
	left:70%;
}
.dropdown-menu.another-drop-pro-two, .another-drop-pro-six{
	top:79%;
	left:6%;
}
.button-drop-style-one, .button-drop-style-two{
	display:inline-block;
}
.button-drop-style-one .btn-button-ct{
	padding: 6px 6px;
    margin-top: 0px;
    background: #fff;
	border-right:1px solid #ccc;
	border-left:0px solid #ccc;
	border-top:1px solid #ccc;
	border-bottom:1px solid #ccc;
	vertical-align: middle; 
}
.button-drop-style-one .btn-button-ct.btn-button-primary-ct{
    background: #337ab7;
	border-right:1px solid #337ab7;
	border-left:0px solid #337ab7;
	border-top:1px solid #337ab7;
	border-bottom:1px solid #337ab7;
	color: #fff;
	vertical-align: middle; 
}
.button-drop-style-one .btn-button-ct.btn-button-success-ct{
    background: #398439;
	border-right:1px solid #398439;
	border-left:0px solid #398439;
	border-top:1px solid #398439;
	border-bottom:1px solid #398439;
	color: #fff;
	vertical-align: middle; 
}
.button-drop-style-one .btn-button-ct.btn-button-warning-ct{
    background: #f0ad4e;
	border-right:1px solid #f0ad4e;
	border-left:0px solid #f0ad4e;
	border-top:1px solid #f0ad4e;
	border-bottom:1px solid #f0ad4e;
	color: #fff;
	vertical-align: middle; 
}
.button-drop-style-one .btn-button-ct.btn-button-danger-ct{
    background: #d9534f;
	border-right:1px solid #d9534f;
	border-left:0px solid #d9534f;
	border-top:1px solid #d9534f;
	border-bottom:1px solid #d9534f;
	color: #fff;
	vertical-align: middle; 
}
.dropdown-menu.another-drop-pro-ten{
	top:88%;
	left:7%;
}
.dropdown-menu.another-drop-pro-eleven{
	top:88%;
	left:45%;
}
.dropdown-menu.another-drop-pro-twevel{
	top:88%;
	left:65%;
}
.primary-btn-cl, .warning-btn-cl, .danger-btn-cl{
	background: #fff;
	color: #303030;
	transition: all .4s ease 0s;
}
.social-btn-icon-cl .edu-icon, .social-btn-icon-cl i{
	color:#303030;
}
.social-btn-icon-cl .edu-icon, .social-btn-icon-cl i{
	color:#03a9f4;
}
.social-btn-icon-cl-df .edu-facebook{
	color:#5d82d1;
}
.social-btn-icon-cl-df .edu-twitter{
	color:#50bff5;
}
.social-btn-icon-cl-df .edu-google-plus{
	color:#eb5e4c;
}
.social-btn-icon-cl-df .fa-dribbble{
	color:#f86fa2;
}
.social-btn-icon-cl-df .edu-pinterest{
	color:#e13138;
}
.social-btn-icon-cl-df .edu-linkedin{
	color:#827be9;
}
.social-btn-icon-cl-df .fa-youtube{
	color:#ef4e41;
}
.social-btn-icon-cl-df .fa-skype{
	color:#0065aa;
}
.social-btn-icon-cl-df .fa-dribbble{
	color:#ea4c89;
}
.social-btn-icon-cl-df .fa-edge{
	color:#303030;
}
.social-btn-icon-cl-df .fa-digg{
	color:#000;
}
.social-btn-icon-cl-df .fa-dropbox{
	color:#007ee5;
}
.btn-default.btn-bg-cl-social .edu-icon, .btn-default.btn-bg-cl-social i{
	color:#fff;
}
.btn-default.btn-bg-cl-social, .btn-default.btn-bg-cl-social{
	background:#303030;
}
.btn-default.btn-bg-cl-social:hover{
	background:#006DF0;
	border-color: #006DF0;
}
.btn-default.btn-bg-cl-social-tw .edu-icon, .btn-default.btn-bg-cl-social-tw i{
	color:#fff;
}
.btn-default.btn-bg-cl-facebook-tw{
	background:#5d82d1;
	border-color: #5d82d1;
}
.btn-default.btn-bg-cl-twitter-tw{
	background:#50bff5;
	border-color: #50bff5;
}
.btn-default.btn-bg-cl-google-tw{
	background:#eb5e4c;
	border-color: #eb5e4c;
}
.btn-default.btn-bg-cl-pinterest-tw{
	background:#e13138;
	border-color: #e13138;
}
.btn-default.btn-bg-cl-linkedin-tw{
	background:#827be9;
	border-color: #827be9;
}
.btn-default.btn-bg-cl-youtube-tw{
	background:#ef4e41;
	border-color: #ef4e41;
}
.btn-default.btn-bg-cl-dropbox-tw{
	background:#007ee5;
	border-color: #007ee5;
}
.btn-default.btn-bg-cl-digg-tw{
	background:#000;
	border-color: #000;
}
.btn-default.btn-bg-cl-dribbble-tw{
	background:#ea4c89;
	border-color: #ea4c89;
}
.btn-default.btn-bg-cl-edge-tw{
	background:#303030;
	border-color: #303030;
}
.btn-default.btn-bg-cl-skype-tw{
	background:#0065aa;
	border-color: #0065aa;
}
.btn-default.btn-bg-cl-facebook-tw .edu-icon, .btn-default.btn-bg-cl-twitter-tw .edu-icon, .btn-default.btn-bg-cl-google-tw .edu-icon, .btn-default.btn-bg-cl-pinterest-tw .edu-icon, .btn-default.btn-bg-cl-linkedin-tw .edu-icon, .btn-default.btn-bg-cl-youtube-tw i, .btn-default.btn-bg-cl-dropbox-tw i, .btn-default.btn-bg-cl-digg-tw i, .btn-default.btn-bg-cl-dribbble-tw i, .btn-default.btn-bg-cl-edge-tw i, .btn-default.btn-bg-cl-skype-tw i{
	color:#fff;
}