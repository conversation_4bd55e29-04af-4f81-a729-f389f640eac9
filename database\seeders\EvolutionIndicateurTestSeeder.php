<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Indicateur;
use App\Models\Reforme;
use App\Models\ReformeIndicateur;
use App\Models\EvolutionIndicateur;
use Carbon\Carbon;

class EvolutionIndicateurTestSeeder extends Seeder
{
    /**
     * Génère des données de test pour le suivi des indicateurs
     */
    public function run()
    {
        $this->command->info('🔄 Génération des données de test pour le suivi des indicateurs...');

        // Vérifier qu'on a des indicateurs et des réformes
        $indicateurs = Indicateur::all();
        $reformes = Reforme::all();

        if ($indicateurs->isEmpty() || $reformes->isEmpty()) {
            $this->command->warn('⚠️  Aucun indicateur ou réforme trouvé. Création de données de base...');
            $this->createBaseData();
            $indicateurs = Indicateur::all();
            $reformes = Reforme::all();
        }

        // Créer des associations réforme-indicateur si elles n'existent pas
        $this->createReformeIndicateurAssociations($reformes, $indicateurs);

        // Générer des évolutions pour les 6 derniers mois
        $this->generateEvolutions();

        $this->command->info('✅ Données de test générées avec succès !');
        $this->displayStatistics();
    }

    /**
     * Crée des données de base si nécessaire
     */
    private function createBaseData()
    {
        // Créer quelques indicateurs de test
        $indicateursData = [
            [
                'libelle' => 'Taux de satisfaction citoyenne',
                'description' => 'Pourcentage de satisfaction des citoyens concernant les services publics',
                'unite' => '%',
                'type' => 'Quantitatif',
                'frequence_collecte' => 'Mensuelle'
            ],
            [
                'libelle' => 'Délai moyen de traitement',
                'description' => 'Temps moyen de traitement des dossiers administratifs',
                'unite' => 'jours',
                'type' => 'Quantitatif',
                'frequence_collecte' => 'Hebdomadaire'
            ],
            [
                'libelle' => 'Nombre de bénéficiaires',
                'description' => 'Nombre total de bénéficiaires des programmes de réforme',
                'unite' => 'personnes',
                'type' => 'Quantitatif',
                'frequence_collecte' => 'Mensuelle'
            ],
            [
                'libelle' => 'Budget utilisé',
                'description' => 'Pourcentage du budget alloué effectivement utilisé',
                'unite' => '%',
                'type' => 'Quantitatif',
                'frequence_collecte' => 'Mensuelle'
            ],
            [
                'libelle' => 'Taux de conformité',
                'description' => 'Pourcentage de conformité aux standards établis',
                'unite' => '%',
                'type' => 'Quantitatif',
                'frequence_collecte' => 'Trimestrielle'
            ]
        ];

        foreach ($indicateursData as $data) {
            Indicateur::firstOrCreate(['libelle' => $data['libelle']], $data);
        }

        // Créer quelques réformes de test si nécessaire
        if (Reforme::count() < 3) {
            $reformesData = [
                [
                    'titre' => 'Modernisation des Services Publics',
                    'description' => 'Réforme visant à moderniser et digitaliser les services publics',
                    'date_debut' => Carbon::now()->subMonths(8),
                    'date_fin' => Carbon::now()->addMonths(4),
                    'type_reforme' => 1,
                    'statut_manuel' => 'En cours'
                ],
                [
                    'titre' => 'Amélioration de la Gouvernance',
                    'description' => 'Réforme pour améliorer la transparence et l\'efficacité gouvernementale',
                    'date_debut' => Carbon::now()->subMonths(6),
                    'date_fin' => Carbon::now()->addMonths(6),
                    'type_reforme' => 1,
                    'statut_manuel' => 'En cours'
                ],
                [
                    'titre' => 'Réforme Éducative',
                    'description' => 'Modernisation du système éducatif national',
                    'date_debut' => Carbon::now()->subMonths(12),
                    'date_fin' => Carbon::now()->subMonths(2),
                    'type_reforme' => 1,
                    'statut_manuel' => 'Achevé'
                ]
            ];

            foreach ($reformesData as $data) {
                Reforme::firstOrCreate(['titre' => $data['titre']], $data);
            }
        }
    }

    /**
     * Crée des associations réforme-indicateur
     */
    private function createReformeIndicateurAssociations($reformes, $indicateurs)
    {
        $this->command->info('📊 Création des associations réforme-indicateur...');

        foreach ($reformes as $reforme) {
            // Associer 2-4 indicateurs aléatoires à chaque réforme
            $nombreIndicateurs = rand(2, min(4, $indicateurs->count()));
            $indicateursSelectionnes = $indicateurs->random($nombreIndicateurs);

            foreach ($indicateursSelectionnes as $indicateur) {
                ReformeIndicateur::firstOrCreate([
                    'reforme_id' => $reforme->id,
                    'indicateur_id' => $indicateur->id
                ], [
                    'valeur_cible' => $this->generateTargetValue($indicateur->unite),
                    'date_debut_suivi' => $reforme->date_debut,
                    'date_fin_suivi' => $reforme->date_fin,
                    'responsable' => 'Équipe de suivi',
                    'commentaires' => 'Association créée automatiquement pour les tests'
                ]);
            }
        }
    }

    /**
     * Génère des évolutions pour les associations existantes
     */
    private function generateEvolutions()
    {
        $this->command->info('📈 Génération des évolutions d\'indicateurs...');

        $associations = ReformeIndicateur::with(['reforme', 'indicateur'])->get();

        foreach ($associations as $association) {
            $this->generateEvolutionForAssociation($association);
        }
    }

    /**
     * Génère des évolutions pour une association spécifique
     */
    private function generateEvolutionForAssociation($association)
    {
        $startDate = Carbon::now()->subMonths(6);
        $endDate = Carbon::now();
        $currentDate = $startDate->copy();

        // Valeur de base selon l'unité
        $baseValue = $this->getBaseValue($association->indicateur->unite);
        $currentValue = $baseValue;

        // Générer une évolution réaliste
        while ($currentDate <= $endDate) {
            // Variation aléatoire mais réaliste
            $variation = $this->generateRealisticVariation($association->indicateur->unite, $currentValue);
            $currentValue += $variation;

            // S'assurer que les valeurs restent dans des limites réalistes
            $currentValue = $this->constrainValue($currentValue, $association->indicateur->unite);

            EvolutionIndicateur::create([
                'reforme_indicateur_id' => $association->id,
                'date_evolution' => $currentDate->copy(),
                'valeur' => round($currentValue, 2),
                'commentaires' => 'Données générées automatiquement pour test',
                'source' => 'Système de test',
                'created_at' => $currentDate->copy(),
                'updated_at' => $currentDate->copy()
            ]);

            // Avancer selon la fréquence
            $currentDate = $this->advanceDate($currentDate, $association->indicateur->frequence_collecte);
        }
    }

    /**
     * Génère une valeur cible selon l'unité
     */
    private function generateTargetValue($unite)
    {
        switch ($unite) {
            case '%':
                return rand(70, 95);
            case 'jours':
                return rand(5, 30);
            case 'personnes':
                return rand(1000, 50000);
            default:
                return rand(50, 200);
        }
    }

    /**
     * Obtient une valeur de base selon l'unité
     */
    private function getBaseValue($unite)
    {
        switch ($unite) {
            case '%':
                return rand(40, 60);
            case 'jours':
                return rand(15, 45);
            case 'personnes':
                return rand(500, 5000);
            default:
                return rand(20, 100);
        }
    }

    /**
     * Génère une variation réaliste
     */
    private function generateRealisticVariation($unite, $currentValue)
    {
        $maxVariation = match ($unite) {
            '%' => 5,
            'jours' => 3,
            'personnes' => $currentValue * 0.1,
            default => $currentValue * 0.05
        };

        return (rand(-100, 100) / 100) * $maxVariation;
    }

    /**
     * Contraint les valeurs dans des limites réalistes
     */
    private function constrainValue($value, $unite)
    {
        return match ($unite) {
            '%' => max(0, min(100, $value)),
            'jours' => max(1, min(365, $value)),
            'personnes' => max(0, $value),
            default => max(0, $value)
        };
    }

    /**
     * Avance la date selon la fréquence
     */
    private function advanceDate($date, $frequence)
    {
        return match ($frequence) {
            'Hebdomadaire' => $date->addWeek(),
            'Mensuelle' => $date->addMonth(),
            'Trimestrielle' => $date->addMonths(3),
            default => $date->addMonth()
        };
    }

    /**
     * Affiche les statistiques finales
     */
    private function displayStatistics()
    {
        $stats = [
            'Indicateurs' => Indicateur::count(),
            'Réformes' => Reforme::count(),
            'Associations Réforme-Indicateur' => ReformeIndicateur::count(),
            'Évolutions générées' => EvolutionIndicateur::count(),
            'Évolutions récentes (30j)' => EvolutionIndicateur::where('created_at', '>=', Carbon::now()->subDays(30))->count()
        ];

        $this->command->info('📊 Statistiques finales :');
        foreach ($stats as $label => $count) {
            $this->command->line("   • $label: $count");
        }
    }
}
