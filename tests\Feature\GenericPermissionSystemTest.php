<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Services\PermissionService;
use Illuminate\Support\Facades\Hash;

class GenericPermissionSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $permissionService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->permissionService = new PermissionService();
        
        // Créer les données de base
        $this->seedBasicData();
    }

    private function seedBasicData()
    {
        // Créer les permissions de base
        $permissions = ['Créer', 'Lire', 'Modifier', 'Supprimer'];
        foreach ($permissions as $permission) {
            Permission::create(['permission_name' => $permission]);
        }

        // Créer quelques menus de test
        $menus = [
            ['libelle' => 'Activités', 'url' => '/activites', 'ordre' => 1],
            ['libelle' => 'Réformes', 'url' => '/reforme', 'ordre' => 2],
            ['libelle' => 'Dashboard', 'url' => '/dashboard', 'ordre' => 3],
        ];
        
        foreach ($menus as $menu) {
            Menu::create($menu);
        }

        // Créer des rôles de test
        $roles = ['Administrateur', 'Gestionnaire', 'Utilisateur', 'TestRole'];
        foreach ($roles as $role) {
            Role::create(['role_name' => $role]);
        }

        // Initialiser les permission_menus et assigner les permissions
        $this->permissionService->createPermissionMenusForAllMenus();
        $this->permissionService->syncAllRolePermissions();
    }

    /** @test */
    public function it_can_create_users_for_any_role_dynamically()
    {
        $roles = Role::all();
        
        foreach ($roles as $role) {
            $user = User::create([
                'nom' => 'Test',
                'prenom' => $role->role_name,
                'email' => strtolower($role->role_name) . '@test.com',
                'password' => Hash::make('password'),
            ]);
            
            $user->roles()->attach($role->id);
            
            $this->assertDatabaseHas('users', [
                'email' => strtolower($role->role_name) . '@test.com'
            ]);
            
            $this->assertTrue($user->hasRoleByName($role->role_name));
        }
    }

    /** @test */
    public function it_assigns_permissions_based_on_configuration()
    {
        $adminRole = Role::where('role_name', 'Administrateur')->first();
        $userRole = Role::where('role_name', 'Utilisateur')->first();
        
        // L'administrateur devrait avoir toutes les permissions
        $adminPermissions = $adminRole->permissionMenus()->count();
        $totalPossiblePermissions = Menu::count() * Permission::count();
        
        $this->assertEquals($totalPossiblePermissions, $adminPermissions);
        
        // L'utilisateur devrait avoir des permissions limitées selon la configuration
        $userPermissions = $userRole->permissionMenus()->count();
        $this->assertLessThan($adminPermissions, $userPermissions);
    }

    /** @test */
    public function it_works_with_generic_permission_methods()
    {
        $roles = Role::all();
        $menus = Menu::all();
        $permissions = Permission::all();
        
        foreach ($roles as $role) {
            $user = User::create([
                'nom' => 'Test',
                'prenom' => $role->role_name,
                'email' => 'test_' . strtolower($role->role_name) . '@example.com',
                'password' => Hash::make('password'),
            ]);
            
            $user->roles()->attach($role->id);
            
            foreach ($menus as $menu) {
                // Tester les méthodes génériques
                $canAccessMenu = $user->canAccessMenu($menu->id);
                $this->assertIsBool($canAccessMenu);
                
                $permissionsForMenu = $user->getPermissionsForMenu($menu->id);
                $this->assertIsArray($permissionsForMenu);
                
                $permissionsForUrl = $user->getPermissionsForUrl($menu->url);
                $this->assertIsArray($permissionsForUrl);
                
                foreach ($permissions as $permission) {
                    $hasPermission = $user->hasPermission($menu->id, $permission->permission_name);
                    $this->assertIsBool($hasPermission);
                    
                    $canPermission = $user->can($permission->permission_name, $menu->id, $menu->url);
                    $this->assertIsBool($canPermission);
                }
            }
        }
    }

    /** @test */
    public function it_handles_new_roles_automatically()
    {
        // Créer un nouveau rôle
        $newRole = Role::create(['role_name' => 'NouveauRole']);
        
        // Assigner les permissions automatiquement
        $this->permissionService->assignPermissionsToRole($newRole);
        
        // Vérifier que le rôle a des permissions
        $this->assertGreaterThan(0, $newRole->permissionMenus()->count());
        
        // Créer un utilisateur avec ce nouveau rôle
        $user = User::create([
            'nom' => 'Nouveau',
            'prenom' => 'Utilisateur',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
        
        $user->roles()->attach($newRole->id);
        
        // Tester que les méthodes génériques fonctionnent
        $this->assertTrue($user->hasRoleByName('NouveauRole'));
        $this->assertIsArray($user->getRoleNames());
        $this->assertContains('NouveauRole', $user->getRoleNames());
    }

    /** @test */
    public function it_respects_system_roles_configuration()
    {
        $systemRoles = config('permissions.system_roles', []);
        
        foreach ($systemRoles as $systemRoleName) {
            $systemRole = Role::where('role_name', $systemRoleName)->first();
            
            if ($systemRole) {
                $this->assertTrue($this->permissionService->isSystemRole($systemRoleName));
            }
        }
        
        // Tester qu'un rôle non-système n'est pas considéré comme système
        $this->assertFalse($this->permissionService->isSystemRole('TestRole'));
    }

    /** @test */
    public function it_provides_consistent_permission_checking()
    {
        $user = User::create([
            'nom' => 'Test',
            'prenom' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
        
        $role = Role::where('role_name', 'Utilisateur')->first();
        $user->roles()->attach($role->id);
        
        $menu = Menu::first();
        
        // Les différentes méthodes de vérification devraient être cohérentes
        $hasReadPermission1 = $user->hasPermission($menu->id, 'Lire');
        $hasReadPermission2 = $user->can('Lire', $menu->id, $menu->url);
        $hasReadPermission3 = $user->canReadForUrl($menu->url);
        
        // Toutes les méthodes devraient donner le même résultat
        $this->assertEquals($hasReadPermission1, $hasReadPermission2);
        $this->assertEquals($hasReadPermission1, $hasReadPermission3);
    }

    /** @test */
    public function it_handles_configuration_changes_dynamically()
    {
        // Simuler un changement de configuration
        $originalConfig = config('permissions.default_role_permissions');
        
        // Modifier temporairement la configuration
        config(['permissions.default_role_permissions.TestRole' => [
            'permissions' => ['Lire']
        ]]);
        
        $testRole = Role::where('role_name', 'TestRole')->first();
        
        // Re-synchroniser les permissions
        $this->permissionService->assignPermissionsToRole($testRole);
        
        // Vérifier que les permissions ont été mises à jour
        $readPermissions = $testRole->permissionMenus()
            ->whereHas('permission', function($q) {
                $q->where('permission_name', 'Lire');
            })->count();
            
        $this->assertGreaterThan(0, $readPermissions);
        
        // Restaurer la configuration originale
        config(['permissions.default_role_permissions' => $originalConfig]);
    }
}
