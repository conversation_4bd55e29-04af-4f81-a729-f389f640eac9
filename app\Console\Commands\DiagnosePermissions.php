<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Models\PermissionMenu;
use App\Services\PermissionService;

class DiagnosePermissions extends Command
{
    protected $signature = 'permissions:diagnose
                            {--user= : ID de l\'utilisateur à diagnostiquer}
                            {--role= : Nom du rôle à diagnostiquer}
                            {--fix : Corriger automatiquement les problèmes détectés}
                            {--sync : Synchroniser toutes les permissions}';
    protected $description = 'Diagnostique et corrige les problèmes de permissions dans le système';

    public function handle()
    {
        $userId = $this->argument('user_id');
        
        if ($userId) {
            $this->diagnoseUser($userId);
        } else {
            $this->diagnoseSystem();
        }
    }

    private function diagnoseUser($userId)
    {
        $user = User::find($userId);
        if (!$user) {
            $this->error("Utilisateur avec ID {$userId} non trouvé");
            return;
        }

        $this->info("=== DIAGNOSTIC POUR L'UTILISATEUR: {$user->name} (ID: {$user->id}) ===");
        
        // Vérifier les rôles
        $roles = $user->roles;
        $this->info("Rôles assignés: " . $roles->pluck('role_name')->join(', '));
        
        if ($roles->isEmpty()) {
            $this->error("❌ PROBLÈME: L'utilisateur n'a aucun rôle assigné!");
            return;
        }

        // Vérifier les menus accessibles
        $this->info("\n--- MENUS ACCESSIBLES ---");
        $accessibleMenus = $user->getAccessibleMenus();
        
        if ($accessibleMenus->isEmpty()) {
            $this->error("❌ PROBLÈME: Aucun menu accessible trouvé!");
            $this->info("Vérification des associations rôle-permission-menu...");
            
            foreach ($roles as $role) {
                $permissionMenus = $role->permissionMenus;
                $this->info("Rôle '{$role->role_name}' a {$permissionMenus->count()} associations permission-menu");
                
                if ($permissionMenus->isEmpty()) {
                    $this->error("❌ Le rôle '{$role->role_name}' n'a aucune permission assignée!");
                }
            }
        } else {
            foreach ($accessibleMenus as $menu) {
                $this->info("✅ {$menu->libelle} ({$menu->url})");
            }
        }

        // Vérifier les permissions spécifiques
        $this->info("\n--- PERMISSIONS SPÉCIFIQUES ---");
        $testUrls = ['/dashboard', '/activites', '/reforme', '/indicateurs'];
        
        foreach ($testUrls as $url) {
            $this->info("URL: {$url}");
            $permissions = ['Créer', 'Lire', 'Modifier', 'Supprimer'];
            
            foreach ($permissions as $permission) {
                $hasPermission = $user->hasPermissionForUrl($url, $permission);
                $status = $hasPermission ? '✅' : '❌';
                $this->info("  {$status} {$permission}");
            }
        }
    }

    private function diagnoseSystem()
    {
        $this->info("=== DIAGNOSTIC DU SYSTÈME DE PERMISSIONS ===");
        
        // Vérifier les tables de base
        $rolesCount = Role::count();
        $menusCount = Menu::count();
        $permissionsCount = Permission::count();
        $permissionMenusCount = PermissionMenu::count();
        
        $this->info("Rôles: {$rolesCount}");
        $this->info("Menus: {$menusCount}");
        $this->info("Permissions: {$permissionsCount}");
        $this->info("Associations Permission-Menu: {$permissionMenusCount}");
        
        // Vérifier les rôles sans permissions
        $this->info("\n--- RÔLES SANS PERMISSIONS ---");
        $roles = Role::doesntHave('permissionMenus')->get();
        
        if ($roles->isNotEmpty()) {
            foreach ($roles as $role) {
                $this->error("❌ Rôle '{$role->role_name}' n'a aucune permission");
            }
        } else {
            $this->info("✅ Tous les rôles ont des permissions");
        }
        
        // Vérifier les menus sans permissions
        $this->info("\n--- MENUS SANS PERMISSIONS ---");
        $menus = Menu::doesntHave('permissionMenus')->get();
        
        if ($menus->isNotEmpty()) {
            foreach ($menus as $menu) {
                $this->error("❌ Menu '{$menu->libelle}' ({$menu->url}) n'a aucune permission");
            }
        } else {
            $this->info("✅ Tous les menus ont des permissions");
        }

        // Proposer des corrections
        $this->info("\n--- CORRECTIONS SUGGÉRÉES ---");
        
        if ($roles->isNotEmpty()) {
            $this->info("Pour corriger les rôles sans permissions:");
            $this->info("php artisan permissions:sync-all");
        }
        
        if ($menus->isNotEmpty()) {
            $this->info("Pour créer les associations permission-menu manquantes:");
            $this->info("php artisan permissions:create-associations");
        }
    }
}
