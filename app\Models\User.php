<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use Notifiable;

    protected $table = 'users';

    protected $fillable = [
        'personne_id',
        'pwd',
        'status',
    ];

    protected $hidden = [
        'pwd',
    ];

    // Utiliser le bon champ de mot de passe
    public function getAuthPassword()
    {
        return $this->pwd;
    }

    public function personne()
    {
        return $this->belongsTo(Personne::class, 'personne_id');
    }

    /**
     * Retourne la personne associée ou lance une exception si absente.
     */
    public function personneOrFail()
    {
        if (!$this->personne) {
            throw new \Exception('Personne liée introuvable pour l\'utilisateur ID=' . $this->id);
        }
        return $this->personne;
    }

    public function roles()
    {
        return $this->belongsToMany(Role::class, 'user_role', 'id_user', 'role_id');
    }

    /**
     * Vérifie si l'utilisateur a un rôle spécifique
     */
    public function hasRole($roleName)
    {
        return $this->roles()->where('role_name', $roleName)->exists();
    }

    /**
     * Vérifie si l'utilisateur a l'une des rôles spécifiés
     */
    public function hasAnyRole($roles)
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        return $this->roles()->whereIn('role_name', $roles)->exists();
    }

    /**
     * Vérifie si l'utilisateur a tous les rôles spécifiés
     */
    public function hasAllRoles($roles)
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        return $this->roles()->whereIn('role_name', $roles)->count() === count($roles);
    }

    /**
     * Vérifie si l'utilisateur a une permission spécifique sur un menu
     */
    public function hasPermission($menuId, $permissionName)
    {
        return $this->roles()
            ->whereHas('permissionMenus', function ($query) use ($menuId, $permissionName) {
                $query->where('menu_id', $menuId)
                      ->whereHas('permission', function ($subQuery) use ($permissionName) {
                          $subQuery->where('permission_name', $permissionName);
                      });
            })
            ->exists();
    }

    /**
     * Vérifie si l'utilisateur peut accéder à un menu
     */
    public function canAccessMenu($menuId)
    {
        return $this->roles()
            ->whereHas('permissionMenus', function ($query) use ($menuId) {
                $query->where('menu_id', $menuId);
            })
            ->exists();
    }

    /**
     * Récupère tous les menus accessibles par l'utilisateur
     */
    public function getAccessibleMenus()
    {
        return \App\Models\Menu::whereHas('permissionMenus', function ($query) {
            $query->whereHas('roles', function ($roleQuery) {
                $roleQuery->whereIn('role.id', $this->roles()->pluck('role.id'));
            });
        })->where('is_active', true)->orderBy('ordre')->get();
    }

    /**
     * Vérifie si l'utilisateur a une permission spécifique (par nom de permission)
     */
    public function hasPermissionByName($permissionName)
    {
        return $this->roles()
            ->whereHas('permissionMenus.permission', function ($query) use ($permissionName) {
                $query->where('permission_name', $permissionName);
            })
            ->exists();
    }

    /**
     * Récupère toutes les permissions de l'utilisateur
     */
    public function getAllPermissions()
    {
        return \App\Models\Permission::whereHas('permissionMenus.roles', function ($query) {
            $query->whereIn('role.id', $this->roles()->pluck('role.id'));
        })->get();
    }

    // Les autres relations sessions...
}
