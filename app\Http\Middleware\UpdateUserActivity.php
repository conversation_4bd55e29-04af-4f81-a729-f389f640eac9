<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Session;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class UpdateUserActivity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Mettre à jour l'activité de l'utilisateur connecté
        if (Auth::check()) {
            $this->updateUserActivity($request);
        }

        return $next($request);
    }

    /**
     * Met à jour l'activité de l'utilisateur dans la table des sessions
     */
    private function updateUserActivity(Request $request): void
    {
        try {
            $user = Auth::user();
            $sessionId = session()->getId();

            // Mettre à jour la dernière activité de la session courante
            Session::where('user_id', $user->id)
                   ->where('session_id', $sessionId)
                   ->where('status', 'active')
                   ->update([
                       'last_activity' => Carbon::now()
                   ]);

            // Marquer les sessions inactives (plus de 30 minutes sans activité)
            // Exécuter cette opération seulement occasionnellement pour éviter la surcharge
            if (rand(1, 100) <= 1) { // 1% de chance
                Session::markInactiveSessions(0.5); // 30 minutes = 0.5 heure
            }

        } catch (\Exception $e) {
            // Log l'erreur mais ne pas interrompre la requête
            \Log::error('Erreur lors de la mise à jour de l\'activité utilisateur', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);
        }
    }
}
