<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class MenuComponent extends Component
{
    public $menus = []; // Variable pour stocker les menus

    public function mount()
    {
        // Récupère les menus selon les permissions de l'utilisateur connecté
        if (Auth::check()) {
            $user = Auth::user();
            $this->menus = $user->getAccessibleMenus();
        } else {
            // Si l'utilisateur n'est pas connecté, aucun menu n'est affiché
            $this->menus = collect();
        }
    }

    public function render()
    {
        return view('livewire.menu-component');
    }
}
