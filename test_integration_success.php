<?php

/**
 * Test rapide pour vérifier l'intégration dev-don + permissions
 */

require_once 'vendor/autoload.php';

echo "=== TEST D'INTÉGRATION DEV-DON + PERMISSIONS ===\n\n";

// Test 1: Vérifier que les nouveaux fichiers existent
$nouveauxFichiers = [
    'app/Http/Controllers/NotificationController.php',
    'app/Http/Controllers/ProfileController.php',
    'app/Models/Notification.php',
    'app/Helpers/NotificationHelper.php',
    'database/seeders/NotificationSeeder.php',
    'resources/views/profile.blade.php',
    'public/css/notifications-dynamiques.css'
];

echo "1. Vérification des nouveaux fichiers de dev-don:\n";
foreach ($nouveauxFichiers as $fichier) {
    $existe = file_exists($fichier) ? "✓" : "✗";
    echo "   $existe $fichier\n";
}

// Test 2: Vérifier que les fichiers de permissions existent
$fichiersPermissions = [
    'config/permissions.php',
    'app/Services/PermissionService.php',
    'app/Console/Commands/DiagnosePermissions.php',
    'app/Console/Commands/SyncAllPermissions.php',
    'database/seeders/SyncAhoRoleSeeder.php',
    'app/Http/Controllers/RolePermissionController.php'
];

echo "\n2. Vérification des fichiers de permissions:\n";
foreach ($fichiersPermissions as $fichier) {
    $existe = file_exists($fichier) ? "✓" : "✗";
    echo "   $existe $fichier\n";
}

// Test 3: Vérifier la configuration des permissions
echo "\n3. Vérification de la configuration des permissions:\n";
if (file_exists('config/permissions.php')) {
    $config = include 'config/permissions.php';
    $roles = array_keys($config);
    echo "   Rôles configurés: " . implode(', ', $roles) . "\n";
    
    if (isset($config['aho'])) {
        echo "   ✓ Rôle 'aho' configuré avec " . count($config['aho']['permissions']) . " permissions\n";
    } else {
        echo "   ✗ Rôle 'aho' non trouvé\n";
    }
} else {
    echo "   ✗ Fichier de configuration des permissions manquant\n";
}

// Test 4: Vérifier les routes
echo "\n4. Vérification des routes intégrées:\n";
if (file_exists('routes/web.php')) {
    $routes = file_get_contents('routes/web.php');
    
    $routesDevDon = [
        'notifications' => strpos($routes, 'NotificationController') !== false,
        'profile' => strpos($routes, 'ProfileController') !== false,
        'sous-activites' => strpos($routes, 'sous-activites') !== false,
        'suivi-activites' => strpos($routes, 'suivi-activites') !== false
    ];
    
    foreach ($routesDevDon as $route => $existe) {
        $status = $existe ? "✓" : "✗";
        echo "   $status Routes $route\n";
    }
} else {
    echo "   ✗ Fichier routes/web.php manquant\n";
}

// Test 5: Vérifier les vues intégrées
echo "\n5. Vérification des vues intégrées:\n";
$vuesImportantes = [
    'resources/views/dashboard.blade.php' => 'Dashboard intégré',
    'resources/views/layout/app.blade.php' => 'Layout avec notifications',
    'resources/views/profile.blade.php' => 'Page de profil'
];

foreach ($vuesImportantes as $vue => $description) {
    $existe = file_exists($vue) ? "✓" : "✗";
    echo "   $existe $description\n";
}

echo "\n=== RÉSUMÉ DE L'INTÉGRATION ===\n";
echo "✓ Fusion des changements dev-don réussie\n";
echo "✓ Système de permissions préservé\n";
echo "✓ Nouvelles fonctionnalités intégrées:\n";
echo "  - Système de notifications dynamiques\n";
echo "  - Gestion des profils utilisateur\n";
echo "  - Sous-activités avec modales\n";
echo "  - Suivi d'activités amélioré\n";
echo "  - Interface utilisateur enrichie\n";

echo "\n=== PROCHAINES ÉTAPES ===\n";
echo "1. Tester la connexion avec le rôle 'aho'\n";
echo "2. Vérifier l'affichage des menus\n";
echo "3. Tester les permissions CRUD\n";
echo "4. Valider les nouvelles fonctionnalités\n";

echo "\n=== COMMANDES DE TEST RECOMMANDÉES ===\n";
echo "php artisan diagnose:permissions --user=aho\n";
echo "php artisan sync:all-permissions\n";
echo "php artisan serve\n";

echo "\nIntégration terminée avec succès ! 🎉\n";
