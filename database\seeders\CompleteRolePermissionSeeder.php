<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Models\PermissionMenu;
use App\Models\RolePermissionMenu;
use App\Services\PermissionService;

class CompleteRolePermissionSeeder extends Seeder
{
    protected $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Log::info('=== DÉBUT DU SEEDER COMPLET RÔLES ET PERMISSIONS ===');

        DB::beginTransaction();

        try {
            // 1. Créer les permissions de base
            $this->createBasePermissions();

            // 2. C<PERSON>er ou mettre à jour les menus
            $this->createOrUpdateMenus();

            // 3. Créer les associations permission-menu
            $this->createPermissionMenuAssociations();

            // 4. <PERSON><PERSON>er les rôles selon la configuration
            $this->createRolesFromConfig();

            // 5. Assigner les permissions aux rôles
            $this->assignPermissionsToRoles();

            // 6. Vérifier et corriger les incohérences
            $this->verifyAndFixInconsistencies();

            DB::commit();
            Log::info('=== SEEDER COMPLET TERMINÉ AVEC SUCCÈS ===');

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Erreur lors du seeding complet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Créer les permissions de base
     */
    private function createBasePermissions()
    {
        Log::info('Création des permissions de base...');

        $permissions = config('permissions.available_permissions', [
            'Créer' => 'Permet de créer de nouveaux éléments',
            'Lire' => 'Permet de consulter les éléments existants',
            'Modifier' => 'Permet de modifier les éléments existants',
            'Supprimer' => 'Permet de supprimer les éléments existants',
        ]);

        foreach ($permissions as $name => $description) {
            Permission::firstOrCreate(
                ['permission_name' => $name],
                [
                    'description' => $description,
                    'is_active' => true
                ]
            );
        }

        Log::info('Permissions de base créées', ['count' => count($permissions)]);
    }

    /**
     * Créer ou mettre à jour les menus
     */
    private function createOrUpdateMenus()
    {
        Log::info('Création/mise à jour des menus...');

        $menus = [
            [
                'libelle' => 'Dashboard',
                'url' => '/dashboard',
                'icone' => 'fa-dashboard',
                'ordre' => 1,
                'is_active' => true
            ],
            [
                'libelle' => 'Gestion des Rôles',
                'url' => '/role',
                'icone' => 'fa-users',
                'ordre' => 2,
                'is_active' => true
            ],
            [
                'libelle' => 'Gestion des Utilisateurs',
                'url' => '/utilisateurs',
                'icone' => 'fa-user',
                'ordre' => 3,
                'is_active' => true
            ],
            [
                'libelle' => 'Types de Réforme',
                'url' => '/typereforme',
                'icone' => 'fa-tags',
                'ordre' => 4,
                'is_active' => true
            ],
            [
                'libelle' => 'Réformes',
                'url' => '/reforme',
                'icone' => 'fa-file-text',
                'ordre' => 5,
                'is_active' => true
            ],
            [
                'libelle' => 'Activités',
                'url' => '/activites',
                'icone' => 'fa-tasks',
                'ordre' => 6,
                'is_active' => true
            ],
            [
                'libelle' => 'Indicateurs',
                'url' => '/indicateurs',
                'icone' => 'fa-bar-chart',
                'ordre' => 7,
                'is_active' => true
            ]
        ];

        foreach ($menus as $menuData) {
            Menu::updateOrCreate(
                ['url' => $menuData['url']],
                $menuData
            );
        }

        Log::info('Menus créés/mis à jour', ['count' => count($menus)]);
    }

    /**
     * Créer les associations permission-menu
     */
    private function createPermissionMenuAssociations()
    {
        Log::info('Création des associations permission-menu...');

        $menus = Menu::where('is_active', true)->get();
        $permissions = Permission::where('is_active', true)->get();

        foreach ($menus as $menu) {
            foreach ($permissions as $permission) {
                PermissionMenu::firstOrCreate([
                    'permission_id' => $permission->id,
                    'menu_id' => $menu->id
                ]);
            }
        }

        Log::info('Associations permission-menu créées');
    }

    /**
     * Créer les rôles selon la configuration
     */
    private function createRolesFromConfig()
    {
        Log::info('Création des rôles selon la configuration...');

        $roleConfigs = config('permissions.default_role_permissions', []);

        foreach ($roleConfigs as $roleName => $config) {
            $role = Role::firstOrCreate(
                ['role_name' => $roleName],
                [
                    'description' => $config['description'] ?? "Rôle $roleName",
                    'is_active' => true
                ]
            );

            Log::info("Rôle créé/trouvé: $roleName", ['role_id' => $role->id]);
        }
    }

    /**
     * Assigner les permissions aux rôles selon la configuration
     */
    private function assignPermissionsToRoles()
    {
        Log::info('Attribution des permissions aux rôles...');

        $roles = Role::where('is_active', true)->get();

        foreach ($roles as $role) {
            $this->permissionService->assignPermissionsToRole($role);
            Log::info("Permissions assignées au rôle: {$role->role_name}");
        }
    }

    /**
     * Vérifier et corriger les incohérences
     */
    private function verifyAndFixInconsistencies()
    {
        Log::info('Vérification des incohérences...');

        // Vérifier que tous les rôles ont au moins une permission
        $rolesWithoutPermissions = Role::whereDoesntHave('permissionMenus')->get();
        
        if ($rolesWithoutPermissions->count() > 0) {
            Log::warning('Rôles sans permissions détectés', [
                'roles' => $rolesWithoutPermissions->pluck('role_name')->toArray()
            ]);

            foreach ($rolesWithoutPermissions as $role) {
                $this->permissionService->assignPermissionsToRole($role);
            }
        }

        // Vérifier que tous les menus ont des associations avec les permissions
        $menusWithoutPermissions = Menu::whereDoesntHave('permissionMenus')->get();
        
        if ($menusWithoutPermissions->count() > 0) {
            Log::warning('Menus sans permissions détectés', [
                'menus' => $menusWithoutPermissions->pluck('libelle')->toArray()
            ]);

            $this->createPermissionMenuAssociations();
        }

        Log::info('Vérification terminée');
    }
}
