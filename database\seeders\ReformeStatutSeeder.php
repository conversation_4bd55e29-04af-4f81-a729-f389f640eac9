<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Reforme;
use App\Models\TypeReforme;
use Carbon\Carbon;

class ReformeStatutSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $typeReforme = TypeReforme::first();
        
        if (!$typeReforme) {
            $this->command->info('Aucun type de réforme trouvé. Veuillez d\'abord exécuter le seeder pour les types de réforme.');
            return;
        }

        // Réforme en cours (automatique)
        Reforme::create([
            'titre' => 'Réforme Digitalisation - En cours automatique',
            'objectifs' => 'Moderniser les processus administratifs',
            'budget' => 150000,
            'date_debut' => Carbon::now()->subDays(30),
            'date_fin_prevue' => Carbon::now()->addDays(60),
            'date_fin' => null,
            'statut_manuel' => null,
            'type_reforme' => $typeReforme->id,
            'created_by' => 1,
            'updated_by' => 1,
        ]);

        // Réforme en pause (manuel)
        Reforme::create([
            'titre' => 'Réforme Ressources Humaines - En pause',
            'objectifs' => 'Améliorer la gestion du personnel',
            'budget' => 200000,
            'date_debut' => Carbon::now()->subDays(15),
            'date_fin_prevue' => Carbon::now()->addDays(90),
            'date_fin' => null,
            'statut_manuel' => 'En pause',
            'type_reforme' => $typeReforme->id,
            'created_by' => 1,
            'updated_by' => 1,
        ]);

        // Réforme achevée (automatique)
        Reforme::create([
            'titre' => 'Réforme Comptabilité - Achevée automatique',
            'objectifs' => 'Moderniser le système comptable',
            'budget' => 100000,
            'date_debut' => Carbon::now()->subDays(90),
            'date_fin_prevue' => Carbon::now()->subDays(10),
            'date_fin' => Carbon::now()->subDays(5),
            'statut_manuel' => null,
            'type_reforme' => $typeReforme->id,
            'created_by' => 1,
            'updated_by' => 1,
        ]);

        // Réforme achevée (manuel)
        Reforme::create([
            'titre' => 'Réforme Communication - Achevée manuellement',
            'objectifs' => 'Améliorer la communication interne',
            'budget' => 75000,
            'date_debut' => Carbon::now()->subDays(60),
            'date_fin_prevue' => Carbon::now()->addDays(30),
            'date_fin' => null,
            'statut_manuel' => 'Achevé',
            'type_reforme' => $typeReforme->id,
            'created_by' => 1,
            'updated_by' => 1,
        ]);

        // Réforme planifiée
        Reforme::create([
            'titre' => 'Réforme Infrastructure - Planifiée',
            'objectifs' => 'Moderniser l\'infrastructure IT',
            'budget' => 300000,
            'date_debut' => Carbon::now()->addDays(15),
            'date_fin_prevue' => Carbon::now()->addDays(120),
            'date_fin' => null,
            'statut_manuel' => null,
            'type_reforme' => $typeReforme->id,
            'created_by' => 1,
            'updated_by' => 1,
        ]);

        // Réforme brouillon
        Reforme::create([
            'titre' => 'Réforme Formation - Brouillon',
            'objectifs' => 'Améliorer la formation du personnel',
            'budget' => 50000,
            'date_debut' => null,
            'date_fin_prevue' => null,
            'date_fin' => null,
            'statut_manuel' => null,
            'type_reforme' => $typeReforme->id,
            'created_by' => 1,
            'updated_by' => 1,
        ]);

        $this->command->info('Réformes de test avec différents statuts créées avec succès !');
    }
}
