BEGIN TRANSACTION;
INSERT INTO "type_reforme" ("id","lib","created_at","updated_at") VALUES (1,'direct','2025-07-03 19:12:25','2025-07-03 19:12:25'),
 (2,'Krizostome pafid aho','2025-07-03 19:12:33','2025-07-03 19:12:33'),
 (3,'suivi','2025-07-03 19:12:42','2025-07-03 19:12:42');
INSERT INTO "personne" ("id","nom","prenom","fonction","tel","email","created_at","updated_at") VALUES (1,'<PERSON><PERSON>','<PERSON>','Directeur','0123456789','<EMAIL>',NULL,NULL),
 (2,'<PERSON>','<PERSON>','Responsable RH','0987654321','<EMAIL>',NULL,NULL),
 (3,'<PERSON><PERSON>','<PERSON>','Chef de projet','0678901234','<EMAIL>',NULL,NULL),
 (4,'<PERSON><PERSON><PERSON>','<PERSON>','<PERSON>lyste','0765432198','<EMAIL>',NULL,NULL),
 (5,'<PERSON>','<PERSON>','Développeur','0654321987','<EMAIL>',NULL,NULL),
 (6,'AHO','Krizostome Pafid','<PERSON> de projets','0678901233','<EMAIL>','2025-07-05 18:37:32','2025-07-05 18:37:32'),
 (7,'AHO','Krizostome Pafid','Chef de projets','0678901222','<EMAIL>','2025-07-05 18:48:37','2025-07-05 18:48:37');
INSERT INTO "users" ("id","personne_id","pwd","status","created_at","updated_at") VALUES (1,1,'$2y$12$tFOQ5u5mmDA9BS35khbAzuGWos.lZ2tTBDNL6bHLR2A57do9Scog2',1,'2025-07-03 14:03:44','2025-07-03 14:03:44'),
 (2,3,'$2y$12$T7KED79X3vN7q7EdukCj2.K3c5Fr3N6.Uy4P6bx61LDOXJaT8rHwi',1,'2025-07-03 14:03:45','2025-07-03 14:03:45'),
 (3,4,'$2y$12$xT4ctltKaGFWZ1QIX3aVAe87Tf/dkFf58AHjT3WsNJuaJ3QR6WyGy',1,'2025-07-03 14:03:45','2025-07-03 14:03:45'),
 (4,6,'$2y$12$ugW7/dIz8IyfyHo973sbU.ew0SvKu6BqrGIPImOMSa6eyV2qCDIPS',1,'2025-07-05 18:37:32','2025-07-05 18:37:32'),
 (5,7,'$2y$12$UTrBA9.VQQpkRnuUTGvNxuAIY6JK/dQlAjCpZjX6bVhdwca9RCKva',1,'2025-07-05 18:48:38','2025-07-05 18:48:38');
INSERT INTO "permission" ("id","permission_name","created_at","updated_at") VALUES (1,'Créer',NULL,NULL),
 (2,'Lire',NULL,NULL),
 (3,'Modifier',NULL,NULL),
 (4,'Supprimer',NULL,NULL);
INSERT INTO "permission_menu" ("id","menu_id","permission_id","created_at","updated_at") VALUES (1,1,2,NULL,NULL),
 (2,2,2,NULL,NULL),
 (3,2,1,NULL,NULL),
 (4,2,3,NULL,NULL),
 (5,2,4,NULL,NULL),
 (6,3,2,NULL,NULL),
 (7,3,1,NULL,NULL),
 (8,3,3,NULL,NULL),
 (9,3,4,NULL,NULL),
 (10,4,2,NULL,NULL),
 (11,4,1,NULL,NULL),
 (12,4,3,NULL,NULL),
 (13,4,4,NULL,NULL),
 (14,5,2,NULL,NULL),
 (15,5,1,NULL,NULL),
 (16,5,3,NULL,NULL),
 (17,5,4,NULL,NULL),
 (18,6,2,NULL,NULL),
 (19,6,1,NULL,NULL),
 (20,6,3,NULL,NULL),
 (21,6,4,NULL,NULL),
 (22,7,2,NULL,NULL),
 (23,7,1,NULL,NULL),
 (24,7,3,NULL,NULL),
 (25,7,4,NULL,NULL),
 (26,8,2,NULL,NULL),
 (27,8,1,NULL,NULL),
 (28,8,3,NULL,NULL),
 (29,8,4,NULL,NULL),
 (30,9,2,NULL,NULL),
 (31,9,1,NULL,NULL),
 (32,9,3,NULL,NULL),
 (33,9,4,NULL,NULL);
INSERT INTO "role" ("id","role_name","created_at","updated_at") VALUES (1,'Administrateur','2025-07-03 14:03:43','2025-07-03 14:03:43'),
 (2,'Gestionnaire','2025-07-03 14:03:43','2025-07-03 14:03:43'),
 (3,'Utilisateur','2025-07-03 14:03:44','2025-07-03 14:03:44');
INSERT INTO "role_permission" ("role_id","permission_menu_id","created_at","updated_at") VALUES (1,1,NULL,NULL),
 (1,3,NULL,NULL),
 (1,2,NULL,NULL),
 (1,4,NULL,NULL),
 (1,5,NULL,NULL),
 (1,7,NULL,NULL),
 (1,6,NULL,NULL),
 (1,8,NULL,NULL),
 (1,9,NULL,NULL),
 (1,11,NULL,NULL),
 (1,10,NULL,NULL),
 (1,12,NULL,NULL),
 (1,13,NULL,NULL),
 (1,15,NULL,NULL),
 (1,14,NULL,NULL),
 (1,16,NULL,NULL),
 (1,17,NULL,NULL),
 (1,19,NULL,NULL),
 (1,18,NULL,NULL),
 (1,20,NULL,NULL),
 (1,21,NULL,NULL),
 (1,23,NULL,NULL),
 (1,22,NULL,NULL),
 (1,24,NULL,NULL),
 (1,25,NULL,NULL),
 (1,27,NULL,NULL),
 (1,26,NULL,NULL),
 (1,28,NULL,NULL),
 (1,29,NULL,NULL),
 (1,31,NULL,NULL),
 (1,30,NULL,NULL),
 (1,32,NULL,NULL),
 (1,33,NULL,NULL),
 (2,1,NULL,NULL),
 (2,11,NULL,NULL),
 (2,10,NULL,NULL),
 (2,12,NULL,NULL),
 (2,13,NULL,NULL),
 (2,15,NULL,NULL),
 (2,14,NULL,NULL),
 (2,16,NULL,NULL),
 (2,17,NULL,NULL),
 (2,19,NULL,NULL),
 (2,18,NULL,NULL),
 (3,1,NULL,NULL);
INSERT INTO "user_role" ("id_user","role_id","created_at","updated_at") VALUES (1,1,NULL,NULL),
 (2,2,NULL,NULL),
 (4,1,NULL,NULL),
 (5,3,NULL,NULL);
INSERT INTO "reformes" ("id","titre","objectifs","budget","date_debut","date_fin_prevue","date_fin","statut","pieces_justificatifs","type_reforme","created_by","updated_by","created_at","updated_at") VALUES (1,'ayass','''rezertyt''("''(-''',10000,'2025-06-28','2025-07-05','2025-07-03 19:34:43','C','zertyuiuytr',1,1,1,'2025-07-03 19:21:42','2025-07-03 19:34:43'),
 (2,'aya','''rezertyt''("''(-''',10000,'2025-06-28','2025-07-05','2025-07-04 16:47:23','C','zertyuiuytr',1,1,1,'2025-07-03 19:56:50','2025-07-04 16:47:23');
INSERT INTO "structure" ("id","lib_court","lib_long","responsable","created_at","updated_at") VALUES (1,'DGRH','Direction Générale des Ressources Humaines',1,NULL,NULL),
 (2,'DSI','Direction des Systèmes d’Information',1,NULL,NULL),
 (3,'CSE','Cellule de Suivi et d’Évaluation',3,NULL,NULL),
 (4,'SG','Secrétariat Général',2,NULL,NULL),
 (5,'IGS','Inspection Générale des Services',2,NULL,NULL);
INSERT INTO "reformes_structure" ("id","reforme_id","structure_id","created_at","updated_at") VALUES (1,1,2,NULL,NULL),
 (2,2,2,NULL,NULL);
INSERT INTO "activites_reformes" ("id","reforme_id","libelle","date_debut","date_fin_prevue","date_fin","poids","statut","parent","structure_responsable","created_by","updated_by","created_at","updated_at") VALUES (1,1,'rredddfs','2025-06-22','2025-07-03','2025-07-03 19:34:43',16,'A',NULL,1,1,1,'2025-07-03 19:32:42','2025-07-03 19:34:43'),
 (2,1,'rredddf','2025-06-28','2025-07-06','2025-07-03 19:34:43',100,'A',1,1,1,1,'2025-07-03 19:33:17','2025-07-03 19:34:43'),
 (3,2,'rredddfs','2025-06-22','2025-07-03','2025-07-04 16:47:23',16,'A',NULL,1,1,1,'2025-07-04 16:39:54','2025-07-04 16:47:23'),
 (4,2,'rredddf','2025-06-28','2025-07-06','2025-07-04 16:47:23',100,'A',3,1,1,1,'2025-07-04 16:44:54','2025-07-04 16:47:23');
INSERT INTO "suivi_activites" ("id","activite_reforme_id","suivi_date","actions_fait","actions_a_fait","difficultes","solutions","observations","created_by","updated_by","created_at","updated_at") VALUES (1,2,'2025-07-03','ezzrt','zert','sdfgh','sdfgh','sdsfgh',1,NULL,'2025-07-03 19:34:08','2025-07-03 19:34:08'),
 (2,2,'2025-07-03 19:34:43','Activité validée et terminée','Aucune action supplémentaire requise',NULL,NULL,'Validation rapide par ',1,NULL,'2025-07-03 19:34:43','2025-07-03 19:34:43'),
 (3,1,'2025-07-03 19:34:43','Activité terminée automatiquement - toutes les sous-activités sont achevées','Aucune action supplémentaire requise',NULL,NULL,'Validation automatique en cascade par ',1,NULL,'2025-07-03 19:34:43','2025-07-03 19:34:43'),
 (4,4,'2025-07-04','qzertuhop','zertyuiop','tfuhpo','êsxdgvbh','sdtrfyujio',1,NULL,'2025-07-04 16:46:45','2025-07-04 16:46:45'),
 (5,4,'2025-07-04 16:47:23','Activité validée et terminée','Aucune action supplémentaire requise',NULL,NULL,'Validation rapide par ',1,NULL,'2025-07-04 16:47:23','2025-07-04 16:47:23'),
 (6,3,'2025-07-04 16:47:23','Activité terminée automatiquement - toutes les sous-activités sont achevées','Aucune action supplémentaire requise',NULL,NULL,'Validation automatique en cascade par ',1,NULL,'2025-07-04 16:47:23','2025-07-04 16:47:23');
INSERT INTO "indicateurs" ("id","libelle","unite","created_at","updated_at") VALUES (1,'es','12A','2025-07-03 19:12:54','2025-07-03 19:12:54'),
 (2,'es','12A','2025-07-03 19:20:20','2025-07-03 19:20:20'),
 (3,'es','12A','2025-07-03 19:21:06','2025-07-03 19:21:06'),
 (4,'rredddfs','15','2025-07-03 19:24:07','2025-07-03 19:24:07');
INSERT INTO "menu" ("id","libelle","url","icon","created_at","updated_at","parent_id","ordre","is_active") VALUES (1,'Dashboard','/dashboard','educate-icon educate-home icon-wrap',NULL,NULL,NULL,0,1),
 (2,'Gestion des rôles','/role','educate-icon educate-department icon-wrap',NULL,NULL,NULL,0,1),
 (3,'Gestion des utilisateurs','/utilisateurs','educate-icon educate-professor icon-wrap',NULL,NULL,NULL,0,1),
 (4,'Type de réforme','/typereforme','educate-icon educate-course icon-wrap',NULL,NULL,NULL,0,1),
 (5,'Gestion des Indicateurs','/indicateurs','educate-icon educate-data-table icon-wrap',NULL,NULL,NULL,0,1),
 (6,'Gestion des réformes','/reforme','educate-icon educate-library icon-wrap',NULL,NULL,NULL,0,1),
 (7,'Gestion des activités','/activites','educate-icon educate-event icon-wrap',NULL,NULL,NULL,0,1),
 (8,'Suivi des activités','/suivi-activites','educate-icon educate-student icon-wrap',NULL,NULL,NULL,0,1),
 (9,'Suivi des indicateurs','/suivi-indicateurs','educate-icon educate-chart icon-wrap',NULL,NULL,NULL,0,1);
COMMIT;
