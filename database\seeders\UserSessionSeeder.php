<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Session;
use App\Models\User;
use Carbon\Carbon;

class UserSessionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer quelques utilisateurs existants
        $users = User::limit(3)->get();

        if ($users->count() === 0) {
            $this->command->info('Aucun utilisateur trouvé. Veuillez d\'abord exécuter les seeders d\'utilisateurs.');
            return;
        }

        // Créer des sessions actives pour simuler des utilisateurs connectés
        foreach ($users as $index => $user) {
            Session::create([
                'user_id' => $user->id,
                'session_id' => 'demo_session_' . $user->id . '_' . time(),
                'ip_address' => '127.0.0.' . (1 + $index),
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'login_at' => Carbon::now()->subMinutes(rand(5, 60)),
                'last_activity' => Carbon::now()->subMinutes(rand(1, 10)),
                'status' => 'active'
            ]);
        }

        // Créer quelques sessions inactives pour les tests
        if ($users->count() > 1) {
            Session::create([
                'user_id' => $users->get(1)->id,
                'session_id' => 'old_session_' . $users->get(1)->id . '_' . (time() - 3600),
                'ip_address' => '127.0.0.10',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'login_at' => Carbon::now()->subHours(2),
                'last_activity' => Carbon::now()->subHours(1),
                'logout_at' => Carbon::now()->subMinutes(30),
                'status' => 'inactive'
            ]);
        }

        $this->command->info('Sessions utilisateur créées avec succès !');
        $this->command->info('Sessions actives : ' . Session::where('status', 'active')->count());
        $this->command->info('Sessions inactives : ' . Session::where('status', 'inactive')->count());
    }
}
