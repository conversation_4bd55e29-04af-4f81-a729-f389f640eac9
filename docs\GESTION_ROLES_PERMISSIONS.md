# Documentation - Système de Gestion des Rôles et Permissions

## Vue d'ensemble

Le système de gestion des rôles et permissions permet de contrôler l'accès aux différentes fonctionnalités de l'application selon le rôle attribué à chaque utilisateur. Cette implémentation respecte l'architecture existante sans modifier les fonctionnalités déjà en place.

## Architecture du Système

### Structure des Tables

1. **`role`** - Stocke les différents rôles
2. **`permission`** - Stocke les types de permissions (C<PERSON>er, Lire, Modifier, Supprimer)
3. **`menu`** - Stocke les menus de l'application
4. **`permission_menu`** - Associe les permissions aux menus
5. **`role_permission`** - Associe les rôles aux permissions de menus
6. **`user_role`** - Associe les utilisateurs aux rôles

### Rôles Prédéfinis

1. **Administrateur** - Accès complet à toutes les fonctionnalités
2. **Gestionnaire** - Accès limité (pas de gestion des rôles/utilisateurs)
3. **Utilisateur** - Accès en lecture seule au dashboard et activités

## Fonctionnalités Implémentées

### 1. Méthodes du Modèle User

Le modèle `User` a été enrichi avec les méthodes suivantes :

```php
// Vérification des rôles
$user->hasRole('Administrateur')
$user->hasAnyRole(['Administrateur', 'Gestionnaire'])
$user->hasAllRoles(['role1', 'role2'])

// Vérification des permissions
$user->hasPermission($menuId, 'Créer')
$user->hasPermissionByName('Modifier')
$user->canAccessMenu($menuId)

// Récupération des données
$user->getAccessibleMenus()
$user->getAllPermissions()
```

### 2. Middleware de Contrôle d'Accès

Le middleware `RolePermissionMiddleware` protège les routes selon les permissions :

```php
// Dans routes/web.php
Route::middleware(['auth', 'role.permission:role:Administrateur'])->group(function () {
    Route::resource('role', RoleController::class);
});

// Syntaxes supportées
'role:Administrateur'                    // Rôle spécifique
'permission:Créer'                       // Permission spécifique
'menu:1'                                // Accès à un menu
'any_role:Admin,Manager'                // L'un des rôles
'all_roles:Admin,Manager'               // Tous les rôles
```

### 3. Directives Blade Personnalisées

Des directives Blade facilitent l'affichage conditionnel :

```blade
@hasRole('Administrateur')
    <button>Gérer les utilisateurs</button>
@endhasRole

@isAdmin
    <div>Section administrative</div>
@endisAdmin

@canCreate
    <a href="/create">Créer</a>
@endcanCreate

@hasAnyRole('Administrateur', 'Gestionnaire')
    <div>Contenu pour managers</div>
@endhasAnyRole
```

### 4. Filtrage Automatique des Menus

Le composant `MenuComponent` affiche automatiquement uniquement les menus accessibles à l'utilisateur connecté selon ses permissions.

## Configuration et Installation

### 1. Exécuter les Migrations

```bash
php artisan migrate
```

### 2. Exécuter les Seeders

```bash
php artisan db:seed --class=RolePermissionSeeder
```

### 3. Utilisateurs de Test Créés

- **<EMAIL>** / admin123 (Administrateur)
- **<EMAIL>** / manager123 (Gestionnaire)  
- **<EMAIL>** / user123 (Utilisateur)

## Gestion des Rôles et Permissions

### Ajouter un Nouveau Rôle

1. Créer le rôle dans la table `role`
2. Assigner les permissions via la table `role_permission`
3. Assigner le rôle aux utilisateurs via `user_role`

```php
// Exemple de création d'un nouveau rôle
$role = Role::create(['role_name' => 'Superviseur']);

// Assigner des permissions
$permissionMenuIds = [1, 2, 3]; // IDs des permission_menu
$role->permissionMenus()->attach($permissionMenuIds);

// Assigner à un utilisateur
$user->roles()->attach($role->id);
```

### Ajouter une Nouvelle Permission

1. Créer la permission dans la table `permission`
2. L'associer aux menus via `permission_menu`
3. L'assigner aux rôles via `role_permission`

### Ajouter un Nouveau Menu

1. Créer le menu dans la table `menu`
2. Créer les associations dans `permission_menu`
3. Assigner aux rôles appropriés

## Tests et Validation

### Routes de Test Disponibles

- `/test-role-system` - Test général du système
- `/test-create-users` - Création d'utilisateurs de test
- `/test-menu-access` - Test d'accès aux menus (nécessite authentification)

### Tests Automatisés

Exécuter les tests avec :

```bash
php artisan test tests/Feature/RolePermissionTest.php
```

## Sécurité

### Protection des Routes

Toutes les routes sensibles sont protégées par le middleware `role.permission` :

- **Routes administratives** : Réservées aux administrateurs
- **Routes de gestion** : Accessibles aux administrateurs et gestionnaires
- **Routes générales** : Accessibles à tous les utilisateurs connectés

### Validation des Permissions

- Vérification côté serveur via middleware
- Vérification côté client via directives Blade
- Filtrage automatique des menus selon les permissions

## Maintenance

### Ajout de Nouvelles Fonctionnalités

1. Créer le menu correspondant
2. Définir les permissions nécessaires
3. Protéger les routes avec le middleware approprié
4. Utiliser les directives Blade pour l'affichage conditionnel

### Modification des Permissions

Les permissions peuvent être modifiées via l'interface de gestion des rôles existante ou directement en base de données.

## Compatibilité

Ce système est entièrement compatible avec l'architecture existante et n'interfère pas avec les fonctionnalités déjà implémentées. Il ajoute une couche de sécurité sans modifier le comportement existant.
