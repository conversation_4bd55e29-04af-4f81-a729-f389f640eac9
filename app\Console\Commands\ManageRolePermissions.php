<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Services\PermissionService;
use Illuminate\Support\Facades\Validator;

class ManageRolePermissions extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'permissions:manage 
                            {action : Action à effectuer (list, assign, create-role, sync)}
                            {--role= : Nom du rôle}
                            {--menu= : URL du menu}
                            {--permission= : Nom de la permission}
                            {--all : Appliquer à tous les éléments}';

    /**
     * The console command description.
     */
    protected $description = 'Gérer les permissions des rôles de manière dynamique';

    protected $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        parent::__construct();
        $this->permissionService = $permissionService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'list':
                $this->listPermissions();
                break;
            case 'assign':
                $this->assignPermission();
                break;
            case 'create-role':
                $this->createRole();
                break;
            case 'sync':
                $this->syncPermissions();
                break;
            default:
                $this->error("Action non reconnue: {$action}");
                $this->info("Actions disponibles: list, assign, create-role, sync");
        }
    }

    /**
     * Lister les permissions actuelles
     */
    protected function listPermissions()
    {
        if ($this->option('role')) {
            $this->listRolePermissions();
        } else {
            $this->listAllRoles();
        }
    }

    /**
     * Lister toutes les permissions d'un rôle
     */
    protected function listRolePermissions()
    {
        $roleName = $this->option('role');
        $role = Role::where('role_name', $roleName)->first();

        if (!$role) {
            $this->error("Rôle '{$roleName}' non trouvé.");
            return;
        }

        $this->info("Permissions pour le rôle: {$roleName}");
        $this->line(str_repeat('-', 50));

        $menus = Menu::all();
        foreach ($menus as $menu) {
            $permissions = $this->permissionService->getRolePermissionsForMenu($role, $menu);
            if ($permissions->isNotEmpty()) {
                $this->info("📁 {$menu->libelle} ({$menu->url}):");
                foreach ($permissions as $permission) {
                    $this->line("   ✓ {$permission}");
                }
                $this->line('');
            }
        }
    }

    /**
     * Lister tous les rôles et leurs permissions
     */
    protected function listAllRoles()
    {
        $roles = Role::all();
        
        $this->info("Tous les rôles du système:");
        $this->line(str_repeat('=', 60));

        foreach ($roles as $role) {
            $config = $this->permissionService->getRoleConfiguration($role->role_name);
            $isSystem = $this->permissionService->isSystemRole($role->role_name);
            
            $this->info("🔐 {$role->role_name}" . ($isSystem ? ' (Système)' : ''));
            
            if ($config) {
                $this->line("   Description: {$config['description']}");
            }
            
            $permissionCount = $role->permissionMenus()->count();
            $this->line("   Permissions: {$permissionCount}");
            $this->line('');
        }
    }

    /**
     * Assigner une permission à un rôle
     */
    protected function assignPermission()
    {
        $roleName = $this->option('role');
        $menuUrl = $this->option('menu');
        $permissionName = $this->option('permission');

        if (!$roleName || !$menuUrl || !$permissionName) {
            $this->error('Les options --role, --menu et --permission sont requises pour cette action.');
            return;
        }

        $role = Role::where('role_name', $roleName)->first();
        if (!$role) {
            $this->error("Rôle '{$roleName}' non trouvé.");
            return;
        }

        $menu = Menu::where('url', $menuUrl)->first();
        if (!$menu) {
            $this->error("Menu '{$menuUrl}' non trouvé.");
            return;
        }

        $permission = Permission::where('permission_name', $permissionName)->first();
        if (!$permission) {
            $this->error("Permission '{$permissionName}' non trouvée.");
            return;
        }

        // Logique d'assignation ici
        $this->info("Permission '{$permissionName}' assignée au rôle '{$roleName}' pour le menu '{$menuUrl}'");
    }

    /**
     * Créer un nouveau rôle
     */
    protected function createRole()
    {
        $roleName = $this->option('role');
        
        if (!$roleName) {
            $roleName = $this->ask('Nom du nouveau rôle:');
        }

        if (Role::where('role_name', $roleName)->exists()) {
            $this->error("Le rôle '{$roleName}' existe déjà.");
            return;
        }

        $role = Role::create(['role_name' => $roleName]);
        
        // Assigner les permissions de base
        $this->permissionService->assignPermissionsToRole($role);
        
        $this->info("Rôle '{$roleName}' créé avec succès avec les permissions de base.");
    }

    /**
     * Synchroniser toutes les permissions selon la configuration
     */
    protected function syncPermissions()
    {
        $this->info('Synchronisation des permissions en cours...');
        
        // Créer les associations permission-menu
        $this->permissionService->createPermissionMenusForAllMenus();
        
        // Synchroniser les permissions des rôles
        $this->permissionService->syncAllRolePermissions();
        
        $this->info('✅ Synchronisation terminée avec succès!');
    }
}
