{"version": 3, "sources": ["../src/util.js", "../src/index.js"], "names": ["<PERSON><PERSON>", "$", "TRANSITION_END", "triggerTransitionEnd", "element", "trigger", "supportsTransitionEnd", "Boolean", "transitionEndEmulator", "duration", "_this", "this", "called", "one", "setTimeout", "fn", "mmEmulateTransitionEnd", "event", "special", "bindType", "delegateType", "handle", "target", "is", "handleObj", "handler", "apply", "arguments", "NAME", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON>", "toggle", "preventDefault", "triggerElement", "parentTrigger", "subMenu", "Event", "SHOW", "SHOWN", "HIDE", "HIDDEN", "CLICK_DATA_API", "ClassName", "MetisMenu", "config", "_objectSpread", "transitioning", "init", "self", "conf", "addClass", "find", "children", "attr", "parents", "has", "not", "on", "e", "eTar", "paRent", "parent", "sibLings", "siblings", "List", "hasClass", "hide", "show", "onTransitionStart", "elem", "startEvent", "isDefaultPrevented", "toggleElem", "removeClass", "height", "setTransitioning", "scrollHeight", "_this2", "offsetHeight", "complete", "onTransitionEnd", "css", "isTransitioning", "dispose", "removeData", "off", "jQueryInterface", "each", "$this", "data", "undefined", "Error", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;soBAEA,IAAMA,EAAQ,SAACC,GACb,IAAMC,EAAiB,gBAEjBF,EAAO,CACXE,eAAgB,kBAEhBC,qBAHW,SAGUC,GACnBH,EAAEG,GAASC,QAAQH,IAGrBI,sBAPW,WAQT,OAAOC,QAAQL,KAoBnB,SAASM,EAAsBC,GAAU,IAAAC,EAAAC,KACnCC,GAAS,EAYb,OAVAX,EAAEU,MAAME,IAAIb,EAAKE,eAAgB,WAC/BU,GAAS,IAGXE,WAAW,WACJF,GACHZ,EAAKG,qBAAqBO,IAE3BD,GAEIE,KAWT,OAPEV,EAAEc,GAAGC,uBAAyBR,EAE9BP,EAAEgB,MAAMC,QAAQlB,EAAKE,gBAlCd,CACLiB,SAAUjB,EACVkB,aAAclB,EACdmB,OAHK,SAGEJ,GACL,GAAIhB,EAAEgB,EAAMK,QAAQC,GAAGZ,MACrB,OAAOM,EACJO,UACAC,QACAC,MAAMf,KAAMgB,aA+BhB3B,EAvDK,+CCCR4B,EAAO,YACPC,EAAW,YACXC,EAAS,IAAOD,EAEhBE,EAAqB9B,EAAEc,GAAGa,GAG1BI,EAAU,CACdC,QAAQ,EACRC,gBAAgB,EAChBC,eAAgB,IAChBC,cAAe,KACfC,QAAS,MAGLC,EAAQ,CACZC,KAAI,OAAST,EACbU,MAAK,QAAUV,EACfW,KAAI,OAASX,EACbY,OAAM,SAAWZ,EACjBa,eAAc,QAAUb,EAjBL,aAoBfc,EACG,YADHA,EAEI,YAFJA,EAGE,UAHFA,EAIM,cAJNA,EAKQ,gBAIRC,aAEJ,SAAAA,EAAYzC,EAAS0C,GACnBnC,KAAKP,QAAUA,EACfO,KAAKmC,OAALC,EAAA,GAAmBf,EAAYc,GAC/BnC,KAAKqC,cAAgB,KAErBrC,KAAKsC,kCAGPA,KAAAA,WACE,IAAMC,EAAOvC,KACPwC,EAAOxC,KAAKmC,OAElB7C,EAAEU,KAAKP,SAASgD,SAASR,GAEzB3C,EAAEU,KAAKP,SACJiD,KAAQF,EAAKf,cADhB,IACiCQ,GAC9BU,SAASH,EAAKhB,gBACdoB,KAAK,gBAAiB,QAEzBtD,EAAEU,KAAKP,SACJiD,KAAQF,EAAKf,cADhB,IACiCQ,GAC9BY,QAAQL,EAAKf,eACbgB,SAASR,GAEZ3C,EAAEU,KAAKP,SACJiD,KAAQF,EAAKf,cADhB,IACiCQ,GAC9BY,QAAQL,EAAKf,eACbkB,SAASH,EAAKhB,gBACdoB,KAAK,gBAAiB,QAEzBtD,EAAEU,KAAKP,SACJiD,KAAQF,EAAKf,cADhB,IACiCQ,GAC9Ba,IAAIN,EAAKd,SACTiB,SAASH,EAAKd,SACde,SAAYR,EAJf,IAIqCA,GAErC3C,EAAEU,KAAKP,SACJiD,KAAKF,EAAKf,eACVsB,IAFH,IAEWd,GACRa,IAAIN,EAAKd,SACTiB,SAASH,EAAKd,SACde,SAASR,GAEZ3C,EAAEU,KAAKP,SACJiD,KAAKF,EAAKf,eACVqB,IAAIN,EAAKd,SACTiB,SAASH,EAAKhB,gBACdwB,GAAGrB,EAAMK,eAAgB,SAAUiB,GAClC,IAAMC,EAAO5D,EAAEU,MACTmD,EAASD,EAAKE,OAAOZ,EAAKf,eAC1B4B,EAAWF,EACdG,SAASd,EAAKf,eACdkB,SAASH,EAAKhB,gBACX+B,EAAOJ,EAAOR,SAASH,EAAKd,SAC9Bc,EAAKjB,gBACP0B,EAAE1B,iBAE+B,SAA/B2B,EAAKN,KAAK,mBAGVO,EAAOK,SAASvB,IAClBiB,EAAKN,KAAK,gBAAiB,SAC3BL,EAAKkB,KAAKF,KAEVhB,EAAKmB,KAAKH,GACVL,EAAKN,KAAK,gBAAiB,QACvBJ,EAAKlB,QACP+B,EAAST,KAAK,gBAAiB,UAI/BJ,EAAKmB,mBACPnB,EAAKmB,kBAAkBV,SAK/BS,KAAAA,SAAKjE,GAAS,IAAAM,EAAAC,KACZ,IAAIA,KAAKqC,gBAAiB/C,EAAEG,GAAS+D,SAASvB,GAA9C,CAGA,IAAM2B,EAAOtE,EAAEG,GAEToE,EAAavE,EAAEqC,MAAMA,EAAMC,MAGjC,GAFAgC,EAAKlE,QAAQmE,IAETA,EAAWC,qBAAf,CAMA,GAFAF,EAAKR,OAAOpD,KAAKmC,OAAOV,eAAegB,SAASR,GAE5CjC,KAAKmC,OAAOb,OAAQ,CACtB,IAAMyC,EAAaH,EAAKR,OAAOpD,KAAKmC,OAAOV,eAAe6B,WAAWX,SAAY3C,KAAKmC,OAAOT,QAA1E,IAAqFO,GACxGjC,KAAKyD,KAAKM,GAGZH,EACGI,YAAY/B,GACZQ,SAASR,GACTgC,OAAO,GAEVjE,KAAKkE,kBAAiB,GAiBtBN,EACGK,OAAOxE,EAAQ,GAAG0E,cAClBjE,IAAIb,EAAKE,eAjBK,WAEVQ,EAAKoC,QAAWpC,EAAKN,UAG1BmE,EACGI,YAAY/B,GACZQ,SAAYR,EAFf,IAEqCA,GAClCgC,OAAO,IAEVlE,EAAKmE,kBAAiB,GAEtBN,EAAKlE,QAAQiC,EAAME,UAMlBxB,uBAvJqB,UA0J1BoD,KAAAA,SAAKhE,GAAS,IAAA2E,EAAApE,KACZ,IACEA,KAAKqC,eAAkB/C,EAAEG,GAAS+D,SAASvB,GAD7C,CAMA,IAAM2B,EAAOtE,EAAEG,GAEToE,EAAavE,EAAEqC,MAAMA,EAAMG,MAGjC,GAFA8B,EAAKlE,QAAQmE,IAETA,EAAWC,qBAAf,CAIAF,EAAKR,OAAOpD,KAAKmC,OAAOV,eAAeuC,YAAY/B,GAEnD2B,EAAKK,OAAOL,EAAKK,UAAU,GAAGI,aAE9BT,EACGnB,SAASR,GACT+B,YAAY/B,GACZ+B,YAAY/B,GAEfjC,KAAKkE,kBAAiB,GAEtB,IAAMI,EAAW,WAEVF,EAAKjC,QAAWiC,EAAK3E,UAGtB2E,EAAK/B,eAAiB+B,EAAKjC,OAAOoC,iBACpCH,EAAKjC,OAAOoC,kBAGdH,EAAKF,kBAAiB,GACtBN,EAAKlE,QAAQiC,EAAMI,QAEnB6B,EACGI,YAAY/B,GACZQ,SAASR,KAGQ,IAAlB2B,EAAKK,UAA0C,SAAxBL,EAAKY,IAAI,WAClCF,IAEAV,EACGK,OAAO,GACP/D,IAAIb,EAAKE,eAAgB+E,GACzBjE,uBA5MmB,UAgN1B6D,iBAAAA,SAAiBO,GACfzE,KAAKqC,cAAgBoC,KAGvBC,QAAAA,WACEpF,EAAEqF,WAAW3E,KAAKP,QAASyB,GAE3B5B,EAAEU,KAAKP,SACJiD,KAAK1C,KAAKmC,OAAOV,eACjBqB,IAAI9C,KAAKmC,OAAOT,SAChBiB,SAAS3C,KAAKmC,OAAOX,gBACrBoD,IAAI,SAEP5E,KAAKqC,cAAgB,KACrBrC,KAAKmC,OAAS,KACdnC,KAAKP,QAAU,QAGVoF,gBAAAA,SAAgB1C,GAErB,OAAOnC,KAAK8E,KAAK,WACf,IAAMC,EAAQzF,EAAEU,MACZgF,EAAOD,EAAMC,KAAK9D,GAChBsB,EAAIJ,EAAA,GACLf,EACA0D,EAAMC,OACa,iBAAX7C,GAAuBA,EAASA,EAAS,IAQtD,GALK6C,IACHA,EAAO,IAAI9C,EAAUlC,KAAMwC,GAC3BuC,EAAMC,KAAK9D,EAAU8D,IAGD,iBAAX7C,EAAqB,CAC9B,QAAqB8C,IAAjBD,EAAK7C,GACP,MAAM,IAAI+C,MAAJ,oBAA8B/C,EAA9B,KAER6C,EAAK7C,qBAWb7C,EAAEc,GAAGa,GAAQiB,EAAU2C,gBACvBvF,EAAEc,GAAGa,GAAMkE,YAAcjD,EACzB5C,EAAEc,GAAGa,GAAMmE,WAAa,WAGtB,OADA9F,EAAEc,GAAGa,GAAQG,EACNc,EAAU2C", "sourcesContent": ["import $ from 'jquery';\n\nconst Util = (($) => { // eslint-disable-line no-shadow\n  const TRANSITION_END = 'transitionend';\n\n  const Util = { // eslint-disable-line no-shadow\n    TRANSITION_END: 'mmTransitionEnd',\n\n    triggerTransitionEnd(element) {\n      $(element).trigger(TRANSITION_END);\n    },\n\n    supportsTransitionEnd() {\n      return Boolean(TRANSITION_END);\n    },\n  };\n\n  function getSpecialTransitionEndEvent() {\n    return {\n      bindType: TRANSITION_END,\n      delegateType: TRANSITION_END,\n      handle(event) {\n        if ($(event.target).is(this)) {\n          return event\n            .handleObj\n            .handler\n            .apply(this, arguments); // eslint-disable-line prefer-rest-params\n        }\n        return undefined;\n      },\n    };\n  }\n\n  function transitionEndEmulator(duration) {\n    let called = false;\n\n    $(this).one(Util.TRANSITION_END, () => {\n      called = true;\n    });\n\n    setTimeout(() => {\n      if (!called) {\n        Util.triggerTransitionEnd(this);\n      }\n    }, duration);\n\n    return this;\n  }\n\n  function setTransitionEndSupport() {\n    $.fn.mmEmulateTransitionEnd = transitionEndEmulator; // eslint-disable-line no-param-reassign\n    // eslint-disable-next-line no-param-reassign\n    $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent();\n  }\n\n  setTransitionEndSupport();\n\n  return Util;\n})($);\n\nexport default Util;\n", "import $ from 'jquery';\nimport Util from './util';\n\nconst NAME = 'metisMenu';\nconst DATA_KEY = 'metisMenu';\nconst EVENT_KEY = `.${DATA_KEY}`;\nconst DATA_API_KEY = '.data-api';\nconst JQUERY_NO_CONFLICT = $.fn[NAME];\nconst TRANSITION_DURATION = 350;\n\nconst Default = {\n  toggle: true,\n  preventDefault: true,\n  triggerElement: 'a',\n  parentTrigger: 'li',\n  subMenu: 'ul',\n};\n\nconst Event = {\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  CLICK_DATA_API: `click${EVENT_KEY}${DATA_API_KEY}`,\n};\n\nconst ClassName = {\n  METIS: 'metismenu',\n  ACTIVE: 'mm-active',\n  SHOW: 'mm-show',\n  COLLAPSE: 'mm-collapse',\n  COLLAPSING: 'mm-collapsing',\n  COLLAPSED: 'mm-collapsed',\n};\n\nclass MetisMenu {\n  // eslint-disable-line no-shadow\n  constructor(element, config) {\n    this.element = element;\n    this.config = { ...Default, ...config };\n    this.transitioning = null;\n\n    this.init();\n  }\n\n  init() {\n    const self = this;\n    const conf = this.config;\n\n    $(this.element).addClass(ClassName.METIS); // add metismenu class to element\n\n    $(this.element)\n      .find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .children(conf.triggerElement)\n      .attr('aria-expanded', 'true'); // add attribute aria-expanded=true the trigger element\n\n    $(this.element)\n      .find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .parents(conf.parentTrigger)\n      .addClass(ClassName.ACTIVE);\n\n    $(this.element)\n      .find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .parents(conf.parentTrigger)\n      .children(conf.triggerElement)\n      .attr('aria-expanded', 'true'); // add attribute aria-expanded=true the triggers of all parents\n\n    $(this.element)\n      .find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .has(conf.subMenu)\n      .children(conf.subMenu)\n      .addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`);\n\n    $(this.element)\n      .find(conf.parentTrigger)\n      .not(`.${ClassName.ACTIVE}`)\n      .has(conf.subMenu)\n      .children(conf.subMenu)\n      .addClass(ClassName.COLLAPSE);\n\n    $(this.element)\n      .find(conf.parentTrigger)\n      .has(conf.subMenu)\n      .children(conf.triggerElement)\n      .on(Event.CLICK_DATA_API, function (e) { // eslint-disable-line func-names\n        const eTar = $(this);\n        const paRent = eTar.parent(conf.parentTrigger);\n        const sibLings = paRent\n          .siblings(conf.parentTrigger)\n          .children(conf.triggerElement);\n        const List = paRent.children(conf.subMenu);\n        if (conf.preventDefault) {\n          e.preventDefault();\n        }\n        if (eTar.attr('aria-disabled') === 'true') {\n          return;\n        }\n        if (paRent.hasClass(ClassName.ACTIVE)) {\n          eTar.attr('aria-expanded', 'false');\n          self.hide(List);\n        } else {\n          self.show(List);\n          eTar.attr('aria-expanded', 'true');\n          if (conf.toggle) {\n            sibLings.attr('aria-expanded', 'false');\n          }\n        }\n\n        if (conf.onTransitionStart) {\n          conf.onTransitionStart(e);\n        }\n      });\n  }\n\n  show(element) {\n    if (this.transitioning || $(element).hasClass(ClassName.COLLAPSING)) {\n      return;\n    }\n    const elem = $(element);\n\n    const startEvent = $.Event(Event.SHOW);\n    elem.trigger(startEvent);\n\n    if (startEvent.isDefaultPrevented()) {\n      return;\n    }\n\n    elem.parent(this.config.parentTrigger).addClass(ClassName.ACTIVE);\n\n    if (this.config.toggle) {\n      const toggleElem = elem.parent(this.config.parentTrigger).siblings().children(`${this.config.subMenu}.${ClassName.SHOW}`);\n      this.hide(toggleElem);\n    }\n\n    elem\n      .removeClass(ClassName.COLLAPSE)\n      .addClass(ClassName.COLLAPSING)\n      .height(0);\n\n    this.setTransitioning(true);\n\n    const complete = () => {\n      // check if disposed\n      if (!this.config || !this.element) {\n        return;\n      }\n      elem\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`)\n        .height('');\n\n      this.setTransitioning(false);\n\n      elem.trigger(Event.SHOWN);\n    };\n\n    elem\n      .height(element[0].scrollHeight)\n      .one(Util.TRANSITION_END, complete)\n      .mmEmulateTransitionEnd(TRANSITION_DURATION);\n  }\n\n  hide(element) {\n    if (\n      this.transitioning || !$(element).hasClass(ClassName.SHOW)\n    ) {\n      return;\n    }\n\n    const elem = $(element);\n\n    const startEvent = $.Event(Event.HIDE);\n    elem.trigger(startEvent);\n\n    if (startEvent.isDefaultPrevented()) {\n      return;\n    }\n\n    elem.parent(this.config.parentTrigger).removeClass(ClassName.ACTIVE);\n    // eslint-disable-next-line no-unused-expressions\n    elem.height(elem.height())[0].offsetHeight;\n\n    elem\n      .addClass(ClassName.COLLAPSING)\n      .removeClass(ClassName.COLLAPSE)\n      .removeClass(ClassName.SHOW);\n\n    this.setTransitioning(true);\n\n    const complete = () => {\n      // check if disposed\n      if (!this.config || !this.element) {\n        return;\n      }\n      if (this.transitioning && this.config.onTransitionEnd) {\n        this.config.onTransitionEnd();\n      }\n\n      this.setTransitioning(false);\n      elem.trigger(Event.HIDDEN);\n\n      elem\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(ClassName.COLLAPSE);\n    };\n\n    if (elem.height() === 0 || elem.css('display') === 'none') {\n      complete();\n    } else {\n      elem\n        .height(0)\n        .one(Util.TRANSITION_END, complete)\n        .mmEmulateTransitionEnd(TRANSITION_DURATION);\n    }\n  }\n\n  setTransitioning(isTransitioning) {\n    this.transitioning = isTransitioning;\n  }\n\n  dispose() {\n    $.removeData(this.element, DATA_KEY);\n\n    $(this.element)\n      .find(this.config.parentTrigger)\n      .has(this.config.subMenu)\n      .children(this.config.triggerElement)\n      .off('click');\n\n    this.transitioning = null;\n    this.config = null;\n    this.element = null;\n  }\n\n  static jQueryInterface(config) {\n    // eslint-disable-next-line func-names\n    return this.each(function () {\n      const $this = $(this);\n      let data = $this.data(DATA_KEY);\n      const conf = {\n        ...Default,\n        ...$this.data(),\n        ...(typeof config === 'object' && config ? config : {}),\n      };\n\n      if (!data) {\n        data = new MetisMenu(this, conf);\n        $this.data(DATA_KEY, data);\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined) {\n          throw new Error(`No method named \"${config}\"`);\n        }\n        data[config]();\n      }\n    });\n  }\n}\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = MetisMenu.jQueryInterface; // eslint-disable-line no-param-reassign\n$.fn[NAME].Constructor = MetisMenu; // eslint-disable-line no-param-reassign\n$.fn[NAME].noConflict = () => {\n  // eslint-disable-line no-param-reassign\n  $.fn[NAME] = JQUERY_NO_CONFLICT; // eslint-disable-line no-param-reassign\n  return MetisMenu.jQueryInterface;\n};\n\nexport default MetisMenu;\n"]}