<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Personne;
use App\Models\Menu;
use App\Models\Permission;
use App\Models\PermissionMenu;
use Illuminate\Support\Facades\Hash;

class RolePermissionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    private function setupTestData()
    {
        // Créer les permissions
        $permissions = ['Créer', 'Lire', 'Modifier', 'Supprimer'];
        foreach ($permissions as $permissionName) {
            Permission::create(['permission_name' => $permissionName]);
        }

        // Créer les rôles
        $adminRole = Role::create(['role_name' => 'Administrateur']);
        $managerRole = Role::create(['role_name' => 'Gestionnaire']);
        $userRole = Role::create(['role_name' => 'Utilisateur']);

        // Créer les menus
        $dashboardMenu = Menu::create([
            'libelle' => 'Dashboard',
            'url' => '/dashboard',
            'icon' => 'educate-icon educate-home icon-wrap',
            'ordre' => 1,
            'is_active' => true
        ]);

        $roleMenu = Menu::create([
            'libelle' => 'Gestion des rôles',
            'url' => '/role',
            'icon' => 'educate-icon educate-department icon-wrap',
            'ordre' => 2,
            'is_active' => true
        ]);

        $userMenu = Menu::create([
            'libelle' => 'Gestion des utilisateurs',
            'url' => '/utilisateurs',
            'icon' => 'educate-icon educate-professor icon-wrap',
            'ordre' => 3,
            'is_active' => true
        ]);

        // Créer les associations permission-menu
        $readPermission = Permission::where('permission_name', 'Lire')->first();
        $createPermission = Permission::where('permission_name', 'Créer')->first();

        // Dashboard accessible en lecture à tous
        $dashboardPermMenu = PermissionMenu::create([
            'menu_id' => $dashboardMenu->id,
            'permission_id' => $readPermission->id
        ]);

        // Rôles accessible seulement aux admins
        $rolePermMenu = PermissionMenu::create([
            'menu_id' => $roleMenu->id,
            'permission_id' => $readPermission->id
        ]);

        // Utilisateurs accessible seulement aux admins
        $userPermMenu = PermissionMenu::create([
            'menu_id' => $userMenu->id,
            'permission_id' => $readPermission->id
        ]);

        // Assigner les permissions aux rôles
        $adminRole->permissionMenus()->attach([$dashboardPermMenu->id, $rolePermMenu->id, $userPermMenu->id]);
        $managerRole->permissionMenus()->attach([$dashboardPermMenu->id]);
        $userRole->permissionMenus()->attach([$dashboardPermMenu->id]);

        // Créer les utilisateurs de test
        $this->createTestUsers($adminRole, $managerRole, $userRole);
    }

    private function createTestUsers($adminRole, $managerRole, $userRole)
    {
        // Utilisateur Admin
        $adminPersonne = Personne::create([
            'nom' => 'Admin',
            'prenom' => 'Test',
            'fonction' => 'Administrateur',
            'tel' => '1234567890',
            'email' => '<EMAIL>'
        ]);

        $this->adminUser = User::create([
            'personne_id' => $adminPersonne->id,
            'pwd' => Hash::make('admin123'),
            'status' => 1
        ]);
        $this->adminUser->roles()->attach($adminRole->id);

        // Utilisateur Manager
        $managerPersonne = Personne::create([
            'nom' => 'Manager',
            'prenom' => 'Test',
            'fonction' => 'Gestionnaire',
            'tel' => '1234567891',
            'email' => '<EMAIL>'
        ]);

        $this->managerUser = User::create([
            'personne_id' => $managerPersonne->id,
            'pwd' => Hash::make('manager123'),
            'status' => 1
        ]);
        $this->managerUser->roles()->attach($managerRole->id);

        // Utilisateur simple
        $userPersonne = Personne::create([
            'nom' => 'User',
            'prenom' => 'Test',
            'fonction' => 'Utilisateur',
            'tel' => '1234567892',
            'email' => '<EMAIL>'
        ]);

        $this->simpleUser = User::create([
            'personne_id' => $userPersonne->id,
            'pwd' => Hash::make('user123'),
            'status' => 1
        ]);
        $this->simpleUser->roles()->attach($userRole->id);
    }

    public function test_admin_has_all_permissions()
    {
        $this->assertTrue($this->adminUser->hasRole('Administrateur'));
        $this->assertTrue($this->adminUser->canAccessMenu(1)); // Dashboard
        $this->assertTrue($this->adminUser->canAccessMenu(2)); // Rôles
        $this->assertTrue($this->adminUser->canAccessMenu(3)); // Utilisateurs
    }

    public function test_manager_has_limited_permissions()
    {
        $this->assertTrue($this->managerUser->hasRole('Gestionnaire'));
        $this->assertTrue($this->managerUser->canAccessMenu(1)); // Dashboard
        $this->assertFalse($this->managerUser->canAccessMenu(2)); // Rôles
        $this->assertFalse($this->managerUser->canAccessMenu(3)); // Utilisateurs
    }

    public function test_simple_user_has_minimal_permissions()
    {
        $this->assertTrue($this->simpleUser->hasRole('Utilisateur'));
        $this->assertTrue($this->simpleUser->canAccessMenu(1)); // Dashboard
        $this->assertFalse($this->simpleUser->canAccessMenu(2)); // Rôles
        $this->assertFalse($this->simpleUser->canAccessMenu(3)); // Utilisateurs
    }

    public function test_admin_can_access_role_management()
    {
        $response = $this->actingAs($this->adminUser)->get('/role');
        $response->assertStatus(200);
    }

    public function test_manager_cannot_access_role_management()
    {
        $response = $this->actingAs($this->managerUser)->get('/role');
        $response->assertStatus(403);
    }

    public function test_simple_user_cannot_access_role_management()
    {
        $response = $this->actingAs($this->simpleUser)->get('/role');
        $response->assertStatus(403);
    }

    public function test_admin_can_access_user_management()
    {
        $response = $this->actingAs($this->adminUser)->get('/utilisateurs');
        $response->assertStatus(200);
    }

    public function test_manager_cannot_access_user_management()
    {
        $response = $this->actingAs($this->managerUser)->get('/utilisateurs');
        $response->assertStatus(403);
    }

    public function test_all_users_can_access_dashboard()
    {
        $response = $this->actingAs($this->adminUser)->get('/dashboard');
        $response->assertStatus(200);

        $response = $this->actingAs($this->managerUser)->get('/dashboard');
        $response->assertStatus(200);

        $response = $this->actingAs($this->simpleUser)->get('/dashboard');
        $response->assertStatus(200);
    }

    public function test_unauthenticated_user_redirected_to_login()
    {
        $response = $this->get('/dashboard');
        $response->assertRedirect('/login');

        $response = $this->get('/role');
        $response->assertRedirect('/login');

        $response = $this->get('/utilisateurs');
        $response->assertRedirect('/login');
    }

    public function test_menu_component_shows_correct_menus_for_admin()
    {
        $this->actingAs($this->adminUser);
        $menus = $this->adminUser->getAccessibleMenus();
        
        $this->assertCount(3, $menus);
        $this->assertTrue($menus->contains('url', '/dashboard'));
        $this->assertTrue($menus->contains('url', '/role'));
        $this->assertTrue($menus->contains('url', '/utilisateurs'));
    }

    public function test_menu_component_shows_correct_menus_for_manager()
    {
        $this->actingAs($this->managerUser);
        $menus = $this->managerUser->getAccessibleMenus();
        
        $this->assertCount(1, $menus);
        $this->assertTrue($menus->contains('url', '/dashboard'));
        $this->assertFalse($menus->contains('url', '/role'));
        $this->assertFalse($menus->contains('url', '/utilisateurs'));
    }
}
