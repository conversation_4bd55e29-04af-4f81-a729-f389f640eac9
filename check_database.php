<?php

/**
 * Script simple pour vérifier le contenu de la base de données
 */

echo "🔍 VÉRIFICATION DU CONTENU DE LA BASE DE DONNÉES\n";
echo "===============================================\n\n";

// Vérifier quel fichier de base de données est utilisé
$envFile = '.env';
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);
    if (preg_match('/DB_DATABASE=(.+)/', $envContent, $matches)) {
        $dbPath = trim($matches[1]);
        echo "📁 Base de données configurée : $dbPath\n";
        
        if (file_exists($dbPath)) {
            echo "✅ Fichier de base de données trouvé\n";
            echo "📊 Taille du fichier : " . number_format(filesize($dbPath) / 1024, 2) . " KB\n";
        } else {
            echo "❌ Fichier de base de données non trouvé\n";
        }
    }
}

echo "\n";

// Lister tous les fichiers de base de données disponibles
echo "📂 FICHIERS DE BASE DE DONNÉES DISPONIBLES :\n";
echo "============================================\n";

$dbFiles = [
    'database/database.sqlite',
    'database/database_new.sqlite', 
    'database/database_backup.sqlite'
];

foreach ($dbFiles as $file) {
    if (file_exists($file)) {
        $size = number_format(filesize($file) / 1024, 2);
        $modified = date('Y-m-d H:i:s', filemtime($file));
        echo "✅ $file - Taille: {$size} KB - Modifié: $modified\n";
    } else {
        echo "❌ $file - Non trouvé\n";
    }
}

echo "\n";

// Essayer de se connecter à SQLite directement
echo "🔗 TENTATIVE DE CONNEXION DIRECTE À SQLITE :\n";
echo "============================================\n";

try {
    // Essayer avec database.sqlite
    if (file_exists('database/database.sqlite')) {
        $pdo = new PDO('sqlite:database/database.sqlite');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "✅ Connexion réussie à database.sqlite\n";
        
        // Lister les tables
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "📋 Tables trouvées (" . count($tables) . ") :\n";
        foreach ($tables as $table) {
            // Compter les enregistrements
            $countStmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $countStmt->fetchColumn();
            echo "   • $table : $count enregistrements\n";
        }
        
        echo "\n";
        
        // Afficher quelques données importantes
        echo "👥 DONNÉES UTILISATEURS :\n";
        echo "========================\n";
        try {
            $stmt = $pdo->query("SELECT id, nom, prenom, email, role_id FROM users LIMIT 10");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($users) > 0) {
                foreach ($users as $user) {
                    echo "• ID: {$user['id']} - {$user['nom']} {$user['prenom']} - {$user['email']} - Rôle: {$user['role_id']}\n";
                }
            } else {
                echo "Aucun utilisateur trouvé.\n";
            }
        } catch (Exception $e) {
            echo "Erreur lors de la récupération des utilisateurs : " . $e->getMessage() . "\n";
        }
        
        echo "\n";
        
        echo "🔄 DONNÉES RÉFORMES :\n";
        echo "====================\n";
        try {
            $stmt = $pdo->query("SELECT id, titre, statut_manuel, date_debut, date_fin FROM reformes LIMIT 10");
            $reformes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($reformes) > 0) {
                foreach ($reformes as $reforme) {
                    echo "• ID: {$reforme['id']} - {$reforme['titre']} - Statut: {$reforme['statut_manuel']} - Période: {$reforme['date_debut']} → {$reforme['date_fin']}\n";
                }
            } else {
                echo "Aucune réforme trouvée.\n";
            }
        } catch (Exception $e) {
            echo "Erreur lors de la récupération des réformes : " . $e->getMessage() . "\n";
        }
        
        echo "\n";
        
        echo "📊 DONNÉES INDICATEURS :\n";
        echo "=======================\n";
        try {
            $stmt = $pdo->query("SELECT id, libelle, unite, type FROM indicateurs LIMIT 10");
            $indicateurs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($indicateurs) > 0) {
                foreach ($indicateurs as $indicateur) {
                    echo "• ID: {$indicateur['id']} - {$indicateur['libelle']} - Unité: {$indicateur['unite']} - Type: {$indicateur['type']}\n";
                }
            } else {
                echo "Aucun indicateur trouvé.\n";
            }
        } catch (Exception $e) {
            echo "Erreur lors de la récupération des indicateurs : " . $e->getMessage() . "\n";
        }
        
        echo "\n";
        
        echo "🔗 ASSOCIATIONS RÉFORME-INDICATEUR :\n";
        echo "===================================\n";
        try {
            $stmt = $pdo->query("SELECT id, reforme_id, indicateur_id, valeur_cible FROM reformes_indicateurs LIMIT 10");
            $associations = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($associations) > 0) {
                foreach ($associations as $assoc) {
                    echo "• ID: {$assoc['id']} - Réforme: {$assoc['reforme_id']} - Indicateur: {$assoc['indicateur_id']} - Cible: {$assoc['valeur_cible']}\n";
                }
            } else {
                echo "Aucune association trouvée.\n";
            }
        } catch (Exception $e) {
            echo "Erreur lors de la récupération des associations : " . $e->getMessage() . "\n";
        }
        
        echo "\n";
        
        echo "📈 ÉVOLUTIONS D'INDICATEURS :\n";
        echo "============================\n";
        try {
            $stmt = $pdo->query("SELECT reforme_indicateur_id, date_evolution, valeur FROM evolution_indicateurs ORDER BY date_evolution DESC LIMIT 10");
            $evolutions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($evolutions) > 0) {
                foreach ($evolutions as $evolution) {
                    echo "• Association: {$evolution['reforme_indicateur_id']} - Date: {$evolution['date_evolution']} - Valeur: {$evolution['valeur']}\n";
                }
            } else {
                echo "Aucune évolution trouvée.\n";
            }
        } catch (Exception $e) {
            echo "Erreur lors de la récupération des évolutions : " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "❌ Fichier database.sqlite non trouvé\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur de connexion : " . $e->getMessage() . "\n";
}

echo "\n🎉 VÉRIFICATION TERMINÉE\n";
echo "========================\n";

?>
