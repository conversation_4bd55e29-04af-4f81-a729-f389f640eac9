<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Models\PermissionMenu;
use App\Models\RolePermissionMenu;
use App\Services\PermissionService;
use Illuminate\Support\Facades\Auth;

class RolePermissionSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $permissionService;
    protected $adminUser;
    protected $gestionnaireUser;
    protected $utilisateurUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->permissionService = app(PermissionService::class);
        
        // Créer les données de test
        $this->createTestData();
    }

    /**
     * Créer les données de test
     */
    private function createTestData()
    {
        // Créer les permissions
        $permissions = ['Créer', 'Lire', 'Modifier', 'Supprimer'];
        foreach ($permissions as $permName) {
            Permission::create([
                'permission_name' => $permName,
                'description' => "Permission $permName",
                'is_active' => true
            ]);
        }

        // Créer les menus
        $menus = [
            ['libelle' => 'Dashboard', 'url' => '/dashboard', 'icone' => 'fa-dashboard', 'ordre' => 1],
            ['libelle' => 'Réformes', 'url' => '/reforme', 'icone' => 'fa-file', 'ordre' => 2],
            ['libelle' => 'Activités', 'url' => '/activites', 'icone' => 'fa-tasks', 'ordre' => 3],
        ];

        foreach ($menus as $menuData) {
            Menu::create(array_merge($menuData, ['is_active' => true]));
        }

        // Créer les associations permission-menu
        $this->permissionService->createPermissionMenuAssociations();

        // Créer les rôles
        $adminRole = Role::create([
            'role_name' => 'Administrateur',
            'description' => 'Accès complet',
            'is_active' => true
        ]);

        $gestionnaireRole = Role::create([
            'role_name' => 'Gestionnaire',
            'description' => 'Accès limité',
            'is_active' => true
        ]);

        $utilisateurRole = Role::create([
            'role_name' => 'Utilisateur',
            'description' => 'Lecture seule',
            'is_active' => true
        ]);

        // Assigner les permissions aux rôles
        $this->permissionService->assignPermissionsToRole($adminRole);
        $this->permissionService->assignPermissionsToRole($gestionnaireRole);
        $this->permissionService->assignPermissionsToRole($utilisateurRole);

        // Créer les utilisateurs
        $this->adminUser = User::factory()->create(['email' => '<EMAIL>']);
        $this->gestionnaireUser = User::factory()->create(['email' => '<EMAIL>']);
        $this->utilisateurUser = User::factory()->create(['email' => '<EMAIL>']);

        // Assigner les rôles
        $this->adminUser->roles()->attach($adminRole->id);
        $this->gestionnaireUser->roles()->attach($gestionnaireRole->id);
        $this->utilisateurUser->roles()->attach($utilisateurRole->id);
    }

    /**
     * Test que l'administrateur a accès à tout
     */
    public function test_admin_has_full_access()
    {
        $this->assertTrue($this->adminUser->hasRole('Administrateur'));
        $this->assertTrue($this->adminUser->canAccessMenu(1)); // Dashboard
        $this->assertTrue($this->adminUser->hasPermission(2, 'Créer')); // Réformes - Créer
        $this->assertTrue($this->adminUser->hasPermission(3, 'Supprimer')); // Activités - Supprimer
    }

    /**
     * Test que le gestionnaire a un accès limité
     */
    public function test_gestionnaire_has_limited_access()
    {
        $this->assertTrue($this->gestionnaireUser->hasRole('Gestionnaire'));
        $this->assertTrue($this->gestionnaireUser->canAccessMenu(1)); // Dashboard
        $this->assertTrue($this->gestionnaireUser->hasPermission(2, 'Créer')); // Réformes - Créer
        
        // Le gestionnaire ne peut pas supprimer selon la config
        $canDelete = $this->gestionnaireUser->hasPermission(2, 'Supprimer'); // Réformes - Supprimer
        // Selon la configuration, cela dépend de ce qui est défini pour le gestionnaire
    }

    /**
     * Test que l'utilisateur n'a que l'accès en lecture
     */
    public function test_utilisateur_has_read_only_access()
    {
        $this->assertTrue($this->utilisateurUser->hasRole('Utilisateur'));
        $this->assertTrue($this->utilisateurUser->canAccessMenu(1)); // Dashboard
        $this->assertTrue($this->utilisateurUser->hasPermission(3, 'Lire')); // Activités - Lire
        
        // L'utilisateur ne peut pas créer
        $this->assertFalse($this->utilisateurUser->hasPermission(2, 'Créer')); // Réformes - Créer
    }

    /**
     * Test du service de permissions pour URL
     */
    public function test_permission_service_url_access()
    {
        // Test accès admin
        $this->assertTrue(
            $this->permissionService->userCanAccessUrl($this->adminUser, '/dashboard')
        );
        
        $this->assertTrue(
            $this->permissionService->userHasPermissionForUrl($this->adminUser, '/reforme', 'Créer')
        );

        // Test accès utilisateur limité
        $this->assertTrue(
            $this->permissionService->userHasPermissionForUrl($this->utilisateurUser, '/activites', 'Lire')
        );
        
        $this->assertFalse(
            $this->permissionService->userHasPermissionForUrl($this->utilisateurUser, '/reforme', 'Créer')
        );
    }

    /**
     * Test du middleware de permissions
     */
    public function test_permission_middleware()
    {
        // Test accès autorisé
        $response = $this->actingAs($this->adminUser)
                         ->get('/dashboard');
        
        $response->assertStatus(200);

        // Test accès refusé (utilisateur sans permissions suffisantes)
        $response = $this->actingAs($this->utilisateurUser)
                         ->post('/reforme', [
                             'titre' => 'Test Réforme',
                             'objectifs' => 'Test',
                             'budget' => 1000
                         ]);
        
        // Devrait être redirigé ou recevoir une erreur 403
        $this->assertTrue(
            $response->isRedirection() || $response->status() === 403
        );
    }

    /**
     * Test de génération de rapport de permissions
     */
    public function test_user_permission_report()
    {
        $report = $this->permissionService->generateUserPermissionReport($this->adminUser);
        
        $this->assertArrayHasKey('user_id', $report);
        $this->assertArrayHasKey('roles', $report);
        $this->assertArrayHasKey('menus', $report);
        
        $this->assertEquals($this->adminUser->id, $report['user_id']);
        $this->assertContains('Administrateur', $report['roles']);
        $this->assertNotEmpty($report['menus']);
    }

    /**
     * Test des directives Blade (simulation)
     */
    public function test_blade_directives_logic()
    {
        // Simuler la logique des directives Blade
        Auth::login($this->adminUser);
        
        // Test @canCreateUrl
        $canCreate = auth()->check() && 
                    $this->permissionService->userHasPermissionForUrl(auth()->user(), '/reforme', 'Créer');
        $this->assertTrue($canCreate);

        // Test avec utilisateur limité
        Auth::login($this->utilisateurUser);
        
        $canCreate = auth()->check() && 
                    $this->permissionService->userHasPermissionForUrl(auth()->user(), '/reforme', 'Créer');
        $this->assertFalse($canCreate);
    }

    /**
     * Test de synchronisation des permissions
     */
    public function test_permission_synchronization()
    {
        // Supprimer toutes les permissions d'un rôle
        $role = Role::where('role_name', 'Gestionnaire')->first();
        $role->permissionMenus()->detach();
        
        // Vérifier qu'il n'a plus de permissions
        $this->assertEquals(0, $role->permissionMenus()->count());
        
        // Synchroniser
        $this->permissionService->assignPermissionsToRole($role);
        
        // Vérifier que les permissions ont été restaurées
        $this->assertGreaterThan(0, $role->fresh()->permissionMenus()->count());
    }

    /**
     * Test de gestion des erreurs
     */
    public function test_error_handling()
    {
        // Test avec menu inexistant
        $result = $this->permissionService->userHasPermissionForUrl(
            $this->adminUser, 
            '/menu-inexistant', 
            'Lire'
        );
        
        $this->assertFalse($result);

        // Test avec utilisateur sans rôles
        $userWithoutRole = User::factory()->create(['email' => '<EMAIL>']);
        
        $result = $this->permissionService->userCanAccessUrl($userWithoutRole, '/dashboard');
        $this->assertFalse($result);
    }
}
