<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RoleSeeder::class,
            // PersonneSeeder::class,  // Commenté car on importe depuis l'ancienne base
            // UsersTableSeeder::class, // Commenté car on importe depuis l'ancienne base
            ImportOldDataSeeder::class,
            UserRoleSeeder::class,
            AhoRoleSeeder::class,  // C<PERSON>er le rôle 'aho' et ses permissions
            NotificationSeeder::class,
            // TestDataSeeder::class,   // Commenté car on a les vraies données
        ]);
    }
}
