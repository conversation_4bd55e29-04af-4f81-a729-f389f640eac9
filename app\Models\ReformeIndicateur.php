<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ReformeIndicateur extends Model
{
    protected $table = 'reformes_indicateurs';

    protected $fillable = [
        'reforme_id',
        'indicateur_id'
    ];

    /**
     * Relation avec la réforme
     */
    public function reforme()
    {
        return $this->belongsTo(Reforme::class, 'reforme_id');
    }

    /**
     * Relation avec l'indicateur
     */
    public function indicateur()
    {
        return $this->belongsTo(Indicateur::class, 'indicateur_id');
    }

    /**
     * Relation avec les évolutions
     */
    public function evolutions()
    {
        return $this->hasMany(EvolutionIndicateur::class, 'reforme_indicateur_id');
    }

    /**
     * Obtenir la dernière évolution
     */
    public function getDerniereEvolution()
    {
        return $this->evolutions()->orderBy('date_evolution', 'desc')->first();
    }

    /**
     * Obtenir la première évolution
     */
    public function getPremiereEvolution()
    {
        return $this->evolutions()->orderBy('date_evolution', 'asc')->first();
    }

    /**
     * Obtenir la valeur actuelle de l'indicateur
     */
    public function getValeurActuelleAttribute()
    {
        $derniere = $this->getDerniereEvolution();
        return $derniere ? $derniere->valeur : null;
    }

    /**
     * Obtenir la date de la dernière mesure
     */
    public function getDateDerniereMesureAttribute()
    {
        $derniere = $this->getDerniereEvolution();
        return $derniere ? $derniere->date_evolution : null;
    }

    /**
     * Vérifier si l'indicateur a des données
     */
    public function hasDonnees()
    {
        return $this->evolutions()->count() > 0;
    }

    /**
     * Obtenir le nombre total de mesures
     */
    public function getNombreMesuresAttribute()
    {
        return $this->evolutions()->count();
    }

    /**
     * Obtenir les évolutions pour une période donnée
     */
    public function getEvolutionsPourPeriode($dateDebut, $dateFin)
    {
        return $this->evolutions()
                    ->whereBetween('date_evolution', [$dateDebut, $dateFin])
                    ->orderBy('date_evolution')
                    ->get();
    }

    /**
     * Calculer la progression depuis le début
     */
    public function getProgressionAttribute()
    {
        $premiere = $this->getPremiereEvolution();
        $derniere = $this->getDerniereEvolution();

        if (!$premiere || !$derniere || $premiere->valeur == 0) {
            return 0;
        }

        return round((($derniere->valeur - $premiere->valeur) / $premiere->valeur) * 100, 2);
    }

    /**
     * Obtenir la tendance générale
     */
    public function getTendanceGeneraleAttribute()
    {
        $progression = $this->progression;
        
        if ($progression > 5) {
            return 'positive';
        } elseif ($progression < -5) {
            return 'negative';
        } else {
            return 'stable';
        }
    }

    /**
     * Obtenir l'icône de tendance générale
     */
    public function getIconeTendanceGeneraleAttribute()
    {
        switch ($this->tendance_generale) {
            case 'positive':
                return '<i class="fa fa-trending-up text-success"></i>';
            case 'negative':
                return '<i class="fa fa-trending-down text-danger"></i>';
            default:
                return '<i class="fa fa-minus text-muted"></i>';
        }
    }

    /**
     * Obtenir les données pour un graphique
     */
    public function getDonneesGraphique()
    {
        $evolutions = $this->evolutions()->orderBy('date_evolution')->get();
        
        return [
            'labels' => $evolutions->pluck('date_formatee')->toArray(),
            'data' => $evolutions->pluck('valeur')->toArray(),
            'indicateur' => $this->indicateur->libelle,
            'unite' => $this->indicateur->unite
        ];
    }

    /**
     * Méthode statique pour obtenir tous les indicateurs d'une réforme avec leurs données
     */
    public static function getIndicateursAvecDonnees($reformeId)
    {
        return self::where('reforme_id', $reformeId)
                   ->with(['indicateur', 'evolutions' => function($query) {
                       $query->orderBy('date_evolution', 'desc');
                   }])
                   ->get();
    }

    /**
     * Ajouter une nouvelle mesure
     */
    public function ajouterMesure($date, $valeur)
    {
        return EvolutionIndicateur::updateOrCreate([
            'reforme_indicateur_id' => $this->id,
            'date_evolution' => $date
        ], [
            'valeur' => $valeur
        ]);
    }

    /**
     * Supprimer une mesure
     */
    public function supprimerMesure($date)
    {
        return $this->evolutions()
                    ->where('date_evolution', $date)
                    ->delete();
    }

    /**
     * Obtenir les statistiques complètes
     */
    public function getStatistiquesCompletes()
    {
        $evolutions = $this->evolutions()->orderBy('date_evolution')->get();
        
        if ($evolutions->isEmpty()) {
            return null;
        }

        return [
            'indicateur' => $this->indicateur->libelle,
            'unite' => $this->indicateur->unite,
            'reforme' => $this->reforme->titre,
            'nombre_mesures' => $evolutions->count(),
            'valeur_initiale' => $evolutions->first()->valeur,
            'valeur_actuelle' => $evolutions->last()->valeur,
            'valeur_min' => $evolutions->min('valeur'),
            'valeur_max' => $evolutions->max('valeur'),
            'moyenne' => round($evolutions->avg('valeur'), 2),
            'progression_pct' => $this->progression,
            'tendance' => $this->tendance_generale,
            'periode_debut' => $evolutions->first()->date_evolution,
            'periode_fin' => $evolutions->last()->date_evolution,
            'donnees_graphique' => $this->getDonneesGraphique()
        ];
    }
}
