<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Models\User;

class UsersTableSeeder extends Seeder
{
    public function run()
    {
        // Créer les utilisateurs
        User::create([
            'id' => 1,
            'personne_id' => 1,
            'pwd' => Hash::make('password1'),
            'status' => 1,
        ]);

        User::create([
            'id' => 2,
            'personne_id' => 3,
            'pwd' => Hash::make('password4'),
            'status' => 1,
        ]);

        User::create([
            'id' => 3,
            'personne_id' => 4,
            'pwd' => Hash::make('password5'),
            'status' => 1,
        ]);

        // Assigner les rôles aux utilisateurs
        DB::table('user_role')->insert([
            [
                'id_user' => 1,
                'role_id' => 1, // Administrateur
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id_user' => 2,
                'role_id' => 2, // Gestionnaire
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id_user' => 3,
                'role_id' => 3, // Utilisateur
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
