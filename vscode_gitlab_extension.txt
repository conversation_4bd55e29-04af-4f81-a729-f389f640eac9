VS CODE + EXTENSION GITLAB
==========================

🔌 INSTALLATION EXTENSION:
1. Ouvrir VS Code
2. Aller dans Extensions (Ctrl+Shift+X)
3. <PERSON><PERSON>cher "GitLab Workflow"
4. Installer l'extension officielle GitLab

📂 OUVERTURE DU PROJET:
1. File → Open Folder
2. Sélectionner votre dossier "suivi-reforme"

🔧 CONFIGURATION:
1. Ctrl+Shift+P → "GitLab: Set GitLab URL"
2. Entrer: https://gitlab.com
3. Ajouter votre token d'accès GitLab

🚀 DÉPLOIEMENT INTÉGRÉ:
1. <PERSON><PERSON><PERSON> "Source Control" (Ctrl+Shift+G)
2. Voir tous les fichiers modifiés
3. <PERSON><PERSON>r "+" pour ajouter tous les fichiers
4. Écrire un message de commit
5. C<PERSON>r "✓ Commit"
6. <PERSON><PERSON>r "..." → "Push"

✅ AVANTAGES:
- Intégration complète GitLab
- Gestion des merge requests
- Aperçu des pipelines CI/CD
- Tout depuis votre éditeur
