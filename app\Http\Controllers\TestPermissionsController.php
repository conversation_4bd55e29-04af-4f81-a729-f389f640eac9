<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Services\PermissionService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class TestPermissionsController extends Controller
{
    public function index()
    {
        $results = [];
        $permissionService = new PermissionService();

        try {
            // Récupérer tous les rôles dynamiquement
            $roles = Role::all();
            $testUsers = [];

            // C<PERSON>er ou récupérer des utilisateurs de test pour chaque rôle
            foreach ($roles as $role) {
                $email = strtolower($role->role_name) . '@test.com';
                $testUsers[$role->role_name] = $this->getOrCreateUser(
                    $email,
                    $role->role_name,
                    'Test',
                    $role->role_name
                );
            }

            // Récupérer tous les menus
            $menus = Menu::orderBy('ordre')->get();
            $permissions = Permission::all();

            if ($menus->isEmpty()) {
                $results['error'] = 'Aucun menu trouvé dans le système';
                return view('test-permissions', compact('results'));
            }

            // Tests génériques pour tous les menus et tous les rôles
            $results['tests'] = [];

            foreach ($menus as $menu) {
                $menuTests = [
                    'menu' => [
                        'id' => $menu->id,
                        'libelle' => $menu->libelle,
                        'url' => $menu->url
                    ],
                    'roles' => []
                ];

                foreach ($roles as $role) {
                    $user = $testUsers[$role->role_name];
                    $roleTests = [
                        'user' => $user->email,
                        'role' => $role->role_name,
                        'permissions' => []
                    ];

                    // Tester chaque permission CRUD
                    foreach ($permissions as $permission) {
                        $hasPermission = $user->hasPermission($menu->id, $permission->permission_name);
                        $roleTests['permissions'][$permission->permission_name] = $hasPermission;
                    }

                    // Tester les méthodes génériques
                    $roleTests['generic_tests'] = [
                        'can_access_menu' => $user->canAccessMenu($menu->id),
                        'permissions_for_menu' => $user->getPermissionsForMenu($menu->id),
                        'permissions_for_url' => $user->getPermissionsForUrl($menu->url),
                        'is_super_admin' => $user->isSuperAdmin(),
                        'role_names' => $user->getRoleNames()
                    ];

                    $menuTests['roles'][$role->role_name] = $roleTests;
                }

                $results['tests'][$menu->url] = $menuTests;
            }

            // Tests des méthodes génériques sur différentes URLs
            $testUrls = ['/activites', '/reforme', '/dashboard'];
            $results['url_tests'] = [];

            foreach ($testUrls as $url) {
                $urlTests = [];
                foreach ($roles as $role) {
                    $user = $testUsers[$role->role_name];
                    $urlTests[$role->role_name] = [
                        'can_create' => $user->canCreateForUrl($url),
                        'can_read' => $user->canReadForUrl($url),
                        'can_edit' => $user->canEditForUrl($url),
                        'can_delete' => $user->canDeleteForUrl($url),
                        'permissions_for_url' => $user->getPermissionsForUrl($url)
                    ];
                }
                $results['url_tests'][$url] = $urlTests;
            }

            // Statistiques générales
            $results['statistics'] = [
                'total_roles' => $roles->count(),
                'total_menus' => $menus->count(),
                'total_permissions' => $permissions->count(),
                'system_roles' => config('permissions.system_roles', []),
                'roles_list' => $roles->pluck('role_name')->toArray()
            ];

        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
            $results['trace'] = $e->getTraceAsString();
        }

        return view('test-permissions-generic', compact('results'));
    }

    private function getOrCreateUser($email, $nom, $prenom, $roleName)
    {
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $user = User::create([
                'nom' => $nom,
                'prenom' => $prenom,
                'email' => $email,
                'pwd' => Hash::make('password'),
            ]);

            $role = Role::where('role_name', $roleName)->first();
            if ($role) {
                $user->roles()->attach($role);
            }
        }

        return $user;
    }
}
