<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Role;
use App\Models\Menu;
use Illuminate\Support\Facades\Hash;

// Configuration de l'application Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Test des Permissions Granulaires ===\n\n";

try {
    // Récupérer les utilisateurs de test
    $adminUser = User::where('email', '<EMAIL>')->first();
    $managerUser = User::where('email', '<EMAIL>')->first();
    $regularUser = User::where('email', '<EMAIL>')->first();

    // Si les utilisateurs n'existent pas, les créer
    if (!$adminUser) {
        $adminUser = User::create([
            'nom' => 'Admin',
            'prenom' => 'Test',
            'email' => '<EMAIL>',
            'pwd' => Hash::make('password'),
        ]);
        $adminRole = Role::where('role_name', 'Administrateur')->first();
        if ($adminRole) {
            $adminUser->roles()->attach($adminRole);
        }
    }

    if (!$managerUser) {
        $managerUser = User::create([
            'nom' => 'Manager',
            'prenom' => 'Test',
            'email' => '<EMAIL>',
            'pwd' => Hash::make('password'),
        ]);
        $managerRole = Role::where('role_name', 'Gestionnaire')->first();
        if ($managerRole) {
            $managerUser->roles()->attach($managerRole);
        }
    }

    if (!$regularUser) {
        $regularUser = User::create([
            'nom' => 'User',
            'prenom' => 'Test',
            'email' => '<EMAIL>',
            'pwd' => Hash::make('password'),
        ]);
        $userRole = Role::where('role_name', 'Utilisateur')->first();
        if ($userRole) {
            $regularUser->roles()->attach($userRole);
        }
    }

    // Récupérer l'ID du menu activités
    $activitesMenu = Menu::where('url', '/activites')->first();
    if (!$activitesMenu) {
        echo "❌ Menu activités non trouvé\n";
        exit(1);
    }

    $activitesMenuId = $activitesMenu->id;

    echo "📋 Test des permissions pour le menu Activités (ID: $activitesMenuId)\n\n";

    // Test Administrateur
    echo "👤 ADMINISTRATEUR ({$adminUser->email}):\n";
    echo "  - Créer: " . ($adminUser->hasPermission($activitesMenuId, 'Créer') ? '✅' : '❌') . "\n";
    echo "  - Lire: " . ($adminUser->hasPermission($activitesMenuId, 'Lire') ? '✅' : '❌') . "\n";
    echo "  - Modifier: " . ($adminUser->hasPermission($activitesMenuId, 'Modifier') ? '✅' : '❌') . "\n";
    echo "  - Supprimer: " . ($adminUser->hasPermission($activitesMenuId, 'Supprimer') ? '✅' : '❌') . "\n\n";

    // Test Gestionnaire
    echo "👤 GESTIONNAIRE ({$managerUser->email}):\n";
    echo "  - Créer: " . ($managerUser->hasPermission($activitesMenuId, 'Créer') ? '✅' : '❌') . "\n";
    echo "  - Lire: " . ($managerUser->hasPermission($activitesMenuId, 'Lire') ? '✅' : '❌') . "\n";
    echo "  - Modifier: " . ($managerUser->hasPermission($activitesMenuId, 'Modifier') ? '✅' : '❌') . "\n";
    echo "  - Supprimer: " . ($managerUser->hasPermission($activitesMenuId, 'Supprimer') ? '✅' : '❌') . "\n\n";

    // Test Utilisateur
    echo "👤 UTILISATEUR ({$regularUser->email}):\n";
    echo "  - Créer: " . ($regularUser->hasPermission($activitesMenuId, 'Créer') ? '✅' : '❌') . "\n";
    echo "  - Lire: " . ($regularUser->hasPermission($activitesMenuId, 'Lire') ? '✅' : '❌') . "\n";
    echo "  - Modifier: " . ($regularUser->hasPermission($activitesMenuId, 'Modifier') ? '✅' : '❌') . "\n";
    echo "  - Supprimer: " . ($regularUser->hasPermission($activitesMenuId, 'Supprimer') ? '✅' : '❌') . "\n\n";

    // Test des méthodes de commodité
    echo "🔧 Test des méthodes de commodité:\n";
    echo "Admin canCreateForUrl('/activites'): " . ($adminUser->canCreateForUrl('/activites') ? '✅' : '❌') . "\n";
    echo "User canCreateForUrl('/activites'): " . ($regularUser->canCreateForUrl('/activites') ? '✅' : '❌') . "\n";
    echo "User canReadForUrl('/activites'): " . ($regularUser->canReadForUrl('/activites') ? '✅' : '❌') . "\n\n";

    // Test pour le menu des rôles (accès administrateur uniquement)
    $roleMenu = Menu::where('url', '/role')->first();
    if ($roleMenu) {
        echo "🔐 Test d'accès au menu Rôles (Admin uniquement):\n";
        echo "Admin peut accéder: " . ($adminUser->hasPermission($roleMenu->id, 'Lire') ? '✅' : '❌') . "\n";
        echo "Manager peut accéder: " . ($managerUser->hasPermission($roleMenu->id, 'Lire') ? '✅' : '❌') . "\n";
        echo "User peut accéder: " . ($regularUser->hasPermission($roleMenu->id, 'Lire') ? '✅' : '❌') . "\n\n";
    }

    echo "✅ Tests terminés avec succès!\n";

} catch (Exception $e) {
    echo "❌ Erreur lors du test: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
