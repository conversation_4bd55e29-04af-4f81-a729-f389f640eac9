<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Indicateur;
use App\Models\Reforme;
use App\Models\ReformeIndicateur;
use App\Models\EvolutionIndicateur;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class SuiviIndicateursTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Créer un utilisateur de test
        $this->user = User::create([
            'nom' => 'Test',
            'prenom' => 'User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role_id' => 1 // Administrateur
        ]);
    }

    /** @test */
    public function test_dashboard_principal_affiche_section_indicateurs()
    {
        $this->actingAs($this->user);

        $response = $this->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Suivi des Indicateurs');
        $response->assertSee('Tableau de Bord Complet');
    }

    /** @test */
    public function test_page_suivi_indicateurs_accessible_avec_permissions()
    {
        $this->actingAs($this->user);

        // Créer quelques données de test
        $this->createTestData();

        $response = $this->get('/dashboard/suivi-indicateurs');

        $response->assertStatus(200);
        $response->assertSee('Suivi des Indicateurs');
        $response->assertSee('Total Indicateurs');
        $response->assertSee('Indicateurs Actifs');
    }

    /** @test */
    public function test_api_suivi_indicateurs_retourne_json()
    {
        $this->actingAs($this->user);

        // Créer quelques données de test
        $this->createTestData();

        $response = $this->get('/dashboard/suivi-indicateurs/api');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'statistiques' => [
                'total_indicateurs',
                'indicateurs_actifs',
                'total_mesures',
                'mesures_recentes',
                'pourcentage_actifs'
            ],
            'evolutions_recentes',
            'timestamp'
        ]);
    }

    /** @test */
    public function test_statistiques_indicateurs_calcul_correct()
    {
        $this->actingAs($this->user);

        // Créer des données de test spécifiques
        $indicateur1 = Indicateur::create([
            'libelle' => 'Test Indicateur 1',
            'description' => 'Description test',
            'unite' => '%',
            'type' => 'Quantitatif',
            'frequence_collecte' => 'Mensuelle'
        ]);

        $indicateur2 = Indicateur::create([
            'libelle' => 'Test Indicateur 2',
            'description' => 'Description test',
            'unite' => 'jours',
            'type' => 'Quantitatif',
            'frequence_collecte' => 'Hebdomadaire'
        ]);

        $reforme = Reforme::create([
            'titre' => 'Test Réforme',
            'description' => 'Description test',
            'date_debut' => Carbon::now()->subMonths(3),
            'date_fin' => Carbon::now()->addMonths(3),
            'type_reforme' => 1
        ]);

        // Créer une association
        $association = ReformeIndicateur::create([
            'reforme_id' => $reforme->id,
            'indicateur_id' => $indicateur1->id,
            'valeur_cible' => 80,
            'date_debut_suivi' => $reforme->date_debut,
            'date_fin_suivi' => $reforme->date_fin
        ]);

        // Créer des évolutions
        EvolutionIndicateur::create([
            'reforme_indicateur_id' => $association->id,
            'date_evolution' => Carbon::now()->subDays(10),
            'valeur' => 65.5,
            'created_at' => Carbon::now()->subDays(10)
        ]);

        EvolutionIndicateur::create([
            'reforme_indicateur_id' => $association->id,
            'date_evolution' => Carbon::now()->subDays(5),
            'valeur' => 70.2,
            'created_at' => Carbon::now()->subDays(5)
        ]);

        $response = $this->get('/dashboard/suivi-indicateurs/api');

        $data = $response->json();

        // Vérifier les statistiques
        $this->assertEquals(2, $data['statistiques']['total_indicateurs']);
        $this->assertEquals(1, $data['statistiques']['indicateurs_actifs']);
        $this->assertEquals(2, $data['statistiques']['total_mesures']);
        $this->assertEquals(2, $data['statistiques']['mesures_recentes']);
        $this->assertEquals(50.0, $data['statistiques']['pourcentage_actifs']);
    }

    /** @test */
    public function test_evolutions_recentes_format_correct()
    {
        $this->actingAs($this->user);

        // Créer des données de test
        $this->createTestData();

        $response = $this->get('/dashboard/suivi-indicateurs/api');
        $data = $response->json();

        if (!empty($data['evolutions_recentes'])) {
            $evolution = $data['evolutions_recentes'][0];
            
            $this->assertArrayHasKey('date', $evolution);
            $this->assertArrayHasKey('indicateur', $evolution);
            $this->assertArrayHasKey('reforme', $evolution);
            $this->assertArrayHasKey('valeur', $evolution);
            $this->assertArrayHasKey('unite', $evolution);
            $this->assertArrayHasKey('tendance', $evolution);
        }
    }

    /** @test */
    public function test_dashboard_principal_api_inclut_indicateurs()
    {
        $this->actingAs($this->user);

        $this->createTestData();

        $response = $this->get('/dashboard/stats');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'utilisateurs_connectes',
            'activites_validees',
            'total_activites',
            'pourcentage_activites',
            'reformes_validees',
            'total_reformes',
            'pourcentage_reformes',
            'timestamp'
        ]);
    }

    /**
     * Crée des données de test basiques
     */
    private function createTestData()
    {
        // Créer un indicateur
        $indicateur = Indicateur::create([
            'libelle' => 'Indicateur de Test',
            'description' => 'Description de test',
            'unite' => '%',
            'type' => 'Quantitatif',
            'frequence_collecte' => 'Mensuelle'
        ]);

        // Créer une réforme
        $reforme = Reforme::create([
            'titre' => 'Réforme de Test',
            'description' => 'Description de test',
            'date_debut' => Carbon::now()->subMonths(2),
            'date_fin' => Carbon::now()->addMonths(2),
            'type_reforme' => 1
        ]);

        // Créer une association
        $association = ReformeIndicateur::create([
            'reforme_id' => $reforme->id,
            'indicateur_id' => $indicateur->id,
            'valeur_cible' => 75,
            'date_debut_suivi' => $reforme->date_debut,
            'date_fin_suivi' => $reforme->date_fin
        ]);

        // Créer quelques évolutions
        for ($i = 0; $i < 5; $i++) {
            EvolutionIndicateur::create([
                'reforme_indicateur_id' => $association->id,
                'date_evolution' => Carbon::now()->subDays($i * 7),
                'valeur' => 50 + ($i * 5),
                'created_at' => Carbon::now()->subDays($i * 7)
            ]);
        }
    }
}
