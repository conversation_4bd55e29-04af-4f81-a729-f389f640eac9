<?php

namespace App\Services;

use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Models\PermissionMenu;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PermissionService
{
    /**
     * Assigne automatiquement les permissions à un rôle selon la configuration
     */
    public function assignPermissionsToRole(Role $role)
    {
        $config = config('permissions.default_role_permissions');
        
        if (!isset($config[$role->role_name])) {
            // Si le rôle n'est pas dans la configuration, lui donner des permissions de base
            $this->assignBasicPermissions($role);
            return;
        }

        $roleConfig = $config[$role->role_name];
        
        if ($roleConfig['permissions'] === 'all') {
            // Assigner toutes les permissions
            $this->assignAllPermissions($role);
        } else {
            // Assigner les permissions spécifiques
            $this->assignSpecificPermissions($role, $roleConfig['permissions']);
        }
    }

    /**
     * Assigne toutes les permissions disponibles à un rôle
     */
    public function assignAllPermissions(Role $role)
    {
        $allPermissionMenus = PermissionMenu::all();
        $role->permissionMenus()->sync($allPermissionMenus->pluck('id'));
        
        Log::info("Toutes les permissions assignées au rôle: {$role->role_name}");
    }

    /**
     * Assigne des permissions spécifiques à un rôle
     */
    public function assignSpecificPermissions(Role $role, array $permissions)
    {
        $permissionMenuIds = [];

        foreach ($permissions as $menuUrl => $permissionNames) {
            $menu = Menu::where('url', $menuUrl)->first();
            
            if (!$menu) {
                Log::warning("Menu non trouvé: {$menuUrl}");
                continue;
            }

            foreach ($permissionNames as $permissionName) {
                $permission = Permission::where('permission_name', $permissionName)->first();
                
                if (!$permission) {
                    Log::warning("Permission non trouvée: {$permissionName}");
                    continue;
                }

                $permissionMenu = PermissionMenu::where('menu_id', $menu->id)
                    ->where('permission_id', $permission->id)
                    ->first();

                if ($permissionMenu) {
                    $permissionMenuIds[] = $permissionMenu->id;
                }
            }
        }

        $role->permissionMenus()->sync($permissionMenuIds);
        
        Log::info("Permissions spécifiques assignées au rôle: {$role->role_name}", [
            'permission_count' => count($permissionMenuIds)
        ]);
    }

    /**
     * Assigne des permissions de base à un nouveau rôle
     */
    public function assignBasicPermissions(Role $role)
    {
        // Par défaut, donner accès en lecture au dashboard
        $dashboardMenu = Menu::where('url', '/dashboard')->first();
        $readPermission = Permission::where('permission_name', 'Lire')->first();

        if ($dashboardMenu && $readPermission) {
            $permissionMenu = PermissionMenu::where('menu_id', $dashboardMenu->id)
                ->where('permission_id', $readPermission->id)
                ->first();

            if ($permissionMenu) {
                $role->permissionMenus()->sync([$permissionMenu->id]);
            }
        }

        Log::info("Permissions de base assignées au nouveau rôle: {$role->role_name}");
    }

    /**
     * Récupère toutes les permissions disponibles pour un menu
     */
    public function getAvailablePermissionsForMenu(Menu $menu)
    {
        return $menu->permissionMenus()
            ->with('permission')
            ->get()
            ->pluck('permission.permission_name', 'id');
    }

    /**
     * Récupère les permissions d'un rôle pour un menu spécifique
     */
    public function getRolePermissionsForMenu(Role $role, Menu $menu)
    {
        return $role->permissionMenus()
            ->where('menu_id', $menu->id)
            ->with('permission')
            ->get()
            ->pluck('permission.permission_name');
    }

    /**
     * Vérifie si un rôle a une permission spécifique sur un menu
     */
    public function roleHasPermission(Role $role, $menuId, $permissionName)
    {
        return $role->permissionMenus()
            ->where('menu_id', $menuId)
            ->whereHas('permission', function ($query) use ($permissionName) {
                $query->where('permission_name', $permissionName);
            })
            ->exists();
    }

    /**
     * Crée automatiquement les associations permission-menu pour tous les menus
     */
    public function createPermissionMenusForAllMenus()
    {
        $menus = Menu::all();
        $permissions = Permission::all();

        foreach ($menus as $menu) {
            foreach ($permissions as $permission) {
                PermissionMenu::firstOrCreate([
                    'menu_id' => $menu->id,
                    'permission_id' => $permission->id,
                ]);
            }
        }

        Log::info('Associations permission-menu créées pour tous les menus');
    }

    /**
     * Synchronise les permissions de tous les rôles selon la configuration
     */
    public function syncAllRolePermissions()
    {
        $roles = Role::all();
        
        foreach ($roles as $role) {
            $this->assignPermissionsToRole($role);
        }

        Log::info('Permissions synchronisées pour tous les rôles');
    }

    /**
     * Récupère la configuration des permissions pour un rôle
     */
    public function getRoleConfiguration($roleName)
    {
        $config = config('permissions.default_role_permissions');
        return $config[$roleName] ?? null;
    }

    /**
     * Vérifie si un rôle est un rôle système (ne peut pas être supprimé)
     */
    public function isSystemRole($roleName)
    {
        $systemRoles = config('permissions.system_roles', []);
        return in_array($roleName, $systemRoles);
    }

    /**
     * Récupère tous les menus sensibles
     */
    public function getSensitiveMenus()
    {
        return config('permissions.sensitive_menus', []);
    }

    /**
     * Vérifie si un utilisateur a une permission spécifique pour une URL
     */
    public function userHasPermissionForUrl($user, $menuUrl, $permissionName)
    {
        // Récupérer le menu par URL
        $menu = Menu::where('url', $menuUrl)->first();

        if (!$menu) {
            Log::warning('Menu non trouvé pour URL', ['url' => $menuUrl]);
            return false;
        }

        // Vérifier si l'utilisateur a la permission pour ce menu
        return $user->hasPermission($menu->id, $permissionName);
    }

    /**
     * Vérifie si un utilisateur peut accéder à une URL avec détection automatique de l'action
     */
    public function userCanAccessUrl($user, $menuUrl, $request = null)
    {
        // Récupérer le menu par URL
        $menu = Menu::where('url', $menuUrl)->first();

        if (!$menu) {
            Log::warning('Menu non trouvé pour URL', ['url' => $menuUrl]);
            return false;
        }

        // Si pas de requête fournie, vérifier juste l'accès au menu
        if (!$request) {
            return $user->canAccessMenu($menu->id);
        }

        // Déterminer l'action CRUD basée sur la requête
        $action = $this->determineCrudActionFromRequest($request);

        if (!$action) {
            // Si on ne peut pas déterminer l'action, vérifier juste l'accès au menu
            return $user->canAccessMenu($menu->id);
        }

        // Vérifier la permission spécifique
        return $user->hasPermission($menu->id, $action);
    }

    /**
     * Détermine l'action CRUD à partir d'une requête
     */
    private function determineCrudActionFromRequest($request)
    {
        if (!$request->route()) {
            return null;
        }

        $actionName = $request->route()->getActionName();
        $method = $request->method();

        // Extraire l'action du nom de la méthode du contrôleur
        if (strpos($actionName, '@') !== false) {
            $controllerAction = explode('@', $actionName)[1];
        } else {
            $controllerAction = '';
        }

        // Mapping standard des actions
        $actionMappings = [
            'index' => 'Lire',
            'show' => 'Lire',
            'create' => 'Créer',
            'store' => 'Créer',
            'edit' => 'Modifier',
            'update' => 'Modifier',
            'destroy' => 'Supprimer',
            'delete' => 'Supprimer',
        ];

        if (isset($actionMappings[$controllerAction])) {
            return $actionMappings[$controllerAction];
        }

        // Fallback sur la méthode HTTP
        switch ($method) {
            case 'GET':
                return 'Lire';
            case 'POST':
                return 'Créer';
            case 'PUT':
            case 'PATCH':
                return 'Modifier';
            case 'DELETE':
                return 'Supprimer';
            default:
                return 'Lire'; // Par défaut
        }
    }

    /**
     * Vérifie si un utilisateur peut effectuer une action CRUD spécifique
     */
    public function userCanPerformAction($user, $menuUrl, $action)
    {
        return $this->userHasPermissionForUrl($user, $menuUrl, $action);
    }

    /**
     * Récupère toutes les permissions d'un utilisateur pour un menu donné
     */
    public function getUserPermissionsForMenu($user, $menuUrl)
    {
        $menu = Menu::where('url', $menuUrl)->first();

        if (!$menu) {
            return [];
        }

        $permissions = [];
        $availablePermissions = ['Créer', 'Lire', 'Modifier', 'Supprimer'];

        foreach ($availablePermissions as $permission) {
            if ($user->hasPermission($menu->id, $permission)) {
                $permissions[] = $permission;
            }
        }

        return $permissions;
    }

    /**
     * Génère un rapport des permissions pour un utilisateur
     */
    public function generateUserPermissionReport($user)
    {
        $report = [
            'user_id' => $user->id,
            'user_name' => $user->personne ? $user->personne->nom . ' ' . $user->personne->prenom : 'Utilisateur #' . $user->id,
            'roles' => $user->roles->pluck('role_name')->toArray(),
            'menus' => []
        ];

        $menus = Menu::where('is_active', true)->orderBy('ordre')->get();

        foreach ($menus as $menu) {
            $menuPermissions = $this->getUserPermissionsForMenu($user, $menu->url);

            if (!empty($menuPermissions)) {
                $report['menus'][] = [
                    'menu_id' => $menu->id,
                    'menu_name' => $menu->libelle,
                    'menu_url' => $menu->url,
                    'permissions' => $menuPermissions
                ];
            }
        }

        return $report;
    }
}
