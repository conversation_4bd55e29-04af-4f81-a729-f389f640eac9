@extends('layout.app')

@section('content')


<div class="container pt-4">
    @if(session('success'))
    <div class="alert alert-success">{{ session('success') }}</div>
    @endif

    @if($errors->any())
    <div class="alert alert-danger">
        <ul class="mb-0">
            @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif

<!-- Mobile Menu end -->
@include('components.page-header', [
    'title' => 'Gestion des activités',
    'breadcrumb' => [
        ['label' => 'Accueil', 'url' => route('dashboard')],
        ['label' => 'Gestion des activités']
    ]
])

    <!-- Bouton Ajouter -->
    

    <!-- Tableau des activités réformes -->
    <div class="data-table-area mg-b-15">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="sparkline13-list">
                        <div class="sparkline13-hd">
                            <div class="main-sparkline13-hd">
                                <h1>Liste <span class="table-project-n">des </span>Activités</h1>
                                <button type="button" class="btn btn-primary mb-3" data-toggle="modal" data-target="#addModal">
                                    + Ajouter une activité
                                </button>
                            </div>
                        </div>
                        <div class="sparkline13-graph">
                            <div class="datatable-dashv1-list custom-datatable-overright">
                                <div id="toolbar">
                                    <select class="form-control dt-tb">
                                        <option value="">Export Basic</option>
                                        <option value="all">Export All</option>
                                        <option value="selected">Export Selected</option>
                                    </select>
                                </div>
                                <table id="table" data-toggle="table" data-pagination="true" data-search="true" data-show-columns="true" data-show-pagination-switch="true" data-show-refresh="true" data-key-events="true" data-show-toggle="true" data-resizable="true" data-cookie="true"
                                    data-cookie-id-table="saveId" data-show-export="true" data-click-to-select="true" data-toolbar="#toolbar">
                                    <thead>
                                        <tr>
                                            <th data-field="state" data-checkbox="true"></th>
                                            <th data-field="id">ID</th>
                                            <th data-field="reforme">Réforme</th>
                                            <th data-field="libelle" data-editable="true">Libellé</th>
                                            <th data-field="date_debut" data-editable="true">Date Début</th>
                                            <th data-field="date_fin_prevue" data-editable="true">Date Fin Prévue</th>
                                            <th data-field="date_fin" data-editable="true">Date Fin</th>
                                            <th data-field="poids" data-editable="true">Poids (%)</th>
                                            <th data-field="statut" data-editable="true">Statut</th>
                                            <th data-field="structure_responsable" data-editable="true">Structure Responsable</th>
                                            <th data-field="action">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($activitesreformes as $activite)
                                        <tr>
                                            <td></td>
                                            <td>{{ $activite->id }}</td>
                                            <td>{{ $activite->reforme->titre  }}</td>
                                            <td>{{ $activite->libelle }}</td>
                                            <td>{{ $activite->date_debut }}</td>
                                            <td>{{ $activite->date_fin_prevue }}</td>
                                            <td>{{ $activite->date_fin }}</td>
                                            <td>{{ $activite->poids }}</td>
                                            <td>
                                                <span class="badge badge-{{ $activite->statut == 'A' ? 'success' : ($activite->statut == 'P' ? 'warning' : 'secondary') }}">
                                                    {{ $activite->statut_label ?? $activite->statut }}
                                                </span>
                                            <td>
                                                @php
                                                    $structureInfo = $structures->firstWhere('id', $activite->structure_responsable);
                                                @endphp
                                                {{ $structureInfo ? $structureInfo->lib_court : 'Structure #' . $activite->structure_responsable }}
                                            </td>
                                                <td>
                                                <!-- Voir -->
                                                <button class="btn btn-info btn-sm" data-toggle="modal" data-target="#viewModal{{ $activite->id }}">
                                                    <i class="fa fa-eye"></i>
                                                </button>

                                                <!-- Modifier -->
                                                <button class="btn btn-warning btn-sm" data-toggle="modal" data-target="#editModal{{ $activite->id }}">
                                                    <i class="fa fa-edit"></i>
                                                </button>

                                                <!-- Supprimer -->
                                                <form action="{{ route('activites.destroy', $activite->id) }}" method="POST" style="display:inline-block;">
                                                    @csrf @method('DELETE')
                                                    <button class="btn btn-danger btn-sm" onclick="return confirm('Supprimer cette activité ?')">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>

                                        <!-- Modal de visualisation -->
                                        <div class="modal fade" id="viewModal{{ $activite->id }}" tabindex="-1" role="dialog" aria-labelledby="viewModal{{ $activite->id }}" aria-hidden="true">
                                            <div class="modal-dialog modal-dialog modal-lg" role="document">
                                                <div class="modal-content">
                                                <div class="modal-header">
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                        <ul id="myTabedu1" class="tab-review-design">
                                                            <li class="active"><a href="#description">Détail de l'activité réforme</a></li>
                                                        </ul>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p><strong>ID:</strong> {{ $activite->id }}</p>
                                                        <p><strong>Réforme:</strong> {{ $activite->reforme->titre  }}</p>
                                                        <p><strong>Libellé:</strong> {{ $activite->libelle }}</p>
                                                        <p><strong>Date Début:</strong> {{ $activite->date_debut }}</p>
                                                        <p><strong>Date Fin Prévue:</strong> {{ $activite->date_fin_prevue }}</p>
                                                        <p><strong>Date Fin:</strong> {{ $activite->date_fin ?? 'Non définie' }}</p>
                                                        <p><strong>Poids:</strong> {{ $activite->poids }}%</p>
                                                        <p><strong>Statut:</strong>
                                                            <span class="badge badge-{{ $activite->statut == 'A' ? 'success' : ($activite->statut == 'P' ? 'warning' : 'secondary') }}">
                                                                {{ $activite->statut_label ?? $activite->statut }}
                                                            </span>
                                                        </p>
                                                        <p><strong>Structure Responsable:</strong> 
                                                            @php
                                                                $structureInfo = $structures->firstWhere('id', $activite->structure_responsable);
                                                            @endphp
                                                            {{ $structureInfo ? $structureInfo->lib_court . ' - ' . $structureInfo->lib_long : 'Structure #' . $activite->structure_responsable }}
                                                        </p>
                                                        @if($activite->parentActivite)
                                                            <p><strong>Activité Parente:</strong> {{ $activite->parentActivite->libelle }}</p>
                                                        @endif
                                                        @if($activite->sousActivites->count() > 0)
                                                            <p><strong>Sous-activités:</strong> {{ $activite->sousActivites->count() }}</p>
                                                        @endif
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button class="btn btn-default" data-dismiss="modal">Fermer</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Modal de modification -->
                                        <div class="modal fade" id="editModal{{ $activite->id }}" tabindex="-1" role="dialog">
                                            <div class="modal-dialog modal-dialog modal-lg" role="document">
                                                <form action="{{ route('activites.update', $activite->id) }}" method="POST">
                                                    @csrf @method('PUT')
                                                    <div class="modal-content">
                                                    <div class="modal-header">
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                        <ul id="myTabedu1" class="tab-review-design">
                                                            <li class="active"><a href="#description">Modifier Activite Reforme</a></li>
                                                        </ul>
                                                    </div>
                                                        <div class="modal-body">
                                                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                                            <div class="mb-3">
                                                                <label>Réforme</label>
                                                                <select name="reforme_id" class="form-control" required>
                                                                    @foreach($reformes ?? [] as $reforme)
                                                                        <option value="{{ $reforme->id }}" {{ $activite->reforme_id == $reforme->id ? 'selected' : '' }}>
                                                                            {{ $reforme->titre }}
                                                                        </option>
                                                                    @endforeach
                                                                </select>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Libellé</label>
                                                                <input type="text" class="form-control" name="libelle" value="{{ $activite->libelle }}" required>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Date Début</label>
                                                                <input type="date" class="form-control" name="date_debut" value="{{ $activite->date_debut }}" required>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Date Fin Prévue</label>
                                                                <input type="date" class="form-control" name="date_fin_prevue" value="{{ $activite->date_fin_prevue }}" required>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                                            <div class="mb-3">
                                                                <label>Date Fin</label>
                                                                <input type="date" class="form-control" name="date_fin" value="{{ $activite->date_fin }}">
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Poids (%)</label>
                                                                <input type="number" class="form-control" name="poids" value="{{ $activite->poids }}" min="1" max="100" required>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Statut</label>
                                                                <select name="statut" class="form-control" required>
                                                                    <option value="C" {{ $activite->statut == 'C' ? 'selected' : '' }}>Créé</option>
                                                                    <option value="P" {{ $activite->statut == 'P' ? 'selected' : '' }}>En cours</option>
                                                                    <option value="A" {{ $activite->statut == 'A' ? 'selected' : '' }}>Achevé</option>
                                                                </select>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label>Structure Responsable</label>
                                                                <select name="structure_responsable" class="form-control" required>
                                                                    @foreach($structures ?? [] as $structure)
                                                                        <option value="{{ $structure->id }}" {{ $activite->structure_responsable == $structure->id ? 'selected' : '' }}>
                                                                            {{ $structure->lib_court }} - {{ $structure->lib_long }}
                                                                        </option>
                                                                    @endforeach
                                                                </select>
                                                            </div>
                                                        </div>
                                                        </div>
    
                                                        <div class="modal-footer">
                                                            <button class="btn btn-primary" type="submit">Mettre à jour</button>
                                                            <button class="btn btn-default" type="button" data-dismiss="modal">Annuler</button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                        @endforeach
                                    </tbody>
                                </table>

                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</div>




<!-- Modal d’ajout -->
<div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <form action="{{ route('activites.store') }}" method="POST" id="add-activite" class="add-activite">
            @csrf
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <ul id="myTabedu1" class="tab-review-design">
                        <li class="active"><a href="#description">Ajouter activité</a></li>
                    </ul>
                </div>
                <div class="modal-body">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="mb-3">
                        <label>Réforme</label>
                        <select name="reforme_id" class="form-control" required>
                            <option value="">Sélectionner une réforme</option>
                            @foreach($reformes ?? [] as $reforme)
                                <option value="{{ $reforme->id }}">{{ $reforme->titre }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label>Libellé</label>
                        <input type="text" name="libelle" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label>Date Début</label>
                        <input type="date" name="date_debut" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label>Date Fin Prévue</label>
                        <input type="date" name="date_fin_prevue" class="form-control" required>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="mb-3">
                        <label>Date Fin</label>
                        <input type="date" name="date_fin" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label>Poids (%)</label>
                        <input type="number" name="poids" class="form-control" min="1" max="100" required>
                    </div>
                    <div class="mb-3">
                        <label>Statut</label>
                        <select name="statut" class="form-control" required>
                            <option value="">Sélectionner un statut</option>
                            <option value="C">Créé</option>
                            <option value="P">En cours</option>
                            <option value="A">Achevé</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label>Structure Responsable</label>
                        <select name="structure_responsable" class="form-control" required>
                            <option value="">Sélectionner une structure</option>
                            @foreach($structures ?? [] as $structure)
                                <option value="{{ $structure->id }}">{{ $structure->lib_court }} - {{ $structure->lib_long }}</option>
                            @endforeach
                        </select>
                    </div>
                    </div>
                <div class="modal-footer ">
                    <button class="btn btn-primary" type="submit">Enregistrer</button>
                    <button class="btn btn-default" type="button" data-dismiss="modal">Annuler</button>
                </div>
            </div>
            </div>
        </form>
    </div>
</div>



@endsection

