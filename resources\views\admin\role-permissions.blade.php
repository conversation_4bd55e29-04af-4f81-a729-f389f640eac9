@extends('layouts.app')

@section('title', 'Gestion des Permissions')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-shield"></i> Gestion des Permissions par Rôle
                    </h3>
                    <div class="pull-right">
                        <button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#createRoleModal">
                            <i class="fa fa-plus"></i> Nouveau Rôle
                        </button>
                        <button type="button" class="btn btn-info btn-xs" onclick="syncPermissions()">
                            <i class="fa fa-refresh"></i> Synchroniser
                        </button>
                    </div>
                </div>
                <div class="panel-body">
                    <!-- Onglets pour chaque rôle -->
                    <ul class="nav nav-tabs" role="tablist">
                        @foreach($roles as $index => $role)
                        <li role="presentation" class="{{ $index === 0 ? 'active' : '' }}">
                            <a href="#role-{{ $role->id }}" aria-controls="role-{{ $role->id }}" role="tab" data-toggle="tab">
                                {{ $role->role_name }}
                                @if(in_array($role->role_name, config('permissions.system_roles', [])))
                                    <span class="label label-warning">Système</span>
                                @endif
                            </a>
                        </li>
                        @endforeach
                    </ul>

                    <!-- Contenu des onglets -->
                    <div class="tab-content" style="margin-top: 20px;">
                        @foreach($roles as $index => $role)
                        <div role="tabpanel" class="tab-pane {{ $index === 0 ? 'active' : '' }}" id="role-{{ $role->id }}">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="panel panel-info">
                                        <div class="panel-heading">
                                            <h4>Permissions pour: {{ $role->role_name }}</h4>
                                            <div class="pull-right">
                                                @if(!in_array($role->role_name, config('permissions.system_roles', [])))
                                                <button type="button" class="btn btn-success btn-xs" onclick="saveRolePermissions({{ $role->id }})">
                                                    <i class="fa fa-save"></i> Sauvegarder
                                                </button>
                                                <button type="button" class="btn btn-danger btn-xs" onclick="deleteRole({{ $role->id }}, '{{ $role->role_name }}')">
                                                    <i class="fa fa-trash"></i> Supprimer
                                                </button>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="panel-body">
                                            <form id="permissions-form-{{ $role->id }}">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th>Menu</th>
                                                                @foreach($permissions as $permission)
                                                                <th class="text-center">{{ $permission->permission_name }}</th>
                                                                @endforeach
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach($menus as $menu)
                                                            <tr>
                                                                <td>
                                                                    <strong>{{ $menu->libelle }}</strong>
                                                                    <br><small class="text-muted">{{ $menu->url }}</small>
                                                                </td>
                                                                @foreach($permissions as $permission)
                                                                <td class="text-center">
                                                                    @php
                                                                        $hasPermission = $role->permissionMenus()
                                                                            ->where('menu_id', $menu->id)
                                                                            ->whereHas('permission', function($q) use ($permission) {
                                                                                $q->where('permission_name', $permission->permission_name);
                                                                            })->exists();
                                                                        
                                                                        $permissionMenuId = \App\Models\PermissionMenu::where('menu_id', $menu->id)
                                                                            ->where('permission_id', $permission->id)
                                                                            ->first()?->id;
                                                                    @endphp
                                                                    
                                                                    @if($permissionMenuId)
                                                                    <input type="checkbox" 
                                                                           name="permissions[{{ $role->id }}][]" 
                                                                           value="{{ $permissionMenuId }}"
                                                                           {{ $hasPermission ? 'checked' : '' }}
                                                                           @if(in_array($role->role_name, config('permissions.system_roles', []))) disabled @endif>
                                                                    @else
                                                                    <span class="text-muted">N/A</span>
                                                                    @endif
                                                                </td>
                                                                @endforeach
                                                            </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour créer un nouveau rôle -->
<div class="modal fade" id="createRoleModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Créer un Nouveau Rôle</h4>
            </div>
            <form id="createRoleForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="role_name">Nom du Rôle:</label>
                        <input type="text" class="form-control" id="role_name" name="role_name" required>
                        <small class="help-block">Le nom du rôle doit être unique.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Créer le Rôle</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Gérer la création d'un nouveau rôle
    $('#createRoleForm').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: '{{ route("admin.roles.create") }}',
            method: 'POST',
            data: $(this).serialize(),
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    location.reload();
                } else {
                    alert('Erreur: ' + response.message);
                }
            },
            error: function(xhr) {
                let message = 'Erreur lors de la création du rôle';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                alert(message);
            }
        });
    });
});

// Sauvegarder les permissions d'un rôle
function saveRolePermissions(roleId) {
    const form = document.getElementById('permissions-form-' + roleId);
    const formData = new FormData(form);
    const permissions = [];
    
    // Récupérer toutes les permissions cochées pour ce rôle
    const checkboxes = form.querySelectorAll('input[name="permissions[' + roleId + '][]"]:checked');
    checkboxes.forEach(checkbox => {
        permissions.push(checkbox.value);
    });
    
    $.ajax({
        url: '/admin/roles/' + roleId + '/permissions',
        method: 'PUT',
        data: {
            permissions: permissions,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                alert(response.message);
            } else {
                alert('Erreur: ' + response.message);
            }
        },
        error: function(xhr) {
            alert('Erreur lors de la sauvegarde');
        }
    });
}

// Supprimer un rôle
function deleteRole(roleId, roleName) {
    if (confirm('Êtes-vous sûr de vouloir supprimer le rôle "' + roleName + '" ?')) {
        $.ajax({
            url: '/admin/roles/' + roleId,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    location.reload();
                } else {
                    alert('Erreur: ' + response.message);
                }
            },
            error: function(xhr) {
                alert('Erreur lors de la suppression');
            }
        });
    }
}

// Synchroniser les permissions
function syncPermissions() {
    if (confirm('Synchroniser toutes les permissions selon la configuration ?')) {
        $.ajax({
            url: '{{ route("admin.permissions.sync") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    location.reload();
                } else {
                    alert('Erreur: ' + response.message);
                }
            },
            error: function(xhr) {
                alert('Erreur lors de la synchronisation');
            }
        });
    }
}
</script>
@endsection
