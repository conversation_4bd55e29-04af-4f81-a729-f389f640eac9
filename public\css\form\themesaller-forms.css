
/*
--------------------------------------------------
@Start smart forms
--------------------------------------------------
*/

/* Roboto google font import
--------------------------------------- */
@import url("https://fonts.googleapis.com/css?family=Roboto:100,300,400,700,900");

html, body{
	border: 0;
	margin: 0;
	padding: 0;
	font-size: 100%;
	vertical-align: baseline;
	font: inherit;
}

body{
	margin: 0;
	padding: 0;
}

/* @backgrounds :: modify or add yours below
------------------------------------------------------------------- */
.darkbg{ background:#6C82A2 url(../images/dark.png) repeat fixed; }
.woodbg{ background:#E6CCA6 url(../images/wood.png) repeat fixed; }

/* @form wrappers
---------------------------------- */
.themesaller-wrap{ padding:0 20px; }
.themesaller-forms,
.themesaller-forms *{
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.themesaller-forms {
	font-family:  "Roboto", Arial, Helvetica, sans-serif;
	line-height: 1.231;
	font-weight: 400;
	font-size: 14px;
	color: #626262;
}

.themesaller-container{
	background:#fff;
	margin:50px auto;
	-webkit-box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
	-moz-box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
	-o-box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
	box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
	-webkit-border-radius:5px 5px 0 0;
	-moz-border-radius:5px 5px 0 0;
	-o-border-radius:5px 5px 0 0;
	border-radius:5px 5px 0 0;

}

/* @form container width
/* @if you want to change the form container width change the values below
/* @alternatively you can add yours eg .wrap4{ max-width:200px; }
---------------------------------------------------------------------------- */
.wrap-0{ max-width:952px; }
.wrap-1{ max-width:852px; }
.wrap-2{ max-width:652px; }
.wrap-3{ max-width:452px; }

/* @form helper classes
--------------------------------------------------------------- */
.themesaller-forms .section{ margin-bottom:22px; }
.themesaller-forms .smart-link{ color:#f00; text-decoration:none; }
.themesaller-forms .smart-link:hover{ text-decoration: underline; }
.themesaller-forms .tagline{ height:0; border-top:1px solid #CFCFCF; text-align:center;  }
.themesaller-forms .tagline span{
	text-transform:uppercase;
	display:inline-block;
	position:relative;
	padding:0 15px;
	background:#fff;
	color:#f00;
	top:-14px;
}

/* @form label + field :: field class is useful for validation
---------------------------------------------------------------------- */
.themesaller-forms .field{ display:block; position:relative; }
.themesaller-forms .field-icon i { color:#BBB; position:relative; }
.themesaller-forms .field-label { display: block; margin-bottom: 7px; }
.themesaller-forms .field-label.colm{ padding-top:12px; }
.themesaller-forms .field-label em{
	color:#e74c3c;
	font-size:14px;
	font-style:normal;
	display:inline-block;
	margin-left:4px;
	position:relative;
	top:3px;
}

/* @form header section
----------------------------------------- */
.themesaller-forms .form-header{
	overflow:hidden;
	position:relative;
	padding:25px 30px;
	-webkit-border-radius:5px 5px 0 0 ;
	-moz-border-radius:5px 5px 0 0 ;
	-o-border-radius:5px 5px 0 0 ;
	border-radius:5px 5px 0 0 ;
}

.themesaller-forms .form-header h4 {
	font-family:"Roboto", Arial, Helvetica, sans-serif;
color: #fff;
font-family: "Roboto",Arial,Helvetica,sans-serif;
font-size: 30px;
margin: 0 auto;
text-align: center;
text-shadow: 0 1px 0 #ccc, 0 2px 0 #c9c9c9, 0 3px 0 #bbb, 0 4px 0 #b9b9b9, 0 5px 0 #aaa, 0 6px 1px rgba(0, 0, 0, 0.1), 0 0 5px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.3), 0 3px 5px rgba(0, 0, 0, 0.15), 0 5px 10px rgba(0, 0, 0, 0.2), 0 10px 10px rgba(0, 0, 0, 0.2), 0 20px 20px rgba(0, 0, 0, 0.1);
text-transform: uppercase;
}

.themesaller-forms .form-header h4 i {
	font-size:38px;
	position:relative;
	margin-right:10px;
	top:2px;
}

/* @header themes :: primary + lite
---------------------------------------------- */
.themesaller-forms .header-primary {
background-color: #ff0000;
border-bottom: 5px solid #e00000;
border-radius: 3px 3px 0 0;
display: block;
position: relative;
text-align: center;
}

.themesaller-forms .header-lite{
	background:#F3F5FA;
	border-top:1px solid #A7D065;
	border-bottom:1px solid #D9DDE5;
}

.themesaller-forms .header-lite:before{
	content:"";
	background-color:#f00;
	position:absolute;
	height:8px;
	z-index:1;
	top:0px;
	right:0;
	left:0;
}

.themesaller-forms .header-primary h4{ color:#fff; }
.themesaller-forms .header-lite h4{ color:#5D6A87; padding-top:5px; }

/* @remove rounded corners form headers
----------------------------------------------------------------- */
.smart-flat,
.smart-flat .form-header{
	-webkit-border-radius:0;
	-moz-border-radius:0;
	-o-border-radius:0;
	border-radius:0;
}

/* @form body + footer
------------------------------------------------------------------- */
.themesaller-forms .form-body{ padding:40px 30px; padding-bottom:20px; }
.themesaller-forms .form-footer {
	overflow:hidden;
	padding:20px 25px;
	padding-top:25px;
	background: #F5F5F5;
	background: #F5F5F5 url(../images/foobg.png) top left repeat-x;
}

/* @crossbrowser placeholder styling :: modern browsers only IE10+
------------------------------------------------------------------------ */
.themesaller-forms input[type=search] { -webkit-appearance: textfield; }
.themesaller-forms ::-webkit-search-decoration,
.themesaller-forms ::-webkit-search-cancel-button { -webkit-appearance: none; }
.themesaller-forms input:invalid { -moz-box-shadow: none; box-shadow: none;  }
.themesaller-forms input::-webkit-input-placeholder,
.themesaller-forms textarea::-webkit-input-placeholder { color: #AAAAAA; }
.themesaller-forms input:focus::-webkit-input-placeholder,
.themesaller-forms textarea:focus::-webkit-input-placeholder { color: #D6DBE0; }
.themesaller-forms input:-moz-placeholder,
.themesaller-forms textarea:-moz-placeholder { color: #AAAAAA; }
.themesaller-forms input:focus:-moz-placeholder,
.themesaller-forms textarea:focus:-moz-placeholder { color: #D6DBE0; }
.themesaller-forms input::-moz-placeholder,
.themesaller-forms textarea::-moz-placeholder { color: #AAAAAA; opacity: 1; }
.themesaller-forms input:focus::-moz-placeholder,
.themesaller-forms textarea:focus::-moz-placeholder { color: #D6DBE0; opacity: 1; }
.themesaller-forms input:-ms-input-placeholder,
.themesaller-forms textarea:-ms-input-placeholder { color: #AAAAAA; }
.themesaller-forms input:focus:-ms-input-placeholder,
.themesaller-forms textarea:focus:-ms-input-placeholder { color: #D6DBE0; }

/* @element general styling :: fonts :: adjust accordingly
------------------------------------------------------------- */
.themesaller-forms label,
.themesaller-forms input,
.themesaller-forms button,
.themesaller-forms select,
.themesaller-forms textarea {
	margin: 0;
	font-size: 14px;
	font-family:  "Roboto", Arial, Helvetica, sans-serif;
	font-weight:400;
	color: #626262;
	outline:none;
}

/* @remove browser specific styling
----------------------------------------------- */
.themesaller-forms .gui-input,
.themesaller-forms .gui-textarea,
.themesaller-forms .select > select,
.themesaller-forms input[type="button"],
.themesaller-forms input[type="submit"],
.themesaller-forms input[type="search"],
.themesaller-forms .select-multiple select {
	-webkit-tap-highlight-color:transparent;
	-webkit-tap-highlight-color: rgba(0,0,0,0);
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	-webkit-border-radius:0px;
	border-radius: 0px;
}

.themesaller-forms input[type="search"]::-webkit-search-decoration,
.themesaller-forms input[type="search"]::-webkit-search-cancel-button,
.themesaller-forms input[type="search"]::-webkit-search-results-button,
.themesaller-forms input[type="search"]::-webkit-search-results-decoration {
	display: none;
}

/* @labels font-size styling :: adjust to fit your needs
--------------------------------------------------------- */
.themesaller-forms .switch,
.themesaller-forms .option,
.themesaller-forms .field-label{ font-size:14px; }

/* @prevent user selection for usability purposes
----------------------------------------------------- */
.themesaller-forms .radio,
.themesaller-forms .button,
.themesaller-forms .checkbox,
.themesaller-forms .select .arrow,
.themesaller-forms .switch > label,
.themesaller-forms .ui-slider .ui-slider-handle{
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

/* @universal rules for all elements
---------------------------------------------------- */
.themesaller-forms .radio,
.themesaller-forms .button,
.themesaller-forms .tooltip,
.themesaller-forms .checkbox,
.themesaller-forms .gui-input,
.themesaller-forms .notification,
.themesaller-forms .gui-textarea,
.themesaller-forms .select > select,
.themesaller-forms .select-multiple select{
	-webkit-transition:all 0.5s ease-in-out;
	-moz-transition:all 0.5s ease-in-out;
	-ms-transition:all 0.5s ease-in-out;
	-o-transition:all 0.5s ease-in-out;
	transition:all 0.5s ease-in-out;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	-ms-border-radius: 0;
	-o-border-radius: 0;
	border-radius: 0;
	outline:none;
}

/* @control border-size :: color etc for these elements
----------------------------------------------------------- */
.themesaller-forms .select,
.themesaller-forms .gui-input,
.themesaller-forms .gui-textarea,
.themesaller-forms .select > select,
.themesaller-forms .select-multiple select{
	background: #fff;
	position: relative;
	vertical-align: top;
	border: 1px solid #CFCFCF;
	display: -moz-inline-stack;
    display: inline-block;
    *display: inline;
	color: #626262;
	outline:none;
	height: 42px;
	width: 100%;
	*zoom: 1;
}

/* @styling inputs and textareas
------------------------------------------- */
.themesaller-forms .gui-input,
.themesaller-forms .gui-textarea { padding:10px; }
.themesaller-forms .gui-textarea {
    resize: none;
	line-height: 19px;
	overflow: auto;
	max-width:100%;
    height: 96px;
}

/* @hint below textareas
---------------------------------------- */
.themesaller-forms .input-hint {
    padding: 10px;
	display: block;
	margin-top: -1px;
	line-height: 16px;
	position: relative;
    background: #F5F5F5;
    border: 1px solid #CFCFCF;
	font-family:Arial, Helvetica, sans-serif;
    -webkit-border-radius: 0;
	-moz-border-radius: 0;
	-o-border-radius: 0;
	border-radius: 0;
    font-size: 11px;
    color: #999;
}

/* @form selects :: dropdowns
-------------------------------------------------- */
.themesaller-forms .select { border:0; z-index:10; }
.themesaller-forms .select > select {
	display: block;
    padding:9px 10px;
    color: #626262;
    background: #F5F5F5;
     border: 1px solid #CFCFCF;
    -webkit-appearance:none;
    -moz-appearance:none;
    appearance:normal;
	outline:none;
	text-indent: 0.01px;
    text-overflow: '';
	z-index:10;
	margin: 0;
}

.themesaller-forms .select > select::-ms-expand { display: none; }
.themesaller-forms .select .arrow {
	position: absolute;
	top: 9px;
	right: 4px;
	width: 24px;
	height: 24px;
	color:#9F9F9F;
	pointer-events:none;
	z-index:16;
}

.themesaller-forms .select .arrow:after,
.themesaller-forms .select .arrow:before {
	content: '';
	position: absolute;
	font:12px "Consolas", monospace;
	font-style:normal;
	pointer-events:none;
	display:none\9;
	left:5px;

}

.themesaller-forms .select .arrow:before { content:'\25BC'; bottom:4px; }
.themesaller-forms .select .double:after { content:'\25B2'; top:-1px;  }
.themesaller-forms .select .double:before { content:'\25BC'; bottom:-1px; }
.themesaller-forms .select-multiple select {
	width:100%;
	height: 123px;
	padding: 10px;
}

/* @file inputs :: file uploaders
-------------------------------------------------------- */
.themesaller-forms .file{ display:block; width:100%; }
.themesaller-forms .file .gui-file{
	width:100%;
	height:100%;
	cursor:pointer;
	padding:8px 10px;
	position:absolute;
	-moz-opacity:0;
	opacity: 0;
	z-index:11;
	bottom:0;
	right:0;
}

.themesaller-forms .file .button {
	position: absolute;
	top: 4px;
	right: 4px;
	float: none;
	height: 34px;
	line-height: 34px;
	padding: 0 16px;
	z-index:10;
}

/* @form element :hover state
-------------------------------------------- */
.themesaller-forms .gui-input:hover,
.themesaller-forms .gui-textarea:hover,
.themesaller-forms .select > select:hover,
.themesaller-forms .select-multiple select:hover,
.themesaller-forms .gui-input:hover ~ .input-hint,
.themesaller-forms .file .gui-file:hover + .gui-input,
.themesaller-forms .gui-textarea:hover ~ .input-hint{
	border-color: #f00;
}

/* @form element :focus state
-------------------------------------------------------- */
.themesaller-forms .gui-input:focus,
.themesaller-forms .gui-textarea:focus,
.themesaller-forms .select > select:focus,
.themesaller-forms .select-multiple select:focus{
    color: #3c3c3c;
    background: #fff;
	border:1px solid #f00;
	-webkit-box-shadow:0px 0px 3px #f00 inset;
	-moz-box-shadow:0px 0px 3px #f00 inset;
	-o-box-shadow:0px 0px 3px #f00 inset;
	box-shadow:0px 0px 3px #f00 inset;
    outline: none;

}

.themesaller-forms .select > select:focus {
	z-index:10;
	z-index:20\9;
}

.themesaller-forms .gui-textarea:focus{  height: 120px; }
.themesaller-forms .select > select:focus { z-index:10; z-index:20\9; }
.themesaller-forms .gui-input:focus ~ .field-icon i,
.themesaller-forms .gui-textarea:focus ~ .field-icon i{ color:#f00; }
.themesaller-forms .select-multiple select:focus,
.themesaller-forms .gui-input:focus ~ .input-hint,
.themesaller-forms .gui-textarea:focus ~ .input-hint,
.themesaller-forms .file .gui-file:focus + .gui-input{ border-color: #f00; }
.themesaller-forms .select > select:focus + .arrow{ color:#f00; }


/* @radio + checkbox option elements
----------------------------------------------------- */
.themesaller-forms .option {
    position: relative;
	padding-right:15px;
	display: inline-block;
    vertical-align: middle;
}
.themesaller-forms .option > input {
    position: absolute;
    height: inherit;
    width: inherit;
    opacity: 0;
	left: 0;
}

.themesaller-forms .checkbox,
.themesaller-forms .radio {
	position:relative;
	margin-right:2px;
	background: #fff;
	display: inline-block;
    border: 3px solid #CFCFCF;
	height: 21px;
	width: 21px;
	top:4px;
}

.themesaller-forms .checkbox:before,
.themesaller-forms .radio:before {
    content: '';
    display: none;
}

.themesaller-forms input:checked + .checkbox:before,
.themesaller-forms input:checked + .radio:before {
    display: block;
}

.themesaller-forms .checkbox:before {
    position: absolute;
    top: 4px;
    left: 3px;
    width: 6px;
    height: 3px;
    border: solid #f00;
    border-width: 0 0 3px 3px;
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.themesaller-forms input:checked + .checkbox,
.themesaller-forms input:checked + .radio{ border: 3px solid #f00; }
.themesaller-forms .radio {
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	-o-border-radius: 20px;
	border-radius: 20px;

}
.themesaller-forms .radio:before {
    margin: 4px;
    width: 7px;
    height: 7px;
    background: #f00;
  	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	-o-border-radius: 10px;
	border-radius: 10px;
}


/* @radio + checkbox :hover state
-------------------------------------------------- */
.themesaller-forms input:hover + .checkbox,
.themesaller-forms input:hover + .radio{
    border-color:#f00;

}

/* @radio + checkbox :focus state
--------------------------------------------------- */
.themesaller-forms input:focus + .checkbox,
.themesaller-forms input:focus + .radio{ border-color: #f00; }
.themesaller-forms input:focus + .radio:before{ background: #f00; }
.themesaller-forms input:focus + .checkbox:before{ border-color: #f00; }

/* @toggle switch elements
-------------------------------------------------- */
.themesaller-forms .switch {
	cursor:pointer;
	position: relative;
	padding-right:10px;
    display: inline-block;
	margin-bottom:5px;
	height: 26px;
}
.themesaller-forms .switch > label {
	cursor:pointer;
    display: inline-block;
    position: relative;
    height: 25px;
	width: 58px;
	color: #fff;
    font-size: 10px;
    font-weight: bold;
	line-height: 20px;
    text-align: center;
    background: #D7D7D7;
    border: 2px solid #D7D7D7;
	text-transform: uppercase;
	font-family:Helvetica, Arial, sans-serif;
    -webkit-transition: 0.3s ease-out;
    -moz-transition: 0.3s ease-out;
    -o-transition: 0.3s ease-out;
    transition: 0.3s ease-out;

}
.themesaller-forms .switch > label + span{ display:inline-block; padding-left:5px; position:relative; top:-7px; }
.themesaller-forms .switch > label:before {
    content: attr(data-off);
    position: absolute;
    top: 1px;
    right: 3px;
    width: 33px;
}

.themesaller-forms .switch > label:after {
	content:"";
    margin: 1px;
    width: 19px;
    height: 19px;
	display: block;
    background: #fff;
}

.themesaller-forms .switch > input {
	-webkit-appearance: none;
	position: absolute;
    width: inherit;
    height: inherit;
    opacity: 0;
	left: 0;
	top: 0;

}

/* @toggle switch focus state
-------------------------------------------------------------- */
.themesaller-forms .switch > input:focus { outline: none; }
.themesaller-forms .switch > input:focus + label { color: #fff; border-color: #C7C7C7; background:#C7C7C7; }
.themesaller-forms .switch > input:focus + label:after { background: #fff; }

/* @toggle switch normal state
--------------------------------------------------------------- */
.themesaller-forms .switch > input:checked + label {
    border-color: #f00;
	background: #f00;
	padding-left: 33px;
	color: white;
}

.themesaller-forms .switch > input:checked + label:before {
    content: attr(data-on);
    left: 1px;
	top:1px;
}

.themesaller-forms .switch > input:checked + label:after {
    margin: 1px;
    width: 19px;
    height: 19px;
    background: white;
}

/* @toggle switch normal state focus
--------------------------------------------------------------------------------- */
.themesaller-forms .switch > input:checked:focus + label { background: #3c9b39; border-color: #3c9b39; }
.themesaller-forms .switch-round > label {
	-webkit-border-radius: 13px;
	-moz-border-radius: 13px;
	-o-border-radius: 13px;
	border-radius: 13px;
}
.themesaller-forms .switch-round > label + span{ top:-2px; }
.themesaller-forms .switch-round > label:before { width: 33px; }
.themesaller-forms .switch-round > label:after {
    width: 19px;
	color:#D7D7D7;
	content: "\2022";
	font:20px/20px Times, Serif;
	-webkit-border-radius: 13px;
	-moz-border-radius: 13px;
	-o-border-radius: 13px;
	border-radius: 13px;
}

.themesaller-forms .switch-round > input:checked + label { padding-left: 33px; }
.themesaller-forms .switch-round > input:checked + label:after{ color:#f00; }

/* @buttons
----------------------------------------------------- */
.themesaller-forms .button {
    border: 0;
	height: 42px;
	color: #243140;
	line-height: 1;
	font-size:15px;
    cursor: pointer;
	padding: 0 18px;
	text-align: center;
	vertical-align: top;
    background: #DBDBDB;
	display: inline-block;
	-webkit-user-drag: none;
	text-shadow: 0 1px rgba(255, 255, 255, 0.2);
}

/* @buttons :hover, :active states
---------------------------------------------------------------- */
.themesaller-forms .button:hover { color: #243140; background: #E8E8E8; }
.themesaller-forms .button:active{ color: #1d2938; background: #C4C4C4; }
.themesaller-forms a.button,
.themesaller-forms span.button,
.themesaller-forms label.button { line-height: 42px; text-decoration: none; }
.themesaller-forms .button i{ font-size:14px; }
.themesaller-forms .button-list .button{ margin-bottom:5px; }

/* @primary button theme
-------------------------------------------- */
.themesaller-forms .btn-primary {  background-color: #f00;  }
.themesaller-forms .btn-primary:hover,
.themesaller-forms .btn-primary:focus { background-color: #3f51b5; }
.themesaller-forms .btn-primary:active{ background-color: #3c9b39; }
.themesaller-forms .btn-primary,
.themesaller-forms .btn-primary:hover,
.themesaller-forms .btn-primary:focus,
.themesaller-forms .btn-primary:active{ color: #fff; text-shadow: 0 1px rgba(0, 0, 0, 0.08); }

/* @rounded buttons
-------------------------------------------- */
.themesaller-forms .btn-rounded{
	-webkit-border-radius:22px;
	-moz-border-radius:22px;
	-o-border-radius:22px;
	border-radius:22px;
}

/* @left + right buttons :: look like IOS
-------------------------------------------- */
.themesaller-forms .button-left,
.themesaller-forms .button-right {
    position: relative;
	z-index:9;
}

.themesaller-forms .button-left:before,
.themesaller-forms .button-right:before {
    content:'';
    z-index:-1;
    width: 32px;
    height: 32px;
	position: absolute;
    background-color: inherit;
	border-color: inherit;
    border: none;
	top: 5px;
}

.themesaller-forms .button-left {
    border-left-width: 0;
	padding: 0 18px 0 7px;
    -webkit-border-radius: 0 3px 3px 0;
	-moz-border-radius: 0 3px 3px 0;
	-o-border-radius: 0 3px 3px 0;
	border-radius: 0 3px 3px 0;
	margin-left:20px;
}

.themesaller-forms .button-left:before {
    left: -15px;
    -webkit-border-radius: 2px 5px 0 5px;
	-moz-border-radius: 2px 5px 0 5px;
	-o-border-radius: 2px 5px 0 5px;
	border-radius: 2px 5px 0 5px;
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.themesaller-forms .button-right {
    padding: 0 7px 0 18px;
    border-right-width: 0;
    -webkit-border-radius: 3px 0 0 3px;
	-moz-border-radius: 3px 0 0 3px;
	-o-border-radius: 3px 0 0 3px;
	border-radius: 3px 0 0 3px;
	margin-right:20px;
}

.themesaller-forms .button-right:before {
    right: -15px;
    -webkit-border-radius: 5px 2px 5px 0;
	-moz-border-radius: 5px 2px 5px 0;
	-o-border-radius: 5px 2px 5px 0;
	border-radius: 5px 2px 5px 0;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
}

/* @left right button pointed button shapes
------------------------------------------------ */
.themesaller-forms .btn-pointed.button-left,
.themesaller-forms .btn-pointed.button-right{
	-webkit-border-radius:22px;
	-moz-border-radius:22px;
	-o-border-radius:22px;
	border-radius:22px;
}

.themesaller-forms .btn-rounded.button-left{
	-webkit-border-radius: 0 22px 22px 0;
	-moz-border-radius: 0 22px 22px 0;
	-o-border-radius: 0 22px 22px 0;
	border-radius: 0 22px 22px 0;
}

.themesaller-forms .btn-rounded.button-right{
	-webkit-border-radius: 22px 0 0 22px;
	-moz-border-radius: 22px 0 0 22px;
	-o-border-radius: 22px 0 0 22px;
	border-radius: 22px 0 0 22px;
}

/* @push buttons
------------------------------------------------ */
.themesaller-forms .pushed {
	-webkit-box-shadow:inset 0 -0.3em 0 rgba(0,0,0,0.2);
	-moz-box-shadow:inset 0 -0.3em 0 rgba(0,0,0,0.2);
	-o-box-shadow:inset 0 -0.3em 0 rgba(0,0,0,0.2);
	box-shadow:inset 0 -0.3em 0 rgba(0,0,0,0.2);
	position:relative;
}

.themesaller-forms .pushed:active{
	-webkit-box-shadow:inset 0 -0.15em 0 rgba(0,0,0,0.2);
	-moz-box-shadow:inset 0 -0.15em  0 rgba(0,0,0,0.2);
	-o-box-shadow:inset 0 -0.15em  0 rgba(0,0,0,0.2);
	box-shadow:inset 0 -0.15em  0 rgba(0,0,0,0.2);
	top:2px;
}

.themesaller-forms .pushed.button-left:before {
	-webkit-box-shadow:inset 0.35em 0  0 rgba(0,0,0,0.2);
	-moz-box-shadow:inset 0.35em 0 0 rgba(0,0,0,0.2);
	-o-box-shadow:inset 0.35em 0 0 rgba(0,0,0,0.2);
	box-shadow:inset 0.35em 0 0 rgba(0,0,0,0.2);
}

.themesaller-forms .pushed:active.button-left:before{
	-webkit-box-shadow:inset 0.2em 0  0 rgba(0,0,0,0.2);
	-moz-box-shadow:inset 0.2em 0  0 rgba(0,0,0,0.2);
	-o-box-shadow:inset 0.2em 0  0 rgba(0,0,0,0.2);
	box-shadow:inset 0.2em 0  0 rgba(0,0,0,0.2);
}

.themesaller-forms .pushed.button-right:before {
	-webkit-box-shadow:inset  -0.35em 0  0 rgba(0,0,0,0.2);
	-moz-box-shadow:inset -0.35em 0  0  rgba(0,0,0,0.2);
	-o-box-shadow:inset -0.35em 0  0  rgba(0,0,0,0.2);
	box-shadow:inset -0.35em 0  0  rgba(0,0,0,0.2);
}

.themesaller-forms .pushed:active.button-right:before{
	-webkit-box-shadow:inset -0.2em 0  0 rgba(0,0,0,0.2);
	-moz-box-shadow:inset -0.2em 0  0 rgba(0,0,0,0.2);
	-o-box-shadow:inset -0.2em 0  0 rgba(0,0,0,0.2);
	box-shadow:inset -0.2em 0  0 rgba(0,0,0,0.2);
}

.tagline span {
  border: 1px solid #999;
  color: #f00 !important;
  padding: 5px 16px !important;
}

/* @adjust buttons in form footer
------------------------------------------------ */
.themesaller-forms .form-footer .button{ display: block;
margin: 0 auto; }
.themesaller-forms .align-right .button{ margin-right:0; margin-left:10px; }

/* @social buttons :: facebook :: twitter :: google +
---------------------------------------------------- */
.themesaller-forms .twitter,
.themesaller-forms .twitter:hover,
.themesaller-forms .twitter:focus,
.themesaller-forms .facebook,
.themesaller-forms .facebook:hover,
.themesaller-forms .facebook:focus,
.themesaller-forms .googleplus,
.themesaller-forms .googleplus:hover,
.themesaller-forms .googleplus:focus { color:#fff; text-shadow: 0 1px rgba(0, 0, 0, 0.08); }
.themesaller-forms .facebook { background-color:#3b5998; }
.themesaller-forms .twitter { background-color:#00acee;  }
.themesaller-forms .googleplus { background-color:#dd4b39; }
.themesaller-forms .facebook:hover,
.themesaller-forms .facebook:focus { background-color:#25385F;  }
.themesaller-forms .twitter:hover,
.themesaller-forms .twitter:focus { background-color:#00749F;  }
.themesaller-forms .googleplus:hover,
.themesaller-forms .googleplus:focus { background-color:#8D2418;  }
.themesaller-forms .span-left{ padding-left:52px; text-align:left; }
.themesaller-forms .btn-social { position:relative; margin-bottom:5px;  }
.themesaller-forms .btn-social i{ font-size:22px; position:relative; top:2px;    }
.themesaller-forms .btn-social span{
	-webkit-border-radius:3px 0 0 3px;
	-moz-border-radius:3px 0 0 3px;
	-o-border-radius:3px 0 0 3px;
	border-radius:3px 0 0 3px;
	display:inline-block;
	text-align:center;
	position:absolute;
	width:42px;
	left:0;
}

.themesaller-forms .twitter span{ background-color:#009AD5; }
.themesaller-forms .facebook span{ background-color:#31497D; }
.themesaller-forms .googleplus span{ background-color:#C03121; }

/* @rating and review star widget :: with hover back afetr selecting
------------------------------------------------------------------------ */
.themesaller-forms .rating { overflow: hidden; }
.themesaller-forms .rating.block { display:block; margin:10px 0; }
.themesaller-forms .rating label{color: #A2A6A8;}
.themesaller-forms .rating label i{ font-size:17px; text-align:center; color:inherit;  }
.themesaller-forms .rating label span{ font:22px/22px Times, Serif; }
.themesaller-forms .rating-star{ margin-left:4px; }
.themesaller-forms .rating-input { position: absolute; left:-9999px; top: auto; }
.themesaller-forms .rating:hover .rating-star:hover,
.themesaller-forms .rating:hover .rating-star:hover ~ .rating-star,
.themesaller-forms .rating-input:checked ~ .rating-star { color: #f00;	}
.themesaller-forms .rating-star,
.themesaller-forms .rating:hover .rating-star {
	width: 18px;
	float: right;
	display: block;
	cursor:pointer;
	color: #A2A6A8;
}


/* @smart widget
   @this widget helps us to position an element eg button or label or span
   @the positions can either be left or right while the input stays 100%
   @you ca use this to rapidly create search widgets, newsletter subscribe etc
---------------------------------------------------------------------------------*/
.themesaller-forms .smart-widget,
.themesaller-forms .append-picker-icon,
.themesaller-forms .prepend-picker-icon { position: relative; display:block; }
.themesaller-forms .smart-widget .field input,
.themesaller-forms .append-picker-icon input,
.themesaller-forms .prepend-picker-icon input { width: 100%; }

.themesaller-forms .append-picker-icon button,
.themesaller-forms .prepend-picker-icon button,
.themesaller-forms .smart-widget .button {
	border:1px solid #CFCFCF;
	background: #F5F5F5;
    position: absolute;
	cursor: pointer;
	color: #626262;
	height: 42px;
    top: 0;
}

.themesaller-forms .sm-right .button,
.themesaller-forms .append-picker-icon button{ border-left:0; }
.themesaller-forms .sm-left .button,
.themesaller-forms .prepend-picker-icon button{ border-right:0; }

.themesaller-forms .sm-left .button,
.themesaller-forms .prepend-picker-icon button { left:0; }
.themesaller-forms .sm-right .button,
.themesaller-forms .append-picker-icon button {  right:0; }

/* @smart widget buttons - to left
------------------------------------------------- */
.themesaller-forms .sml-50,
.themesaller-forms .prepend-picker-icon { padding-left: 50px; }
.themesaller-forms .sml-50 .button,
.themesaller-forms .prepend-picker-icon button{ width: 50px; }
.themesaller-forms .sml-80{ padding-left: 80px; }
.themesaller-forms .sml-80 .button { width: 80px; }
.themesaller-forms .sml-120{ padding-left: 120px; }
.themesaller-forms .sml-120 .button { width: 120px; }

/* @smart widget buttons - to right
------------------------------------------------- */
.themesaller-forms .smr-50,
.themesaller-forms .append-picker-icon{ padding-right: 50px; }
.themesaller-forms .smr-50 .button,
.themesaller-forms .append-picker-icon button{ width: 50px; }
.themesaller-forms .smr-80{ padding-right: 80px; }
.themesaller-forms .smr-80 .button { width: 80px; }
.themesaller-forms .smr-120{ padding-right: 120px; }
.themesaller-forms .smr-120 .button { width: 120px; }


/* @icon append (right) :: prepend (left)
------------------------------------------------- */
.themesaller-forms .append-icon,
.themesaller-forms .prepend-icon{
    display: inline-block;
    vertical-align: top;
    position: relative;
	width:100%;
}

.themesaller-forms .append-icon .field-icon,
.themesaller-forms .prepend-icon .field-icon{
	top:0;
	z-index:4;
	width:42px;
	height:42px;
	color: inherit;
	line-height:42px;
	position:absolute;
	text-align: center;
    -webkit-transition: all 0.5s ease-out;
    -moz-transition: all 0.5s ease-out;
    -ms-transition: all 0.5s ease-out;
    -o-transition: all 0.5s ease-out;
    transition: all 0.5s ease-out;
    pointer-events: none;
}

.themesaller-forms .append-icon .field-icon i,
.themesaller-forms .prepend-icon .field-icon i{
	position:relative;
	font-size:14px;
}

.themesaller-forms .prepend-icon .field-icon{ left:0;  }
.themesaller-forms .append-icon .field-icon{ right:0; }
.themesaller-forms .prepend-icon > input,
.themesaller-forms .prepend-icon > textarea{ padding-left:36px; }
.themesaller-forms .append-icon > input,
.themesaller-forms .append-icon > textarea{ padding-right:36px; padding-left:10px;  }
.themesaller-forms .append-icon > textarea{ padding-right:36px; }

/* @tooltips on inputs + textareas
------------------------------------------------- */
.themesaller-forms .tooltip {
	position: absolute;
	z-index: -1;
	opacity: 0;
	color: #fff;
	width: 184px;
	left: -9999px;
	top:auto;
	font-size: 11px;
	font-weight:normal;
	background: #333333;
	-webkit-transition: margin 0.6s, opacity 0.6s;
	-moz-transition: margin 0.6s, opacity 0.6s;
	-ms-transition: margin 0.6s, opacity 0.6s;
	-o-transition: margin 0.6s, opacity 0.6s;
	transition: margin 0.6s, opacity 0.6s;
}

.themesaller-forms .tooltip > em{ padding:12px; font-style:normal; display:block; position:static; }
.themesaller-forms .tooltip:after { content: ''; position: absolute; }
.themesaller-forms .gui-input:focus + .tooltip,
.themesaller-forms .gui-textarea:focus + .tooltip { opacity: 1; z-index: 999; }

/* @tooltip left
------------------------------------------------- */
.themesaller-forms .tip-left { top:1px; margin-right:-20px; }
.themesaller-forms .tip-left:after {
	top:12px;
	left: 100%;
	border-left: 8px solid #333333;
	border-top: 8px solid transparent;
	border-bottom: 8px solid transparent;
}

.themesaller-forms .gui-input:focus + .tip-left,
.themesaller-forms .gui-textarea:focus + .tip-left {
	margin-right:5px;
	right: 100%;
	left: auto;
}

/* @tooltip right
------------------------------------------------- */
.themesaller-forms .tip-right { top:1px; margin-left:-20px; }
.themesaller-forms .tip-right:after {
	top:12px;
	right: 100%;
	border-right: 8px solid #333333;
	border-top: 8px solid transparent;
	border-bottom: 8px solid transparent;
}

.themesaller-forms .gui-input:focus + .tip-right,
.themesaller-forms .gui-textarea:focus + .tip-right { left: 100%; margin-left:5px; }

/* @tooltip right-top
------------------------------------------------- */
.themesaller-forms .tip-right-top { bottom: 100%; margin-bottom: -20px; }
.themesaller-forms .tip-right-top:after {
	top: 100%;
	right: 12px;
	border-top: 8px solid #333333;
	border-right: 8px solid transparent;
	border-left: 8px solid transparent;
}

.themesaller-forms .gui-input:focus + .tip-right-top,
.themesaller-forms .gui-textarea:focus + .tip-right-top {
	right: 0;
	left: auto;
	margin-bottom: 10px;
}

/* @tooltip left-top
------------------------------------------------- */
.themesaller-forms .tip-left-top { bottom: 100%; margin-bottom: -20px; }
.themesaller-forms .tip-left-top:after {
	top: 100%;
	left: 12px;
	border-top: 8px solid #333333;
	border-right: 8px solid transparent;
	border-left: 8px solid transparent;
}

.themesaller-forms .gui-input:focus + .tip-left-top,
.themesaller-forms .gui-textarea:focus + .tip-left-top {
	left: 0;
	right: auto;
	margin-bottom: 10px;
}

/* @tooltip right-bottom
------------------------------------------------- */
.themesaller-forms .tip-right-bottom { top: 100%; margin-top: -20px; }
.themesaller-forms .tip-right-bottom:after {
	right: 12px;
	bottom: 100%;
	border-bottom: 8px solid #333333;
	border-right: 8px solid transparent;
	border-left: 8px solid transparent;
}

.themesaller-forms .gui-input:focus + .tip-right-bottom,
.themesaller-forms .gui-textarea:focus + .tip-right-bottom {
	margin-top: 10px;
	left: auto;
	right: 0;
}

/* @tooltip left-bottom
------------------------------------------------- */
.themesaller-forms .tip-left-bottom { top: 100%; margin-top: -20px; }
.themesaller-forms .tip-left-bottom:after {
	left: 12px;
	bottom: 100%;
	border-bottom: 8px solid #333333;
	border-right: 8px solid transparent;
	border-left: 8px solid transparent;
}

.themesaller-forms .gui-input:focus + .tip-left-bottom,
.themesaller-forms .gui-textarea:focus + .tip-left-bottom {
	margin-top:10px;
	right: auto;
	left: 0;
}

/* @lists
-------------------------------------------------------------- */
.themesaller-forms .smart-list{ list-style:none; margin:0; padding:0; }
.themesaller-forms .smart-list li{ margin-bottom:20px; }

/* @notification messages | info | error | warning | success
-------------------------------------------------------------- */
.themesaller-forms .form-msg{ display:none; }
.themesaller-forms .notification { color: #444; padding:15px; position:relative; }
.themesaller-forms .notification p{ margin:0; padding:0 15px; padding-left:5px; line-height:normal;  }
.themesaller-forms .notification .close-btn{
	margin-top: -7px;
	padding: inherit;
	position: absolute;
	text-decoration:none;
    font: bold 20px/20px Arial, sans-serif;
	opacity: 0.65;
	color: inherit;
    display: block;
    right:1px;
	top:14%;
}

.themesaller-forms .notification .close-btn:hover{ opacity: 1; }
.themesaller-forms .alert-info { color:#163161; background-color: #cfe6fc; }
.themesaller-forms .alert-success { color:#336633; background-color: #d2f7ad; }
.themesaller-forms .alert-warning { color: #CC6600; background-color: #fae7a2; }
.themesaller-forms .alert-error { color:#990000; background-color: #FBDBCF; }

/* @validaion - error state
------------------------------------- */
.themesaller-forms .state-error .gui-input,
.themesaller-forms .state-error .gui-textarea,
.themesaller-forms .state-error.select > select,
.themesaller-forms .state-error.select-multiple > select,
.themesaller-forms .state-error input:hover + .checkbox,
.themesaller-forms .state-error input:hover + .radio,
.themesaller-forms .state-error input:focus + .checkbox,
.themesaller-forms .state-error input:focus + .radio,
.themesaller-forms .state-error .checkbox,
.themesaller-forms .state-error .radio{
	background:#FEE9EA;
	border-color:#DE888A;
}

.themesaller-forms .state-error .gui-input:focus,
.themesaller-forms .state-error .gui-textarea:focus,
.themesaller-forms .state-error.select > select:focus,
.themesaller-forms .state-error.select-multiple > select:focus{
	-webkit-box-shadow:0px 0px 3px #DE888A inset;
	-moz-box-shadow:0px 0px 3px #DE888A inset;
	-o-box-shadow:0px 0px 3px #DE888A inset;
	box-shadow:0px 0px 3px #DE888A inset;
}

.themesaller-forms .state-error .gui-input ~ .field-icon i,
.themesaller-forms .state-error .gui-textarea ~ .field-icon i{  color: #DE888A; }
.themesaller-forms .state-error.select .arrow { color: #DE888A; }
.themesaller-forms .state-error.select > select:focus + .arrow{ color:#DE888A; }
.themesaller-forms .state-error .gui-input ~ .input-hint,
.themesaller-forms .state-error.file .gui-file:hover + .gui-input,
.themesaller-forms .state-error .gui-textarea ~ .input-hint { border-color:#DE888A; }
.themesaller-forms .state-error + em{
	display: block!important;
	margin-top: 6px;
	padding: 0 3px;
	font-family:Arial, Helvetica, sans-serif;
	font-style: normal;
	line-height: normal;
	font-size:0.85em;
	color:#DE888A;
}

/* @validaion - success state
-------------------------------------------------- */
.themesaller-forms .state-success .gui-input,
.themesaller-forms .state-success .gui-textarea,
.themesaller-forms .state-success.select > select,
.themesaller-forms .state-success.select-multiple > select,
.themesaller-forms .state-success input:hover + .checkbox,
.themesaller-forms .state-success input:hover + .radio,
.themesaller-forms .state-success input:focus + .checkbox,
.themesaller-forms .state-success input:focus + .radio,
.themesaller-forms .state-success .checkbox,
.themesaller-forms .state-success .radio{
	background:#F0FEE9;
	border-color:#A5D491;
}

.themesaller-forms .state-success .gui-input:focus,
.themesaller-forms .state-success .gui-textarea:focus,
.themesaller-forms .state-success.select > select:focus,
.themesaller-forms .state-success.select-multiple > select:focus{
	-webkit-box-shadow:0px 0px 3px #A5D491 inset;
	-moz-box-shadow:0px 0px 3px #A5D491 inset;
	-o-box-shadow:0px 0px 3px #A5D491 inset;
	box-shadow:0px 0px 3px #A5D491 inset;
}

.themesaller-forms .state-success .gui-input ~ .field-icon i,
.themesaller-forms .state-success .gui-textarea ~ .field-icon i{  color: #A5D491; }
.themesaller-forms .state-success.select .arrow { color: #A5D491; }
.themesaller-forms .state-success.select > select:focus + .arrow{ color:#A5D491; }
.themesaller-forms .state-success .gui-input ~ .input-hint,
.themesaller-forms .state-success.file .gui-file:hover + .gui-input,
.themesaller-forms .state-success .gui-textarea ~ .input-hint { border-color:#A5D491; }

/* @disabled state
----------------------------------------------- */
.themesaller-forms .button[disabled],
.themesaller-forms .state-disabled .button,
.themesaller-forms input[disabled] + .radio,
.themesaller-forms input[disabled] + .checkbox,
.themesaller-forms .switch > input[disabled] + label{
	cursor: default;
	opacity:0.5;
}

.themesaller-forms .gui-input[disabled],
.themesaller-forms .gui-textarea[disabled],
.themesaller-forms .select > select[disabled],
.themesaller-forms .select-multiple select[disabled],
.themesaller-forms .gui-input[disabled] ~ .input-hint,
.themesaller-forms .file .gui-file[disabled] + .gui-input,
.themesaller-forms .file .gui-file[disabled]:hover + .gui-input,
.themesaller-forms .gui-textarea[disabled] ~ .input-hint {
	background-color: #f4f6f6;
	border-color: #d5dbdb!important;
	cursor: default;
	color: #d5dbdb;
	opacity:0.7;
}

.themesaller-forms input[disabled] ~ .field-icon i,
.themesaller-forms textarea[disabled] ~ .field-icon i,
.themesaller-forms .select > select[disabled] + .arrow{
	opacity:0.4;
}

/* @datepicker - requires jquery ui
----------------------------------------------- */
.ui-datepicker {
	width: 18em;
	margin-top:8px;
	display: none;
	background: #fff;
	position:relative;
	font: 14px/1.55  "Roboto", Arial, Helvetica, sans-serif;
	-webkit-box-shadow: 0 0 4px rgba(0,0,0,.1);
	-moz-box-shadow: 0 0 4px rgba(0,0,0,.1);
	-o-box-shadow: 0 0 4px rgba(0,0,0,.1);
	box-shadow: 0 0 4px rgba(0,0,0,.1);
	border:1px solid #CFCFCF;
	z-index:9999!important;
	text-align: center;
	color: #666;

}

.ui-datepicker a { color: #404040; text-align:center; }
.ui-datepicker .ui-state-disabled span{ color:#DBDBDB;}
.ui-datepicker .ui-datepicker-header {
	position: relative;
	background: #F5F5F5;
	border-bottom:1px solid #CFCFCF;
	line-height: 27px;
	font-size: 15px;
	padding: 10px;
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
	width: 34px;
	height: 34px;
	display: block;
	font-size: 14px;
	position: absolute;
	text-decoration: none;
	cursor: pointer;
	color:#f00;
	top:20.5%;
}

.ui-datepicker .ui-datepicker-prev { left: 2px;  }
.ui-datepicker .ui-datepicker-next { right: 2px; }
.ui-datepicker .ui-datepicker-title {
	margin: 0 2.3em;
	line-height: 1.8em;
	text-align: center;
	color:#f00;
}

.ui-datepicker .ui-datepicker-title select { font-size: 1em; margin: 1px 0; }
.ui-datepicker select.ui-datepicker-month-year { width: 100%; }
.ui-datepicker select.ui-datepicker-month,
.ui-datepicker select.ui-datepicker-year { width: 49%; }
.ui-datepicker table {
	width: 100%;
	font-size: .9em;
	margin: 0 0 .4em;
	border-collapse: collapse;
}

.ui-datepicker th {
	padding: .5em .3em;
	text-align: center;
	font-weight: bold;
	border: 0;
}

.ui-datepicker td { border: 0; padding:2px 5px; }
.ui-datepicker td span,
.ui-datepicker td a {
	padding: .25em;
	display: block;
	text-align: center;
	text-decoration: none;
}

.ui-datepicker td span:hover,
.ui-datepicker td a:hover {  background:#F5F5F5; }
.ui-datepicker .ui-state-disabled span:hover{ background:none; }
.ui-datepicker-today a, .ui-datepicker-today a:hover,
.ui-datepicker .ui-state-highlight {
	font-weight: 700;
	background: #f00!important;
	color:#fff;
}

/* @multiple calendars || not responsive use carefully
--------------------------------------------------------------- */
.cal-widget .ui-datepicker { width: 100%; margin-top:0; }
.cal-widget .ui-datepicker:before{ display:none; }
.ui-datepicker.ui-datepicker-multi { width: auto; }
.ui-datepicker-multi .ui-datepicker-group { float: left; }
.ui-datepicker-multi .ui-datepicker-group table { width: 95%; margin: 0 auto .4em; }
.ui-datepicker-multi-2 .ui-datepicker-group { width: 50%; }
.ui-datepicker-multi-3 .ui-datepicker-group { width: 33.333%; }
.ui-datepicker-multi-4 .ui-datepicker-group { width: 25%; }
.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header { border-left-width: 0; }
.ui-datepicker-multi .ui-datepicker-buttonpane { clear: left; }
.ui-datepicker-row-break { clear: both; width: 100%; font-size: 0; }

/* @ ui buttons
---------------------------------------------------------------- */
.ui-datepicker-buttonpane{ border-top:1px solid #CFCFCF; padding:10px;  }
.ui-datepicker-buttonpane button {
	padding: 8px 12px;
	margin-right: .2em;
	position: relative;
	line-height: normal;
	display: inline-block;
	-webkit-user-drag: none;
	text-shadow: 0 1px rgba(255, 255, 255, 0.2);
	vertical-align: middle;
	background: #DBDBDB;
	text-align: center;
	overflow: visible;
	cursor: pointer;
	color: #243140;
	border:0;
}

/* @ ui buttons :hover, :active states
---------------------------------------------------------------- */
.ui-datepicker-buttonpane button:hover { color: #243140; background: #E8E8E8; }
.ui-datepicker-buttonpane button:active{ color: #1d2938; background: #C4C4C4; }
.ui-monthpicker .ui-datepicker-header{ margin-bottom:3px; }

/* @ui slider - requires jquery ui
------------------------------------------------------*/
.themesaller-forms .slider-wrapper,
.themesaller-forms .sliderv-wrapper{
	background:#E5E5E5;
	position:relative;
}

.themesaller-forms .ui-slider {
	position: relative;
	text-align: left;
}

.themesaller-forms .ui-slider .ui-slider-handle {
	position: absolute;
	z-index: 2;
	width: 1.5em;
	height: 1.5em;
	cursor: default;
	background:#fff;
	border:3px solid #f00;
	-webkit-border-radius:20px;
	-moz-border-radius:20px;
	-o-border-radius:20px;
	border-radius:20px;
	-ms-touch-action: none;
	touch-action: none;
	margin-top:-3px;
	outline:none;
}

.themesaller-forms .ui-slider .ui-slider-handle:before{
	content: '';
    width: 7px;
    height: 7px;
	position:absolute;
    background-color: #f00;
  	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	-o-border-radius: 10px;
	border-radius: 10px;
	z-index: 2;
	left:4px;
	top:4px;
}

.themesaller-forms .ui-slider .ui-slider-range {
	position: absolute;
	z-index: 1;
	font-size: .7em;
	display: block;
	border: 0;
	background-position: 0 0;
	background-color: #f00;
}

.themesaller-forms .ui-slider.ui-state-disabled .ui-slider-handle,
.themesaller-forms .ui-slider.ui-state-disabled .ui-slider-range { filter: inherit; }
.themesaller-forms .ui-slider-horizontal { height: .5em; }
.themesaller-forms .ui-slider-horizontal .ui-slider-handle { top: -.3em; margin-left: -.6em; }
.themesaller-forms .ui-slider-horizontal .ui-slider-range { top: 0; height: 100%; }
.themesaller-forms .ui-slider-horizontal .ui-slider-range-min { left: 0; }
.themesaller-forms .ui-slider-horizontal .ui-slider-range-max { right: 0; }
.themesaller-forms .ui-slider-vertical,
.themesaller-forms .sliderv-wrapper { width: .5em; height: 100px; }
.themesaller-forms .ui-slider-vertical .ui-slider-handle { left: -.45em; margin-left: 0; margin-bottom: -.6em; }
.themesaller-forms .ui-slider-vertical .ui-slider-range { left: 0; width: 100%; }
.themesaller-forms .ui-slider-vertical .ui-slider-range-min { bottom: 0; }
.themesaller-forms .ui-slider-vertical .ui-slider-range-max { top: 0; }
.themesaller-forms .slider-input{  color:#f6931f!important; border:0; background:none; }
.themesaller-forms .slider-group .sliderv-wrapper{ height:150px; float:left; margin:15px 15px;   }
.themesaller-forms .ui-slider .ui-state-active {
	cursor: -webkit-grabbing;
	cursor: -moz-grabbing;
	cursor: grabbing;
}

/* @ui slider tooltip
------------------------------------------------------*/
.themesaller-forms .slider-tip {
	display: block;
	position: absolute;
	text-align: center;
	font: 10pt Tahoma, Arial, sans-serif ;
	background: #333333;
	padding:10px;
	color: #fff;
}

.themesaller-forms .slider-wrapper .slider-tip{ top: -50px; left:-15px; }
.themesaller-forms .slider-wrapper .slider-tip:after {
	content: '';
	position: absolute;
	top: 98%;
	left: 35%;
	border-top: 8px solid #333333;
	border-right: 8px solid transparent;
	border-left: 8px solid transparent;
}

.themesaller-forms .sliderv-wrapper .slider-tip{ left: 30px; top:-12px; }
.themesaller-forms .sliderv-wrapper .slider-tip:after{
	content: '';
	position: absolute;
	top:30%;
	right: 98%;
	border-right: 8px solid #333333;
	border-top: 8px solid transparent;
	border-bottom: 8px solid transparent;
}

/* @ui slider themes
------------------------------------------------------*/
.themesaller-forms .yellow-slider .ui-slider .ui-slider-handle{ border-color:#faa226; }
.themesaller-forms .yellow-slider .ui-slider .ui-slider-handle:before,
.themesaller-forms .yellow-slider .ui-slider .ui-slider-range { background-color: #faa226;  }
.themesaller-forms .red-slider .ui-slider .ui-slider-handle{ border-color:#ee4f3d; }
.themesaller-forms .red-slider .ui-slider .ui-slider-handle:before,
.themesaller-forms .red-slider .ui-slider .ui-slider-range { background-color:#ee4f3d;  }
.themesaller-forms .purple-slider .ui-slider .ui-slider-handle{ border-color:#006DF0; }
.themesaller-forms .purple-slider .ui-slider .ui-slider-handle:before,
.themesaller-forms .purple-slider .ui-slider .ui-slider-range { background-color:#006DF0;  }
.themesaller-forms .blue-slider .ui-slider .ui-slider-handle{ border-color:#00acee; }
.themesaller-forms .blue-slider .ui-slider .ui-slider-handle:before,
.themesaller-forms .blue-slider .ui-slider .ui-slider-range { background-color:#00acee;  }
.themesaller-forms .black-slider .ui-slider .ui-slider-handle{ border-color:#505558; }
.themesaller-forms .black-slider .ui-slider .ui-slider-handle:before,
.themesaller-forms .black-slider .ui-slider .ui-slider-range { background-color:#505558;  }
.themesaller-forms .green-slider .ui-slider .ui-slider-handle{ border-color:#0E993C; }
.themesaller-forms .green-slider .ui-slider .ui-slider-handle:before,
.themesaller-forms .green-slider .ui-slider .ui-slider-range { background-color:#0E993C;  }

/* @ui timepicker - requires jquery ui
------------------------------------------------------*/
.ui-timepicker-div .ui-widget-header {
	position: relative;
	background: #F5F5F5;
	line-height: 27px;
	font-size: 15px;
	padding: 10px;
 }

.ui-timepicker-div dl { text-align: left; border:1px solid #CFCFCF; border-width:1px 0 0 0; padding:15px 10px; margin:0; }
.ui-timepicker-div dl dt { float: left; clear:left; padding: 0 0 0 5px; }
.ui-timepicker-div dl dd { margin: 0 10px 20px 40%; }
.ui-timepicker-div dl .ui_tpicker_hour,
.ui-timepicker-div dl .ui_tpicker_minute,
.ui-timepicker-div dl .ui_tpicker_second,
.ui-timepicker-div dl .ui_tpicker_millisec{ background:#E5E5E5;  position:relative; top:6px; }
.ui-timepicker-div td { font-size: 90%; }
.ui-tpicker-grid-label { background: none; border: none; margin: 0; padding: 0; }
.ui-timepicker-rtl{ direction: rtl; }
.ui-timepicker-rtl dl { text-align: right; padding: 0 5px 0 0; }
.ui-timepicker-rtl dl dt{ float: right; clear: right; }
.ui-timepicker-rtl dl dd { margin: 0 40% 10px 10px; }

/* @progress bars
------------------------------------------------------*/
.themesaller-forms .progress-section{ display:none; }
.themesaller-forms .progress-bar {
    position: relative;
    background:#E5E5E5;
}

.themesaller-forms .progress-bar .percent {
	position:absolute;
	display:inline-block;
	top:-3px;
	right:-24px;
	font-size:9px;
	color:#93A2AA;
}
.themesaller-forms .progress-bar > .bar {
	width:60%;
	height:7px;
	display: block;
	background-size: 16px 16px;
	background-color: #bdc3c7;
}

/* @progress bar themes
----------------------------------------------------------- */
.themesaller-forms .bar-primary > .bar  { background-color: #f00; }
.themesaller-forms .bar-blue > .bar     { background-color: #00acee; }
.themesaller-forms .bar-black > .bar    { background-color: #505558; }
.themesaller-forms .bar-green > .bar    { background-color: #0E993C; }
.themesaller-forms .bar-purple > .bar   { background-color: #9464e2; }
.themesaller-forms .bar-red > .bar      { background-color: #ee4f3d; }
.themesaller-forms .bar-yellow > .bar   { background-color: #faa226; }

/* @progress bar strips + animation IE10+
----------------------------------------------------------- */
.themesaller-forms .ui-slider .ui-slider-range,
.themesaller-forms .progress > button[type="submit"],
.themesaller-forms .progress > button[type="submit"]:hover,
.themesaller-forms .progress-bar > .bar{
    background-size: 16px 16px;
    background-image: -webkit-linear-gradient(top left,
					  transparent, transparent 25%, rgba(255, 255, 255, 0.3) 25%, rgba(255, 255, 255, 0.3) 50%,
					  transparent 50%, transparent 75%, rgba(255, 255, 255, 0.3) 75%, rgba(255, 255, 255, 0.3));

    background-image: -moz-linear-gradient(top left,
					  transparent, transparent 25%, rgba(255, 255, 255, 0.3) 25%, rgba(255, 255, 255, 0.3) 50%,
					  transparent 50%, transparent 75%, rgba(255, 255, 255, 0.3) 75%, rgba(255, 255, 255, 0.3));

    background-image: -o-linear-gradient(top left,
					  transparent, transparent 25%, rgba(255, 255, 255, 0.3) 25%, rgba(255, 255, 255, 0.3) 50%,
					  transparent 50%, transparent 75%, rgba(255, 255, 255, 0.3) 75%, rgba(255, 255, 255, 0.3));

    background-image: linear-gradient(to bottom right,
					  transparent, transparent 25%, rgba(255, 255, 255, 0.3) 25%, rgba(255, 255, 255, 0.3) 50%,
					  transparent 50%, transparent 75%, rgba(255, 255, 255, 0.3) 75%, rgba(255, 255, 255, 0.3));
}

.themesaller-forms .progress > button[type="submit"],
.themesaller-forms .progress > button[type="submit"]:hover,
.themesaller-forms .progress-animated > .bar{
	-webkit-animation: sfprogress .6s linear infinite;
	-moz-animation: sfprogress .6s linear infinite;
	-o-animation: sfprogress .6s linear infinite;
    animation: sfprogress .6s linear infinite;
}

.themesaller-forms .progress > button[type="submit"]:hover{ cursor:wait; }

@-webkit-keyframes sfprogress {
    from { background-position: 0 0; }
	to { background-position: -16px 0; }
}


@-moz-keyframes sfprogress {
	from { background-position: 0 0; }
	to { background-position: -16px 0; }
}

@-o-keyframes sfprogress {
	from { background-position: 0 0; }
	to { background-position: -16px 0; }
}

@keyframes sfprogress {
    from { background-position: 0 0; }
	to { background-position: -16px 0; }
}


/* @google map :: block elements
----------------------------------------------------------------------- */
.themesaller-forms .map-container{ padding:10px; border: 1px solid #CFCFCF; }
.themesaller-forms #map_canvas{ width:100%; height:300px; overflow:hidden;  }
.themesaller-forms .block{ display:block; }

/* @form grid
----------------------------------- */

/* @form rows
--------------------------------- */
.themesaller-forms .frm-row{ margin:0 -10px;  }
.themesaller-forms .slider-group:before,
.themesaller-forms .slider-group:after,
.themesaller-forms .frm-row:before,
.themesaller-forms .frm-row:after { display: table; content: ""; line-height: 0; }
.themesaller-forms .slider-group:after,
.themesaller-forms .frm-row:after{ clear: both; }

/* @form columns
----------------------------------- */
.themesaller-forms .frm-row .colm{
	min-height:1px;
	padding-left:10px;
	padding-right:10px;
	position:relative;
	float:left;
}

.themesaller-forms .frm-row .colm1{width:8.33%;}
.themesaller-forms .frm-row .colm2{width:16.66%;}
.themesaller-forms .frm-row .colm3{width:25%;}
.themesaller-forms .frm-row .colm4{width:33.33%;}
.themesaller-forms .frm-row .colm5{width:41.66%;}
.themesaller-forms .frm-row .colm6{width:50%;}
.themesaller-forms .frm-row .colm7{width:58.33%;}
.themesaller-forms .frm-row .colm8{width:66.66%;}
.themesaller-forms .frm-row .colm9{width:75%;}
.themesaller-forms .frm-row .colm10{width:83.33%;}
.themesaller-forms .frm-row .colm11{width:91.66%;}
.themesaller-forms .frm-row .colm12{width:100%; }
.themesaller-forms .frm-row .colm1-5{width:20%;}
.themesaller-forms .frm-row .colm1-8{width:12.5%;}

/* @spacers
--------------------------------------- */
.themesaller-forms .spacer{
	border-top:1px solid #CFCFCF;
	display:block;
	height:0;
}

/* @margin spacers :: modify accordingly
-------------------------------------------- */
.themesaller-forms .spacer-t10{ margin-top:10px; }
.themesaller-forms .spacer-b10{ margin-bottom:10px; }
.themesaller-forms .spacer-t15{ margin-top:15p; }
.themesaller-forms .spacer-b15{ margin-bottom:15px; }
.themesaller-forms .spacer-t20{ margin-top:20px; }
.themesaller-forms .spacer-b20{ margin-bottom:20px; }
.themesaller-forms .spacer-t25{ margin-top:25px; }
.themesaller-forms .spacer-b25{ margin-bottom:25px; }
.themesaller-forms .spacer-t30{ margin-top:30px; }
.themesaller-forms .spacer-b30{ margin-bottom:30px; }
.themesaller-forms .spacer-t40{ margin-top:40px; }
.themesaller-forms .spacer-b40{ margin-bottom:40px; }

/* @padding spacers :: modify accordingly
-------------------------------------------------- */
.themesaller-forms .frm-row .pad-l10{ padding-left:10px; }
.themesaller-forms .frm-row .pad-r10{ padding-right:10px; }
.themesaller-forms .frm-row .pad-l20{ padding-left:20px; }
.themesaller-forms .frm-row .pad-r20{ padding-right:20px; }
.themesaller-forms .frm-row .pad-l30{ padding-left:30px; }
.themesaller-forms .frm-row .pad-r30{ padding-right:30px; }
.themesaller-forms .frm-row .pad-l40{ padding-left:40px; }
.themesaller-forms .frm-row .pad-r40{ padding-right:40px; }

/* @border spacers + text adjust
-------------------------------------------------- */
.themesaller-forms .bdl { border-left:1px solid #CFCFCF;   }
.themesaller-forms .bdr { border-right:1px solid #CFCFCF;  }
.themesaller-forms .fine-grey{ color:#999; }
.themesaller-forms .small-text{ font-size:11px; font-style:normal;  }
.themesaller-forms .text-align{ height:42px; line-height:42px; }

/* @element alignment
-------------------------------------------------- */
.themesaller-forms .align-right{ text-align:right; }
.themesaller-forms .align-center{ text-align:center; }

/* @simple price boxes :: depend on grid
-------------------------------------------------- */
.themesaller-forms .price-box{
	padding:30px;
	text-align:center;
	position:relative;
	border:1px solid #CFCFCF;
	font-family:Arial, Helvetica, sans-serif;
	-webkit-box-shadow:  0px 2px 0px 0px rgba(0, 0, 0, 0.05);
	-moz-box-shadow:  0px 2px 0px 0px rgba(0, 0, 0, 0.05);
	-o-box-shadow:  0px 2px 0px 0px rgba(0, 0, 0, 0.05);
	box-shadow:  0px 2px 0px 0px rgba(0, 0, 0, 0.05);
}

.themesaller-forms .price-box p{ line-height:1.5em; color:#526066; margin-bottom:0; }
.themesaller-forms .price-box h5{ text-transform:uppercase; font-weight:300; margin:0; font-size:15px; color:#B0B2B9; letter-spacing:2px  }
.themesaller-forms .price-box h4{ font-size:60px; font-weight:300; margin:0; color:#626262; }
.themesaller-forms .selected-box h4{ color:#f00; }
.themesaller-forms .price-box h4 sup{ position:relative; font-size:30px; vertical-align:top; top:15px; }
.themesaller-forms .price-box h4 .per-month{font-size:14px; }
.themesaller-forms .expand{ height:50px; line-height:50px!important; border-radius:3px; }

/* @simple price boxes ribbon IE8+
----------------------------------------- */
.themesaller-forms .ribbon,
.themesaller-forms .ribbon-large{
	width:75px;
	height:78px;
	overflow:hidden;
	position:absolute;
	right: -2px;
	top: -2px;
	z-index:1;
}

.themesaller-forms .ribbon-inner{
	font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;
	-webkit-box-shadow:  0px 2px 0px 0px rgba(0, 0, 0, 0.15);
	-moz-box-shadow:  0px 2px 0px 0px rgba(0, 0, 0, 0.15);
	-o-box-shadow:  0px 2px 0px 0px rgba(0, 0, 0, 0.15);
	box-shadow:  0px 2px 0px 0px rgba(0, 0, 0, 0.15);
	-webkit-transform: translate3d(0, 0, 0);
	-webkit-backface-visibility: hidden;
	-webkit-perspective: 1000;
	-webkit-transform:rotate(45deg);
	-moz-transform:rotate(45deg);
	-ms-transform:rotate(45deg);
	-o-transform:rotate(45deg);
	transform:rotate(45deg);
	background: #f00;
	letter-spacing:4px;
	text-align:center;
	position:relative;
	font-weight:700;
	font-size:14px;
	padding:7px 0;
	width:100px;
	color:#fff;
	z-index:1;
	left:3px;
	top:6px;
}

.themesaller-forms .ribbon-inner:before,
.themesaller-forms .ribbon-inner:after{
	content:"";
	border-top:3px solid #3c9b39;
	border-left:3px solid transparent;
	border-right:3px solid transparent;
	position:absolute;
	bottom:-3px;
}

.themesaller-forms .ribbon-inner:before{ left:0px; }
.themesaller-forms .ribbon-inner:after{ right:0px; }
.themesaller-forms .ribbon-large{ width:115px; height:118px; }
.themesaller-forms .ribbon-large .ribbon-inner{
	width:160px;
	left:-8px;
	top:28px;
}

/* @captcha refresh button + icon
-------------------------------------------------- */
.themesaller-forms .sfcode{ padding-left:24px; }
.themesaller-forms .captcode{ padding:0; position:relative; }
.themesaller-forms .captcode img{ position:relative; top:1px;}
.themesaller-forms .refresh-captcha{
	position:absolute;
	background:#f00;
	border:3px solid #3C9B39;
	-webkit-border-radius:30px;
	-moz-border-radius:30px;
	-o-border-radius:30px;
	border-radius:30px;
	right:-15px;
	height:32px;
	width:32px;
	top:4px;
}

.themesaller-forms .refresh-captcha i{
	position:absolute;
	text-align:center;
	line-height:26px;
	font-size:17px;
	color:#fff;
	left:24%;
}

/* @captcha refresh button themes
-------------------------------------------------- */
.themesaller-forms .refresh-black    { background:#505558; border-color: #333333; }
.themesaller-forms .refresh-blue     { background:#00acee; border-color: #0087bb; }
.themesaller-forms .refresh-green    { background:#0E993C; border-color: #0B792F; }
.themesaller-forms .refresh-purple   { background:#9464e2; border-color: #7639da; }
.themesaller-forms .refresh-red      { background:#ee4f3d; border-color: #e42914; }
.themesaller-forms .refresh-yellow   { background:#faa226; border-color: #e88a05; }

/* Firefox select fix - select arrow hack  disabled on FF 30+
-------------------------------------------------------------- */
@-moz-document url-prefix() {
	.themesaller-forms .select:before{
		content: '';
		pointer-events:none;
		-moz-transition:none;
		transition:none;
		position: absolute;
		background: #F5F5F5;
		width: 36px;
		right:1px;
		top:1px;
		bottom:1px;
		z-index:99;
	}

	.themesaller-forms .select > select:focus,
	.themesaller-forms .select > select:hover,
	.themesaller-forms .select:hover select,
	.themesaller-forms .select:hover:before{
		background: #fff;
		-moz-transition:none;
		transition:none;
		-moz-box-shadow:none;
		box-shadow:none;
	}

	.themesaller-forms .select .arrow {  z-index:100;  }
	.themesaller-forms .state-error.select > select:focus,
	.themesaller-forms .state-error.select > select:hover,
	.themesaller-forms .state-error.select:hover select,
	.themesaller-forms .state-error.select:hover:before,
	.themesaller-forms .state-error.select:before { background:#FEE9EA;  }

	.themesaller-forms .state-success.select > select:focus,
	.themesaller-forms .state-success.select > select:hover,
	.themesaller-forms .state-success.select:hover select,
	.themesaller-forms .state-success.select:hover:before,
	.themesaller-forms .state-success.select:before { background:#F0FEE9;  }

}

@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
	.themesaller-forms .select .arrow:after,
	.themesaller-forms .select .arrow:before { display:block; }
}

/* @Fix old of versions android + ios
------------------------------------------------------------- */
@media screen and (-webkit-min-device-pixel-ratio:0) {
        .themesaller-forms .option,
        .themesaller-forms .rating,
        .themesaller-forms .switch,
		.themesaller-forms .captcode { -webkit-animation: bugfix infinite 1s; }
        @-webkit-keyframes bugfix {
            from { padding: 0;  }
            to { padding: 0; }
        }
		.themesaller-forms .switch { margin-right:10px;  margin-bottom:5px; }
		.themesaller-forms .option { margin-right:15px; }
}

/* @responsiveness for tablets + smart mobile
-------------------------------------------------- */
@media (max-width: 600px) {
	.themesaller-forms .frm-row{ margin:0;  }
	.themesaller-forms .frm-row .colm{  width: 100%; float:none; padding:0; }
	.themesaller-forms .bdl { border-left:0;  }
	.themesaller-forms .bdr { border-right:0; }
	.themesaller-forms .align-right{ text-align: left; }
}
