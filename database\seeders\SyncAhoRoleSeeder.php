<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Services\PermissionService;

class SyncAhoRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🔧 Correction complète du système de permissions...');

        $permissionService = new PermissionService();

        // 1. <PERSON><PERSON><PERSON> toutes les associations permission-menu manquantes
        $this->command->info('📋 Création des associations permission-menu...');
        $permissionService->createPermissionMenusForAllMenus();

        // 2. C<PERSON>er le rôle "aho" s'il n'existe pas
        $ahoRole = Role::firstOrCreate(['role_name' => 'aho']);
        $this->command->info("✅ Rôle 'aho' vérifié/créé (ID: {$ahoRole->id})");

        // 3. Synchroniser toutes les permissions des rôles
        $this->command->info('👥 Synchronisation des permissions de tous les rôles...');
        $permissionService->syncAllRolePermissions();

        // 4. Vérifier les résultats pour le rôle aho
        $permissionMenus = $ahoRole->permissionMenus()->with(['menu', 'permission'])->get();
        $this->command->info("📋 Permissions assignées au rôle 'aho': {$permissionMenus->count()}");

        if ($permissionMenus->isEmpty()) {
            $this->command->error("❌ PROBLÈME: Le rôle 'aho' n'a aucune permission!");

            // Correction manuelle si la configuration ne fonctionne pas
            $this->assignManualPermissions($ahoRole);
        } else {
            $groupedPermissions = $permissionMenus->groupBy('menu.url');
            foreach ($groupedPermissions as $url => $permissions) {
                $permissionNames = $permissions->pluck('permission.permission_name')->join(', ');
                $this->command->info("  - {$url}: {$permissionNames}");
            }
        }

        $this->command->info('🎉 Correction terminée avec succès!');
    }

    /**
     * Assigne manuellement les permissions au rôle aho si la configuration échoue
     */
    private function assignManualPermissions(Role $ahoRole)
    {
        $this->command->info('🔧 Attribution manuelle des permissions...');

        $urls = ['/dashboard', '/activites', '/reforme', '/typereforme', '/indicateurs'];
        $permissions = ['Créer', 'Lire', 'Modifier', 'Supprimer'];

        $permissionMenuIds = [];

        foreach ($urls as $url) {
            $menu = \App\Models\Menu::where('url', $url)->first();
            if (!$menu) {
                $this->command->warn("Menu non trouvé: {$url}");
                continue;
            }

            foreach ($permissions as $permissionName) {
                $permission = \App\Models\Permission::where('permission_name', $permissionName)->first();
                if (!$permission) {
                    $this->command->warn("Permission non trouvée: {$permissionName}");
                    continue;
                }

                $permissionMenu = \App\Models\PermissionMenu::where('menu_id', $menu->id)
                    ->where('permission_id', $permission->id)
                    ->first();

                if ($permissionMenu) {
                    $permissionMenuIds[] = $permissionMenu->id;
                    $this->command->info("  ✅ {$url} - {$permissionName}");
                }
            }
        }

        if (!empty($permissionMenuIds)) {
            $ahoRole->permissionMenus()->sync($permissionMenuIds);
            $this->command->info("✅ {count($permissionMenuIds)} permissions assignées manuellement");
        }
    }
}
