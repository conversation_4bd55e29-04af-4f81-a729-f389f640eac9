<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class UserSession extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'user_sessions';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'session_id',
        'ip_address',
        'user_agent',
        'login_at',
        'logout_at',
        'last_activity',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'login_at' => 'datetime',
        'logout_at' => 'datetime',
        'last_activity' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [];

    /**
     * Get the user that owns the session.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include active sessions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive sessions.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Scope a query to only include recent sessions (within last 30 minutes).
     */
    public function scopeRecent($query, $minutes = 30)
    {
        return $query->where('last_activity', '>=', Carbon::now()->subMinutes($minutes));
    }

    /**
     * Check if the session is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the session is recent (within last 30 minutes).
     */
    public function isRecent($minutes = 30): bool
    {
        return $this->last_activity && $this->last_activity->diffInMinutes(Carbon::now()) <= $minutes;
    }

    /**
     * Get the duration of the session.
     */
    public function getDuration(): ?int
    {
        if (!$this->login_at) {
            return null;
        }

        $endTime = $this->logout_at ?: $this->last_activity ?: Carbon::now();
        return $this->login_at->diffInMinutes($endTime);
    }

    /**
     * Mark the session as inactive.
     */
    public function markAsInactive(): bool
    {
        return $this->update([
            'status' => 'inactive',
            'logout_at' => Carbon::now(),
        ]);
    }

    /**
     * Update the last activity timestamp.
     */
    public function updateActivity(): bool
    {
        return $this->update([
            'last_activity' => Carbon::now(),
        ]);
    }

    /**
     * Get sessions for a specific user.
     */
    public static function forUser($userId)
    {
        return static::where('user_id', $userId);
    }

    /**
     * Get active sessions count for a user.
     */
    public static function activeSessionsCount($userId): int
    {
        return static::where('user_id', $userId)->active()->count();
    }

    /**
     * Clean up old sessions (older than specified days).
     */
    public static function cleanupOldSessions($days = 30): int
    {
        $cutoffDate = Carbon::now()->subDays($days);
        
        return static::where('created_at', '<', $cutoffDate)
                    ->where('status', 'inactive')
                    ->delete();
    }

    /**
     * Get connected users count (active sessions in last X minutes).
     */
    public static function getConnectedUsersCount($minutes = 30): int
    {
        return static::active()
                    ->recent($minutes)
                    ->distinct('user_id')
                    ->count('user_id');
    }

    /**
     * Get recent sessions with user information.
     */
    public static function getRecentWithUsers($limit = 10)
    {
        return static::with('user.personne')
                    ->orderBy('last_activity', 'desc')
                    ->limit($limit)
                    ->get();
    }
}
