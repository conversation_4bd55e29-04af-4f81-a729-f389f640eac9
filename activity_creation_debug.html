<!DOCTYPE html>
<html>
<head>
    <title>🔧 Debug - Création d'Activités</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #007bff; }
        .warning { color: #ffc107; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-box { background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0; }
        .fix-box { background-color: #d1edff; padding: 15px; border-left: 4px solid #007bff; margin: 10px 0; }
        .code-block { background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }
        .btn-primary { background-color: #007bff; }
        .btn-success { background-color: #28a745; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .comparison-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .comparison-table th { background-color: #f8f9fa; }
        .step-list { list-style-type: none; padding: 0; counter-reset: step-counter; }
        .step-list li { padding: 8px 0; counter-increment: step-counter; }
        .step-list li:before { content: counter(step-counter) ". "; color: #007bff; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug - Problème de Création d'Activités</h1>
        
        <div class="test-section">
            <h2>🎯 Problèmes Identifiés et Corrections Appliquées</h2>
            <p class="info">Diagnostic complet du problème de création d'activités avec corrections appliquées.</p>
        </div>
        
        <div class="test-section">
            <h2>🔍 Problèmes Identifiés</h2>
            
            <div class="debug-box">
                <h4>⚠️ Problème 1 : Transmission des Données dans create()</h4>
                <p>La méthode <code>create()</code> utilisait <code>->with(compact('reformes', 'structures'))</code> incorrectement.</p>
                
                <h5>Code Problématique :</h5>
                <div class="code-block">
public function create()
{
    $reformes = Reforme::all();
    $structures = \DB::table('reformes_structure as rs')...
    
    // ❌ PROBLÈME : with() ne fonctionne pas comme attendu
    return redirect()->route('activites.index')->with(compact('reformes', 'structures'));
}
                </div>
                
                <h5>Conséquence :</h5>
                <p>Les variables <code>$reformes</code> et <code>$structures</code> n'étaient pas disponibles dans la vue, causant des erreurs dans les boucles <code>@foreach</code>.</p>
            </div>
            
            <div class="debug-box">
                <h4>⚠️ Problème 2 : Gestion des Variables Non Définies</h4>
                <p>La vue ne vérifiait pas l'existence des variables avant de les utiliser.</p>
                
                <h5>Code Problématique :</h5>
                <div class="code-block">
@foreach($reformes as $reforme)  {{-- ❌ Erreur si $reformes non défini --}}
    <option value="{{ $reforme->id }}">{{ $reforme->titre }}</option>
@endforeach
                </div>
            </div>
            
            <div class="debug-box">
                <h4>⚠️ Problème 3 : Logs de Debug Insuffisants</h4>
                <p>Manque de logs détaillés pour diagnostiquer les problèmes de validation et de sauvegarde.</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>✅ Corrections Appliquées</h2>
            
            <div class="fix-box">
                <h4>✅ Correction 1 : Simplification de create()</h4>
                <p><strong>Fichier :</strong> <code>app/Http/Controllers/ActivitesreformesController.php</code></p>
                
                <h5>Nouveau Code :</h5>
                <div class="code-block">
public function create()
{
    // Rediriger vers l'index car la création se fait via modal
    // Les données sont déjà chargées dans la méthode index()
    return redirect()->route('activites.index');
}
                </div>
                
                <p><strong>Avantage :</strong> Les données sont gérées uniquement dans <code>index()</code>, évitant les conflits.</p>
            </div>
            
            <div class="fix-box">
                <h4>✅ Correction 2 : Protection des Variables dans la Vue</h4>
                <p><strong>Fichier :</strong> <code>resources/views/activitesreformes.blade.php</code></p>
                
                <h5>Nouveau Code :</h5>
                <div class="code-block">
<select name="reforme_id" class="form-control" required>
    <option value="">Sélectionner une réforme</option>
    @if(isset($reformes) && $reformes->count() > 0)
        @foreach($reformes as $reforme)
            <option value="{{ $reforme->id }}">{{ $reforme->titre }}</option>
        @endforeach
    @else
        <option value="" disabled>Aucune réforme disponible</option>
    @endif
</select>
                </div>
                
                <p><strong>Avantage :</strong> Évite les erreurs si les variables ne sont pas définies.</p>
            </div>
            
            <div class="fix-box">
                <h4>✅ Correction 3 : Logs de Debug Détaillés</h4>
                <p><strong>Fichier :</strong> <code>app/Http/Controllers/ActivitesreformesController.php</code></p>
                
                <h5>Logs Ajoutés :</h5>
                <div class="code-block">
// Avant validation
\Log::info('=== DÉBUT CRÉATION ACTIVITÉ ===');
\Log::info('Données brutes reçues:', $request->all());
\Log::info('Méthode HTTP:', $request->method());

// Après validation
\Log::info('Validation réussie - Données après validation:', $request->validated());

// Après création
\Log::info('Activité créée avec ID: ' . $activite->id . ' et statut: ' . $activite->statut);

// Vérification base de données
$activiteVerif = Activitesreformes::find($activite->id);
if ($activiteVerif) {
    \Log::info('Vérification base de données réussie');
} else {
    \Log::error('ERREUR: Activité non trouvée en base après création!');
}
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧪 Procédure de Test</h2>
            
            <h3>Test 1 - Vérification des Données :</h3>
            <ol class="step-list">
                <li><strong>Accédez à :</strong> <code>/activites</code></li>
                <li><strong>Vérifiez :</strong> Les listes déroulantes sont remplies</li>
                <li><strong>Inspectez :</strong> Console navigateur pour erreurs JavaScript</li>
                <li><strong>Confirmez :</strong> Modal s'ouvre correctement</li>
            </ol>
            
            <h3>Test 2 - Création d'Activité :</h3>
            <ol class="step-list">
                <li><strong>Cliquez :</strong> "Ajouter une activité"</li>
                <li><strong>Remplissez :</strong> Tous les champs requis</li>
                <li><strong>Soumettez :</strong> Le formulaire</li>
                <li><strong>Vérifiez :</strong> Message de succès ou d'erreur</li>
            </ol>
            
            <h3>Test 3 - Vérification Logs :</h3>
            <ol class="step-list">
                <li><strong>Consultez :</strong> <code>storage/logs/laravel.log</code></li>
                <li><strong>Recherchez :</strong> "=== DÉBUT CRÉATION ACTIVITÉ ==="</li>
                <li><strong>Analysez :</strong> Les données reçues et les erreurs</li>
                <li><strong>Vérifiez :</strong> La création en base de données</li>
            </ol>
            
            <h3>Test 4 - Vérification Base de Données :</h3>
            <div class="code-block">
-- Vérifier les activités créées récemment
SELECT id, libelle, statut, reforme_id, created_at 
FROM activites_reformes 
WHERE created_at >= CURDATE() 
ORDER BY created_at DESC 
LIMIT 10;

-- Vérifier les réformes disponibles
SELECT id, titre FROM reformes ORDER BY titre;

-- Vérifier les structures disponibles
SELECT rs.id, s.lib_court, s.lib_long 
FROM reformes_structure rs 
JOIN structure s ON rs.structure_id = s.id;
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 Diagnostic Avancé</h2>
            
            <h3>Si le Problème Persiste :</h3>
            
            <h4>1. Vérification des Routes :</h4>
            <div class="code-block">
# Lister toutes les routes
php artisan route:list | grep activites

# Vérifier la route store
php artisan route:list | grep "activites.*store"
            </div>
            
            <h4>2. Test Manuel de la Route :</h4>
            <div class="code-block">
# Test avec curl
curl -X POST http://localhost/activites \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "reforme_id=1&libelle=Test&date_debut=2024-01-01&date_fin_prevue=2024-12-31&poids=50&structure_responsable=1"
            </div>
            
            <h4>3. Vérification des Permissions :</h4>
            <div class="code-block">
# Vérifier les permissions de storage/logs
ls -la storage/logs/

# Vérifier les permissions de la base de données
# (dépend de votre configuration)
            </div>
            
            <h4>4. Test de Validation :</h4>
            <div class="code-block">
// Dans Tinker (php artisan tinker)
$request = new \Illuminate\Http\Request();
$request->merge([
    'reforme_id' => 1,
    'libelle' => 'Test Activité',
    'date_debut' => '2024-01-01',
    'date_fin_prevue' => '2024-12-31',
    'poids' => 50,
    'structure_responsable' => 1
]);

$validator = \Validator::make($request->all(), [
    'reforme_id' => 'required|exists:reformes,id',
    'libelle' => 'required|string|max:255',
    'date_debut' => 'required|date',
    'date_fin_prevue' => 'required|date|after:date_debut',
    'poids' => 'required|integer|min:1|max:100',
    'structure_responsable' => 'required|integer|min:1|exists:reformes_structure,id',
]);

if ($validator->fails()) {
    dd($validator->errors());
} else {
    echo "Validation réussie!";
}
            </div>
        </div>
        
        <div class="test-section">
            <h2>📊 Checklist de Vérification</h2>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Élément</th>
                        <th>Vérification</th>
                        <th>Commande/Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Route activites.store</td>
                        <td>Existe et pointe vers ActivitesreformesController@store</td>
                        <td><code>php artisan route:list | grep store</code></td>
                    </tr>
                    <tr>
                        <td>Données $reformes</td>
                        <td>Disponibles dans la vue</td>
                        <td>Inspecter source HTML du select</td>
                    </tr>
                    <tr>
                        <td>Données $structures</td>
                        <td>Disponibles dans la vue</td>
                        <td>Inspecter source HTML du select</td>
                    </tr>
                    <tr>
                        <td>Validation JavaScript</td>
                        <td>Pas d'erreurs console</td>
                        <td>F12 → Console</td>
                    </tr>
                    <tr>
                        <td>Soumission formulaire</td>
                        <td>POST vers /activites</td>
                        <td>F12 → Network lors de la soumission</td>
                    </tr>
                    <tr>
                        <td>Logs Laravel</td>
                        <td>Messages de debug visibles</td>
                        <td><code>tail -f storage/logs/laravel.log</code></td>
                    </tr>
                    <tr>
                        <td>Base de données</td>
                        <td>Activité créée</td>
                        <td>Requête SQL SELECT</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="test-section">
            <h2>🎯 Actions de Test</h2>
            
            <div style="text-align: center; margin-top: 20px;">
                <a href="/activites" class="btn btn-primary" target="_blank">
                    🧪 Tester Page Activités
                </a>
                <a href="/activites/create" class="btn btn-success" target="_blank">
                    ➕ Tester Route Create
                </a>
                <a href="javascript:console.log('Vérifiez les logs Laravel')" class="btn btn-warning">
                    📋 Vérifier Logs
                </a>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🎉 Résultat Attendu</h2>
            <p class="success">Après ces corrections, la création d'activités devrait fonctionner correctement !</p>
            
            <h3>Fonctionnalités Restaurées :</h3>
            <ul>
                <li>✅ <strong>Chargement de la page :</strong> Pas d'erreurs de variables non définies</li>
                <li>✅ <strong>Formulaire modal :</strong> Listes déroulantes remplies</li>
                <li>✅ <strong>Validation :</strong> Erreurs claires si champs manquants</li>
                <li>✅ <strong>Sauvegarde :</strong> Activités créées avec statut 'C'</li>
                <li>✅ <strong>Feedback :</strong> Messages de succès/erreur appropriés</li>
                <li>✅ <strong>Logs :</strong> Diagnostic détaillé des problèmes</li>
            </ul>
            
            <h3>Workflow de Création Corrigé :</h3>
            <ol>
                <li>Utilisateur accède à <code>/activites</code></li>
                <li>Page se charge avec données <code>$reformes</code> et <code>$structures</code></li>
                <li>Utilisateur clique "Ajouter une activité"</li>
                <li>Modal s'ouvre avec formulaire pré-rempli</li>
                <li>Utilisateur remplit et soumet le formulaire</li>
                <li>Validation côté serveur</li>
                <li>Création en base avec statut 'C' automatique</li>
                <li>Redirection avec message de succès</li>
            </ol>
        </div>
    </div>
</body>
</html>
