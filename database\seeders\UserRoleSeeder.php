<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UserRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Vider la table user_role d'abord
        DB::table('user_role')->truncate();

        // Assigner les rôles aux utilisateurs existants
        DB::table('user_role')->insert([
            [
                'id_user' => 1,
                'role_id' => 1, // Administrateur
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id_user' => 2,
                'role_id' => 2, // Gestionnaire
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id_user' => 3,
                'role_id' => 3, // Utilisateur
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
