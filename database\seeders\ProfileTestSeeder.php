<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Personne;
use App\Models\Role;

class ProfileTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un rôle de test s'il n'existe pas
        $role = Role::firstOrCreate([
            'nom' => 'Utilisateur'
        ], [
            'description' => 'Utilisateur standard'
        ]);

        // Créer une personne de test
        $personne = Personne::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'nom' => 'Dupont',
            'prenom' => 'Jean',
            'fonction' => 'Développeur',
            'tel' => '+225 0123456789'
        ]);

        // Créer un utilisateur de test
        $user = User::firstOrCreate([
            'personne_id' => $personne->id
        ], [
            'pwd' => bcrypt('password'),
            'status' => 'A'
        ]);

        // Attacher le rôle à l'utilisateur
        if (!$user->roles()->where('role_id', $role->id)->exists()) {
            $user->roles()->attach($role->id);
        }

        $this->command->info('Données de test pour le profil créées avec succès !');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Mot de passe: password');
    }
} 