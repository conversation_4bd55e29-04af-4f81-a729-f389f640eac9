<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class EvolutionIndicateur extends Model
{
    protected $table = 'evolution_indicateurs';

    protected $fillable = [
        'reforme_indicateur_id',
        'date_evolution',
        'valeur'
    ];

    protected $casts = [
        'date_evolution' => 'date',
        'valeur' => 'decimal:2'
    ];

    // Clé primaire composite
    protected $primaryKey = ['reforme_indicateur_id', 'date_evolution'];
    public $incrementing = false;

    /**
     * Relation avec la table pivot reforme_indicateur
     */
    public function reformeIndicateur()
    {
        return $this->belongsTo(ReformeIndicateur::class, 'reforme_indicateur_id');
    }

    /**
     * Accesseur pour obtenir la réforme via la relation pivot
     */
    public function getReformeAttribute()
    {
        return $this->reformeIndicateur?->reforme;
    }

    /**
     * Accesseur pour obtenir l'indicateur via la relation pivot
     */
    public function getIndicateurAttribute()
    {
        return $this->reformeIndicateur?->indicateur;
    }

    /**
     * Scope pour filtrer par réforme
     */
    public function scopeForReforme($query, $reformeId)
    {
        return $query->whereHas('reformeIndicateur', function($q) use ($reformeId) {
            $q->where('reforme_id', $reformeId);
        });
    }

    /**
     * Scope pour filtrer par indicateur
     */
    public function scopeForIndicateur($query, $indicateurId)
    {
        return $query->whereHas('reformeIndicateur', function($q) use ($indicateurId) {
            $q->where('indicateur_id', $indicateurId);
        });
    }

    /**
     * Scope pour filtrer par période
     */
    public function scopeForPeriod($query, $dateDebut, $dateFin)
    {
        return $query->whereBetween('date_evolution', [$dateDebut, $dateFin]);
    }

    /**
     * Scope pour obtenir les dernières évolutions
     */
    public function scopeLatest($query, $limit = 10)
    {
        return $query->orderBy('date_evolution', 'desc')->limit($limit);
    }

    /**
     * Accesseur pour formater la date
     */
    public function getDateFormatteeAttribute()
    {
        return $this->date_evolution ? $this->date_evolution->format('d/m/Y') : '';
    }

    /**
     * Accesseur pour formater la valeur avec l'unité
     */
    public function getValeurFormatteeAttribute()
    {
        $unite = $this->indicateur?->unite ?? '';
        return number_format($this->valeur, 2, ',', ' ') . ' ' . $unite;
    }

    /**
     * Accesseur pour formater la date
     */
    public function getDateFormateeAttribute()
    {
        return \Carbon\Carbon::parse($this->date_evolution)->format('d/m/Y');
    }

    /**
     * Méthode statique pour créer une nouvelle évolution
     */
    public static function creerEvolution($reformeId, $indicateurId, $date, $valeur)
    {
        // Trouver ou créer la relation reforme-indicateur
        $reformeIndicateur = ReformeIndicateur::firstOrCreate([
            'reforme_id' => $reformeId,
            'indicateur_id' => $indicateurId
        ]);

        // Créer ou mettre à jour l'évolution
        return self::updateOrCreate([
            'reforme_indicateur_id' => $reformeIndicateur->id,
            'date_evolution' => $date
        ], [
            'valeur' => $valeur
        ]);
    }

    /**
     * Obtenir l'évolution précédente pour calculer la tendance
     */
    public function getEvolutionPrecedente()
    {
        return self::where('reforme_indicateur_id', $this->reforme_indicateur_id)
                   ->where('date_evolution', '<', $this->date_evolution)
                   ->orderBy('date_evolution', 'desc')
                   ->first();
    }

    /**
     * Calculer la tendance par rapport à l'évolution précédente
     */
    public function getTendanceAttribute()
    {
        $precedente = $this->getEvolutionPrecedente();
        
        if (!$precedente) {
            return 'stable'; // Première valeur
        }

        $difference = $this->valeur - $precedente->valeur;
        
        if ($difference > 0) {
            return 'hausse';
        } elseif ($difference < 0) {
            return 'baisse';
        } else {
            return 'stable';
        }
    }

    /**
     * Obtenir l'icône de tendance
     */
    public function getIconeTendanceAttribute()
    {
        switch ($this->tendance) {
            case 'hausse':
                return '<i class="fa fa-arrow-up text-success"></i>';
            case 'baisse':
                return '<i class="fa fa-arrow-down text-danger"></i>';
            default:
                return '<i class="fa fa-minus text-muted"></i>';
        }
    }

    /**
     * Obtenir le pourcentage de variation
     */
    public function getPourcentageVariationAttribute()
    {
        $precedente = $this->getEvolutionPrecedente();
        
        if (!$precedente || $precedente->valeur == 0) {
            return 0;
        }

        return round((($this->valeur - $precedente->valeur) / $precedente->valeur) * 100, 2);
    }

    /**
     * Méthode statique pour obtenir les statistiques d'un indicateur
     */
    public static function getStatistiquesIndicateur($reformeId, $indicateurId)
    {
        $evolutions = self::whereHas('reformeIndicateur', function($q) use ($reformeId, $indicateurId) {
            $q->where('reforme_id', $reformeId)
              ->where('indicateur_id', $indicateurId);
        })->orderBy('date_evolution')->get();

        if ($evolutions->isEmpty()) {
            return null;
        }

        return [
            'valeur_initiale' => $evolutions->first()->valeur,
            'valeur_actuelle' => $evolutions->last()->valeur,
            'valeur_min' => $evolutions->min('valeur'),
            'valeur_max' => $evolutions->max('valeur'),
            'moyenne' => $evolutions->avg('valeur'),
            'nombre_mesures' => $evolutions->count(),
            'periode_debut' => $evolutions->first()->date_evolution,
            'periode_fin' => $evolutions->last()->date_evolution,
            'tendance_globale' => $evolutions->last()->tendance
        ];
    }
}
