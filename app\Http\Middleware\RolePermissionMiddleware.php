<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use App\Services\PermissionService;

class RolePermissionMiddleware
{
    protected $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$parameters): Response
    {
        // Vérifier si l'utilisateur est connecté
        if (!Auth::check()) {
            $errorMessage = config('permissions.error_messages.login_required',
                'Vous devez être connecté pour accéder à cette page.');

            Log::warning('Tentative d\'accès non authentifié', [
                'url' => $request->url(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return redirect()->route('login')->with('error', $errorMessage);
        }

        $user = Auth::user();

        // Log de débogage détaillé
        Log::info('Vérification des permissions middleware', [
            'user_id' => $user->id,
            'user_roles' => $user->roles->pluck('role_name')->toArray(),
            'url' => $request->url(),
            'route_name' => $request->route() ? $request->route()->getName() : null,
            'action' => $request->route() ? $request->route()->getActionName() : null,
            'method' => $request->method(),
            'parameters' => $parameters
        ]);

        // Si aucun paramètre n'est fourni, laisser passer (juste vérification d'authentification)
        if (empty($parameters)) {
            return $next($request);
        }

        // Analyser les paramètres
        foreach ($parameters as $parameter) {
            if ($this->checkParameter($user, $parameter, $request)) {
                return $next($request);
            }
        }

        // Si aucune condition n'est remplie, refuser l'accès
        return $this->accessDenied($request, $user, $parameters);
    }

    /**
     * Vérifie un paramètre de permission
     */
    private function checkParameter($user, $parameter, Request $request)
    {
        // Format: role:nom_role ou permission:nom_permission ou menu:id_menu ou crud:url
        if (strpos($parameter, ':') !== false) {
            [$type, $value] = explode(':', $parameter, 2);

            switch ($type) {
                case 'role':
                    return $user->hasRole($value);

                case 'permission':
                    return $user->hasPermissionByName($value);

                case 'menu':
                    return $user->canAccessMenu((int)$value);

                case 'any_role':
                    $roles = explode(',', $value);
                    return $user->hasAnyRole($roles);

                case 'all_roles':
                    $roles = explode(',', $value);
                    return $user->hasAllRoles($roles);

                case 'crud':
                    // Nouveau: Vérification CRUD granulaire
                    return $this->checkCrudPermission($user, $value, $request);

                case 'url':
                    // Vérification par URL avec action automatique
                    return $this->checkUrlPermission($user, $value, $request);
            }
        }

        // Si pas de format spécial, considérer comme un nom de rôle
        return $user->hasRole($parameter);
    }

    /**
     * Vérifier les permissions CRUD granulaires
     */
    private function checkCrudPermission($user, $menuUrl, Request $request)
    {
        // Déterminer l'action CRUD basée sur la méthode HTTP et l'action de la route
        $action = $this->determineCrudAction($request);

        if (!$action) {
            Log::warning('Action CRUD non déterminée', [
                'route' => $request->route() ? $request->route()->getName() : null,
                'method' => $request->method(),
                'action_name' => $request->route() ? $request->route()->getActionName() : null
            ]);
            return false;
        }

        // Vérifier si l'utilisateur a la permission CRUD pour ce menu
        return $this->permissionService->userHasPermissionForUrl($user, $menuUrl, $action);
    }

    /**
     * Vérifier les permissions par URL avec détection automatique de l'action
     */
    private function checkUrlPermission($user, $menuUrl, Request $request)
    {
        // Utiliser le service de permissions pour vérifier l'accès à l'URL
        return $this->permissionService->userCanAccessUrl($user, $menuUrl, $request);
    }

    /**
     * Déterminer l'action CRUD basée sur la route et la méthode HTTP
     */
    private function determineCrudAction(Request $request)
    {
        if (!$request->route()) {
            return null;
        }

        $routeName = $request->route()->getName();
        $method = $request->method();
        $actionName = $request->route()->getActionName();

        // Extraire l'action du nom de la méthode du contrôleur
        if (strpos($actionName, '@') !== false) {
            $controllerAction = explode('@', $actionName)[1];
        } else {
            // Pour les contrôleurs invokables ou les closures
            $controllerAction = '';
        }

        // Mapping des actions vers les permissions CRUD
        $actionMappings = config('permissions.url_actions', []);

        // Vérifier d'abord par l'action du contrôleur
        if ($controllerAction && isset($actionMappings[$controllerAction])) {
            return $actionMappings[$controllerAction];
        }

        // Mapping standard des actions de contrôleur
        $standardMappings = [
            'index' => 'Lire',
            'show' => 'Lire',
            'create' => 'Créer',
            'store' => 'Créer',
            'edit' => 'Modifier',
            'update' => 'Modifier',
            'destroy' => 'Supprimer',
            'delete' => 'Supprimer',

            // Actions spécifiques aux sous-activités
            'indexSousActivites' => 'Lire',
            'storeSousActivite' => 'Créer',
            'updateSousActivite' => 'Modifier',
            'destroySousActivite' => 'Supprimer',
        ];

        if (isset($standardMappings[$controllerAction])) {
            return $standardMappings[$controllerAction];
        }

        // Fallback sur la méthode HTTP
        switch ($method) {
            case 'GET':
                return 'Lire';
            case 'POST':
                return 'Créer';
            case 'PUT':
            case 'PATCH':
                return 'Modifier';
            case 'DELETE':
                return 'Supprimer';
            default:
                return null;
        }
    }

    /**
     * Gère le refus d'accès
     */
    private function accessDenied(Request $request, $user = null, $parameters = [])
    {
        $errorMessage = config('permissions.error_messages.access_denied',
            'Accès refusé. Vous n\'avez pas les permissions nécessaires.');

        // Log détaillé du refus d'accès
        Log::warning('Accès refusé - Permissions insuffisantes', [
            'user_id' => $user ? $user->id : null,
            'user_roles' => $user ? $user->roles->pluck('role_name')->toArray() : [],
            'url' => $request->url(),
            'route_name' => $request->route() ? $request->route()->getName() : null,
            'method' => $request->method(),
            'parameters' => $parameters,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'message' => $errorMessage,
                'error' => 'access_denied',
                'code' => 403
            ], 403);
        }

        // Redirection intelligente selon le contexte
        $redirectRoute = 'dashboard';

        // Si l'utilisateur n'a pas accès au dashboard, rediriger vers login
        if ($user && !$user->canAccessMenu(1)) { // Assuming dashboard has menu ID 1
            $redirectRoute = 'login';
            $errorMessage = 'Votre compte n\'a pas les permissions nécessaires. Contactez l\'administrateur.';
        }

        return redirect()->route($redirectRoute)->with('error', $errorMessage);
    }
}
