<?php

/**
 * Vérification et création de la table sessions si nécessaire
 */

echo "🔍 VÉRIFICATION DE LA TABLE SESSIONS\n";
echo "====================================\n\n";

try {
    $pdo = new PDO('sqlite:database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Vérifier si la table sessions existe
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='sessions'");
    $tableExists = $stmt->fetch();
    
    if ($tableExists) {
        echo "✅ La table 'sessions' existe déjà\n";
        
        // Vérifier la structure
        $stmt = $pdo->query("PRAGMA table_info(sessions)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "📋 Structure de la table 'sessions' :\n";
        foreach ($columns as $column) {
            echo "  • {$column['name']} ({$column['type']})\n";
        }
        
        // Compter les enregistrements
        $count = $pdo->query("SELECT COUNT(*) FROM sessions")->fetchColumn();
        echo "\n📊 Nombre d'enregistrements : $count\n";
        
    } else {
        echo "❌ La table 'sessions' n'existe pas\n";
        echo "🔧 Création de la table 'sessions'...\n\n";
        
        // Créer la table sessions selon le standard Laravel
        $createTableSql = "
        CREATE TABLE sessions (
            id VARCHAR(255) PRIMARY KEY NOT NULL,
            user_id INTEGER NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            payload LONGTEXT NOT NULL,
            last_activity INTEGER NOT NULL
        )";
        
        $pdo->exec($createTableSql);
        echo "✅ Table 'sessions' créée avec succès !\n";
        
        // Créer un index pour les performances
        $pdo->exec("CREATE INDEX sessions_user_id_index ON sessions (user_id)");
        $pdo->exec("CREATE INDEX sessions_last_activity_index ON sessions (last_activity)");
        echo "✅ Index créés pour optimiser les performances\n";
    }
    
    echo "\n🔧 VÉRIFICATION DE LA CONFIGURATION SESSIONS :\n";
    echo "===============================================\n";
    
    // Vérifier le fichier .env
    $envContent = file_get_contents('.env');
    if (strpos($envContent, 'SESSION_DRIVER=database') !== false) {
        echo "✅ SESSION_DRIVER=database configuré correctement\n";
    } else {
        echo "⚠️  SESSION_DRIVER n'est pas configuré sur 'database'\n";
        echo "🔧 Correction du fichier .env...\n";
        
        // Corriger le .env
        $envContent = preg_replace('/SESSION_DRIVER=.*/', 'SESSION_DRIVER=database', $envContent);
        file_put_contents('.env', $envContent);
        echo "✅ SESSION_DRIVER corrigé dans .env\n";
    }
    
    echo "\n🧪 TEST DE LA TABLE SESSIONS :\n";
    echo "==============================\n";
    
    // Test d'insertion d'une session de test
    $testSessionId = 'test_' . uniqid();
    $testPayload = base64_encode(serialize(['test' => 'data']));
    $currentTime = time();
    
    $insertSql = "INSERT INTO sessions (id, user_id, ip_address, user_agent, payload, last_activity) 
                  VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($insertSql);
    $stmt->execute([
        $testSessionId,
        1, // ID utilisateur test
        '127.0.0.1',
        'Test User Agent',
        $testPayload,
        $currentTime
    ]);
    
    echo "✅ Session de test insérée avec succès\n";
    
    // Vérifier la lecture
    $selectSql = "SELECT * FROM sessions WHERE id = ?";
    $stmt = $pdo->prepare($selectSql);
    $stmt->execute([$testSessionId]);
    $testSession = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testSession) {
        echo "✅ Session de test lue avec succès\n";
        echo "  • ID: {$testSession['id']}\n";
        echo "  • User ID: {$testSession['user_id']}\n";
        echo "  • IP: {$testSession['ip_address']}\n";
        
        // Nettoyer la session de test
        $pdo->prepare("DELETE FROM sessions WHERE id = ?")->execute([$testSessionId]);
        echo "✅ Session de test supprimée\n";
    } else {
        echo "❌ Erreur lors de la lecture de la session de test\n";
    }
    
    echo "\n🎉 VÉRIFICATION TERMINÉE !\n";
    echo "La table 'sessions' est maintenant prête à être utilisée par Laravel.\n";
    
} catch (Exception $e) {
    echo "❌ ERREUR : " . $e->getMessage() . "\n";
    echo "Trace : " . $e->getTraceAsString() . "\n";
}

echo "\n🚀 PROCHAINES ÉTAPES :\n";
echo "======================\n";
echo "1. Redémarrez votre serveur Laravel\n";
echo "2. Videz le cache Laravel : php artisan cache:clear\n";
echo "3. Videz le cache de configuration : php artisan config:clear\n";
echo "4. Testez la connexion à votre application\n";

?>
