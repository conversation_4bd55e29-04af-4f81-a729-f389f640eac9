<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Reforme;
use App\Models\Indicateur;
use App\Models\ReformeIndicateur;
use App\Models\EvolutionIndicateur;
use Carbon\Carbon;

class SuiviIndicateurSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🔄 Création des données de test pour le suivi des indicateurs...');

        // Créer des indicateurs de test s'ils n'existent pas
        $this->createIndicateurs();

        // Créer des réformes de test s'ils n'existent pas
        $this->createReformes();

        // Récupérer les données créées
        $reformes = Reforme::take(3)->get();
        $indicateurs = Indicateur::take(5)->get();

        if ($reformes->isEmpty() || $indicateurs->isEmpty()) {
            $this->command->error('❌ Impossible de créer ou récupérer les données de base.');
            return;
        }

        foreach ($reformes as $reforme) {
            // Associer 2-3 indicateurs à chaque réforme
            $indicateursReforme = $indicateurs->random(rand(2, 3));

            foreach ($indicateursReforme as $indicateur) {
                // Créer la relation reforme-indicateur
                $reformeIndicateur = ReformeIndicateur::firstOrCreate([
                    'reforme_id' => $reforme->id,
                    'indicateur_id' => $indicateur->id,
                ]);

                // Créer des évolutions sur les 6 derniers mois
                $dateDebut = Carbon::now()->subMonths(6);
                $valeurInitiale = rand(50, 200);

                for ($i = 0; $i < 6; $i++) {
                    $date = $dateDebut->copy()->addMonths($i);

                    // Calculer une progression réaliste
                    $variation = rand(-10, 15); // Variation entre -10% et +15%
                    $valeurInitiale = $valeurInitiale * (1 + $variation / 100);
                    $valeurInitiale = max(0, $valeurInitiale); // Éviter les valeurs négatives

                    EvolutionIndicateur::create([
                        'reforme_indicateur_id' => $reformeIndicateur->id,
                        'date_evolution' => $date->format('Y-m-d'),
                        'valeur' => round($valeurInitiale, 2),
                    ]);
                }
            }
        }

        $this->command->info('Données de test pour le suivi des indicateurs créées avec succès !');
    }

    /**
     * Créer des indicateurs de test
     */
    private function createIndicateurs()
    {
        $indicateurs = [
            ['libelle' => 'Taux de satisfaction citoyenne', 'unite' => '%'],
            ['libelle' => 'Délai moyen de traitement', 'unite' => 'jours'],
            ['libelle' => 'Nombre de bénéficiaires', 'unite' => 'personnes'],
            ['libelle' => 'Budget utilisé', 'unite' => '%'],
            ['libelle' => 'Taux de conformité', 'unite' => '%'],
        ];

        foreach ($indicateurs as $data) {
            Indicateur::firstOrCreate(['libelle' => $data['libelle']], $data);
        }

        $this->command->info('📊 Indicateurs créés/vérifiés');
    }

    /**
     * Créer des réformes de test
     */
    private function createReformes()
    {
        $reformes = [
            [
                'titre' => 'Réforme Administrative',
                'objectifs' => 'Modernisation des services administratifs',
                'date_debut' => now()->subMonths(6),
                'date_fin_prevue' => now()->addMonths(6),
                'type_reforme' => 1,
                'created_by' => 1
            ],
            [
                'titre' => 'Réforme Éducative',
                'objectifs' => 'Amélioration du système éducatif',
                'date_debut' => now()->subMonths(12),
                'date_fin_prevue' => now()->addMonths(3),
                'type_reforme' => 1,
                'created_by' => 1
            ],
            [
                'titre' => 'Réforme Sanitaire',
                'objectifs' => 'Renforcement du système de santé',
                'date_debut' => now()->subMonths(8),
                'date_fin_prevue' => now()->addMonths(4),
                'type_reforme' => 1,
                'created_by' => 1
            ]
        ];

        foreach ($reformes as $data) {
            Reforme::firstOrCreate(['titre' => $data['titre']], $data);
        }

        $this->command->info('🏛️ Réformes créées/vérifiées');
    }


}
