<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer des types de réforme
        DB::table('type_reforme')->insert([
            [
                'id' => 1,
                'lib' => 'Réforme Administrative',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 2,
                'lib' => 'Réforme Économique',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Créer des structures
        DB::table('structure')->insert([
            [
                'id' => 1,
                'lib_court' => 'DG',
                'lib_long' => 'Direction Générale',
                'responsable' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 2,
                'lib_court' => 'SI',
                'lib_long' => 'Service Informatique',
                'responsable' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Créer des réformes
        DB::table('reformes')->insert([
            [
                'id' => 1,
                'libelle' => 'Modernisation des Services Publics',
                'description' => 'Amélioration de l\'efficacité des services publics',
                'date_debut' => '2025-01-01',
                'date_fin' => '2025-12-31',
                'type_reforme_id' => 1,
                'statut' => 'En cours',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 2,
                'libelle' => 'Digitalisation Administrative',
                'description' => 'Transformation numérique des processus administratifs',
                'date_debut' => '2025-02-01',
                'date_fin' => '2025-11-30',
                'type_reforme_id' => 1,
                'statut' => 'Planifié',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        // Créer des activités
        DB::table('activites_reformes')->insert([
            [
                'id' => 1,
                'libelle' => 'Analyse des processus existants',
                'description' => 'Étude approfondie des processus actuels',
                'date_debut' => '2025-01-15',
                'date_fin' => '2025-03-15',
                'poids' => 25,
                'reforme_id' => 1,
                'statut' => 'En cours',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 2,
                'libelle' => 'Formation du personnel',
                'description' => 'Formation des agents aux nouveaux processus',
                'date_debut' => '2025-03-01',
                'date_fin' => '2025-05-01',
                'poids' => 30,
                'reforme_id' => 1,
                'statut' => 'Planifié',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
