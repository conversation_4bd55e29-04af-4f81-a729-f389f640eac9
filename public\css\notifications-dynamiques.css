/* Styles pour les notifications dynamiques */

.notification-item {
    transition: all 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.non-lue {
    background-color: #fff3cd;
    border-left: 3px solid #ffc107;
}

.notification-item.lue {
    background-color: #ffffff;
    border-left: 3px solid #6c757d;
}

.notification-item.non-lue .notification-icon i {
    color: #ffc107;
}

.notification-item.lue .notification-icon i {
    color: #6c757d;
}

.notification-item a {
    text-decoration: none;
    color: inherit;
    display: block;
    padding: 10px;
}

.notification-item a:hover {
    text-decoration: none;
    color: inherit;
}

#marquerToutesLues {
    margin-left: 10px;
    font-size: 12px;
    padding: 5px 10px;
}

#notificationCount {
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 10px;
    position: absolute;
    top: -5px;
    right: -5px;
    min-width: 18px;
    text-align: center;
}

.notification-single-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.notification-single-top h1 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

.notification-menu {
    max-height: 300px;
    overflow-y: auto;
}

.notification-menu li {
    border-bottom: 1px solid #f0f0f0;
}

.notification-menu li:last-child {
    border-bottom: none;
}

.notification-content {
    padding: 10px 20px;
}

.notification-date {
    font-size: 11px;
    color: #999;
    display: block;
    margin-bottom: 5px;
}

.notification-content p {
    margin: 0;
    font-size: 13px;
    line-height: 1.4;
}

.notification-view {
    padding: 10px 20px;
    border-top: 1px solid #eee;
    text-align: center;
}

.notification-view a {
    color: #007bff;
    text-decoration: none;
    font-size: 12px;
}

.notification-view a:hover {
    text-decoration: underline;
}

/* Styles pour les icônes de la barre supérieure */
.header-right-info {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 24px;
    min-height: 60px;
}

.header-right-menu {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 18px;
    margin: 0;
    padding: 0;
}

.header-right-menu .nav-item {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
}

.header-icon-item .nav-link,
.logout-btn {
    background: none;
    border: none;
    color: #fff;
    padding: 0 10px;
    border-radius: 50%;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 38px;
    height: 38px;
    font-size: 20px;
    box-shadow: none;
}

.header-icon-item .nav-link:hover,
.logout-btn:hover {
    background: rgba(255,255,255,0.15);
    color: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.header-icon-item .nav-link i,
.logout-btn i,
#notificationDropdown i {
    color: #fff !important;
    font-size: 20px !important;
    vertical-align: middle;
}

#notificationDropdown {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 38px;
    height: 38px;
    padding: 0 10px;
    border-radius: 50%;
}

#notificationCount {
    position: absolute;
    top: 4px;
    right: 6px;
    background: #dc3545;
    color: #fff;
    border-radius: 50%;
    font-size: 11px;
    min-width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    font-weight: bold;
    box-shadow: 0 1px 4px rgba(0,0,0,0.08);
}

@media (max-width: 768px) {
    .header-right-info {
        padding-right: 8px;
    }
    .header-right-menu {
        gap: 10px;
    }
    .header-icon-item .nav-link,
    .logout-btn,
    #notificationDropdown {
        min-width: 32px;
        height: 32px;
        font-size: 16px;
        padding: 0 6px;
    }
    #notificationCount {
        font-size: 10px;
        min-width: 14px;
        height: 14px;
        line-height: 14px;
        top: 2px;
        right: 2px;
    }
}

/* Harmonisation des icônes de la barre supérieure */
.header-right-menu i {
    font-size: 22px !important;
    color: aliceblue !important;
    width: 28px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    vertical-align: middle;
    display: inline-block;
} 