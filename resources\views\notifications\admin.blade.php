@extends('layouts.app')

@section('title', 'Administration des Notifications')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-bell"></i> Administration des Notifications
                    </h3>
                </div>
                <div class="panel-body">
                    
                    <!-- Statistiques -->
                    <div class="row" id="stats-section">
                        <div class="col-md-12">
                            <div class="panel panel-info">
                                <div class="panel-heading">
                                    <h4>Statistiques des Notifications</h4>
                                </div>
                                <div class="panel-body">
                                    <div class="row" id="stats-content">
                                        <div class="col-md-12 text-center">
                                            <i class="fa fa-spinner fa-spin"></i> Chargement des statistiques...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Formulaire de création de notification -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="panel panel-primary">
                                <div class="panel-heading">
                                    <h4>Créer une Nouvelle Notification</h4>
                                </div>
                                <div class="panel-body">
                                    <form id="notification-form">
                                        @csrf
                                        <div class="row">
                                            <div class="col-md-8">
                                                <div class="form-group">
                                                    <label for="message">Message de la notification *</label>
                                                    <textarea class="form-control" id="message" name="message" rows="3" 
                                                              placeholder="Saisissez le message de la notification..." required></textarea>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="url">URL de redirection (optionnel)</label>
                                                    <input type="text" class="form-control" id="url" name="url" 
                                                           placeholder="/dashboard">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Destinataires</label>
                                                    <div class="radio">
                                                        <label>
                                                            <input type="radio" name="recipient_type" value="all" checked>
                                                            Tous les utilisateurs
                                                        </label>
                                                    </div>
                                                    <div class="radio">
                                                        <label>
                                                            <input type="radio" name="recipient_type" value="specific">
                                                            Utilisateurs spécifiques
                                                        </label>
                                                    </div>
                                                    <div class="radio">
                                                        <label>
                                                            <input type="radio" name="recipient_type" value="self">
                                                            Moi uniquement
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group" id="users-selection" style="display: none;">
                                                    <label for="user_ids">Sélectionner les utilisateurs</label>
                                                    <select class="form-control" id="user_ids" name="user_ids[]" multiple>
                                                        <!-- Options chargées dynamiquement -->
                                                    </select>
                                                    <small class="help-block">Maintenez Ctrl pour sélectionner plusieurs utilisateurs</small>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-send"></i> Envoyer la Notification
                                            </button>
                                            <button type="reset" class="btn btn-default">
                                                <i class="fa fa-refresh"></i> Réinitialiser
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions rapides -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="panel panel-warning">
                                <div class="panel-heading">
                                    <h4>Actions Rapides</h4>
                                </div>
                                <div class="panel-body">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-info" onclick="sendQuickNotification('info')">
                                            <i class="fa fa-info-circle"></i> Notification d'Information
                                        </button>
                                        <button type="button" class="btn btn-success" onclick="sendQuickNotification('success')">
                                            <i class="fa fa-check-circle"></i> Notification de Succès
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="sendQuickNotification('warning')">
                                            <i class="fa fa-exclamation-triangle"></i> Notification d'Avertissement
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="sendQuickNotification('error')">
                                            <i class="fa fa-times-circle"></i> Notification d'Erreur
                                        </button>
                                    </div>
                                    <hr>
                                    <button type="button" class="btn btn-default" onclick="cleanOldNotifications()">
                                        <i class="fa fa-trash"></i> Nettoyer les Anciennes Notifications
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="refreshStats()">
                                        <i class="fa fa-refresh"></i> Actualiser les Statistiques
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Charger les statistiques au chargement
    loadStats();
    
    // Charger la liste des utilisateurs
    loadUsers();
    
    // Gérer l'affichage de la sélection d'utilisateurs
    $('input[name="recipient_type"]').change(function() {
        if ($(this).val() === 'specific') {
            $('#users-selection').show();
        } else {
            $('#users-selection').hide();
        }
    });
    
    // Gérer la soumission du formulaire
    $('#notification-form').submit(function(e) {
        e.preventDefault();
        sendNotification();
    });
});

function loadStats() {
    $.ajax({
        url: '{{ route("notifications.stats") }}',
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                displayStats(response.stats);
            }
        },
        error: function(xhr, status, error) {
            console.error('Erreur lors du chargement des statistiques:', error);
            $('#stats-content').html('<div class="alert alert-danger">Erreur lors du chargement des statistiques</div>');
        }
    });
}

function displayStats(stats) {
    var html = `
        <div class="col-md-2">
            <div class="text-center">
                <h3 class="text-primary">${stats.total_notifications}</h3>
                <p>Total Notifications</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="text-center">
                <h3 class="text-warning">${stats.notifications_non_lues}</h3>
                <p>Non Lues</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="text-center">
                <h3 class="text-success">${stats.notifications_lues}</h3>
                <p>Lues</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="text-center">
                <h3 class="text-info">${stats.utilisateurs_avec_notifications}</h3>
                <p>Utilisateurs</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="text-center">
                <h3 class="text-muted">${stats.notifications_aujourd_hui}</h3>
                <p>Aujourd'hui</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="text-center">
                <h3 class="text-muted">${stats.notifications_cette_semaine}</h3>
                <p>Cette Semaine</p>
            </div>
        </div>
    `;
    $('#stats-content').html(html);
}

function loadUsers() {
    // Simuler le chargement des utilisateurs (à remplacer par un appel AJAX réel)
    var users = [
        {id: 1, name: 'Administrateur', email: '<EMAIL>'},
        {id: 2, name: 'Gestionnaire', email: '<EMAIL>'},
        {id: 3, name: 'Utilisateur', email: '<EMAIL>'}
    ];
    
    var options = '';
    users.forEach(function(user) {
        options += `<option value="${user.id}">${user.name} (${user.email})</option>`;
    });
    $('#user_ids').html(options);
}

function sendNotification() {
    var formData = {
        message: $('#message').val(),
        url: $('#url').val(),
        send_to_all: $('input[name="recipient_type"]:checked').val() === 'all',
        user_ids: $('input[name="recipient_type"]:checked').val() === 'specific' ? $('#user_ids').val() : null
    };
    
    $.ajax({
        url: '{{ route("notifications.store") }}',
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: formData,
        success: function(response) {
            if (response.success) {
                alert('Notification envoyée avec succès!');
                $('#notification-form')[0].reset();
                $('#users-selection').hide();
                loadStats(); // Actualiser les statistiques
            } else {
                alert('Erreur: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Erreur lors de l\'envoi:', error);
            alert('Erreur lors de l\'envoi de la notification');
        }
    });
}

function sendQuickNotification(type) {
    var messages = {
        'info': 'Information importante pour tous les utilisateurs',
        'success': 'Opération réalisée avec succès',
        'warning': 'Attention : maintenance programmée',
        'error': 'Problème technique détecté'
    };
    
    var message = prompt('Message de la notification:', messages[type]);
    if (message) {
        $.ajax({
            url: '{{ route("notifications.store") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
                message: message,
                send_to_all: true
            },
            success: function(response) {
                if (response.success) {
                    alert('Notification envoyée avec succès!');
                    loadStats();
                } else {
                    alert('Erreur: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('Erreur lors de l\'envoi de la notification');
            }
        });
    }
}

function cleanOldNotifications() {
    if (confirm('Êtes-vous sûr de vouloir supprimer les notifications de plus de 30 jours?')) {
        // Implémentation à ajouter
        alert('Fonctionnalité à implémenter');
    }
}

function refreshStats() {
    loadStats();
}
</script>
@endsection
