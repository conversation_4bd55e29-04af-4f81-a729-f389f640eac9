<?php

/**
 * Comparaison des deux bases de données pour identifier laquelle contient les anciennes données
 */

echo "🔍 COMPARAISON DES BASES DE DONNÉES\n";
echo "===================================\n\n";

try {
    // Connexions aux deux bases
    $db1 = new PDO('sqlite:database/database.sqlite');
    $db2 = new PDO('sqlite:database/database_new.sqlite');
    
    $db1->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db2->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Informations sur les fichiers
    $size1 = filesize('database/database.sqlite');
    $size2 = filesize('database/database_new.sqlite');
    
    echo "📊 INFORMATIONS DES FICHIERS :\n";
    echo "==============================\n";
    echo "• database.sqlite (ACTUELLE) : " . number_format($size1 / 1024, 2) . " KB\n";
    echo "• database_new.sqlite (NOUVELLE) : " . number_format($size2 / 1024, 2) . " KB\n\n";
    
    // Tables principales à comparer
    $tablesToCheck = ['personne', 'users', 'reformes', 'activites_reformes', 'indicateurs', 'user_sessions'];
    
    echo "📋 COMPARAISON DES DONNÉES :\n";
    echo "============================\n";
    
    foreach ($tablesToCheck as $table) {
        echo "🔸 Table '$table' :\n";
        
        try {
            $count1 = $db1->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            echo "  • database.sqlite (actuelle) : $count1 enregistrements\n";
        } catch (Exception $e) {
            echo "  • database.sqlite (actuelle) : Table n'existe pas\n";
            $count1 = 0;
        }
        
        try {
            $count2 = $db2->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            echo "  • database_new.sqlite (nouvelle) : $count2 enregistrements\n";
        } catch (Exception $e) {
            echo "  • database_new.sqlite (nouvelle) : Table n'existe pas\n";
            $count2 = 0;
        }
        
        if ($count1 > $count2) {
            echo "  ➡️  Plus de données dans la base ACTUELLE\n";
        } elseif ($count2 > $count1) {
            echo "  ➡️  Plus de données dans la base NOUVELLE\n";
        } else {
            echo "  ➡️  Même nombre de données\n";
        }
        echo "\n";
    }
    
    // Vérification des utilisateurs spécifiques
    echo "👥 UTILISATEURS DANS CHAQUE BASE :\n";
    echo "==================================\n";
    
    echo "🔹 Base ACTUELLE (database.sqlite) :\n";
    try {
        $users1 = $db1->query("
            SELECT u.id, p.nom, p.prenom, p.email 
            FROM users u 
            LEFT JOIN personne p ON u.personne_id = p.id 
            LIMIT 5
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($users1 as $user) {
            echo "  • ID {$user['id']}: {$user['nom']} {$user['prenom']} ({$user['email']})\n";
        }
    } catch (Exception $e) {
        echo "  ❌ Erreur : " . $e->getMessage() . "\n";
    }
    
    echo "\n🔹 Base NOUVELLE (database_new.sqlite) :\n";
    try {
        $users2 = $db2->query("
            SELECT u.id, p.nom, p.prenom, p.email 
            FROM users u 
            LEFT JOIN personne p ON u.personne_id = p.id 
            LIMIT 5
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($users2 as $user) {
            echo "  • ID {$user['id']}: {$user['nom']} {$user['prenom']} ({$user['email']})\n";
        }
    } catch (Exception $e) {
        echo "  ❌ Erreur : " . $e->getMessage() . "\n";
    }
    
    // Vérification des réformes
    echo "\n🔄 RÉFORMES DANS CHAQUE BASE :\n";
    echo "==============================\n";
    
    echo "🔹 Base ACTUELLE (database.sqlite) :\n";
    try {
        $reformes1 = $db1->query("SELECT id, titre, statut FROM reformes LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
        foreach ($reformes1 as $reforme) {
            echo "  • ID {$reforme['id']}: {$reforme['titre']} - Statut: {$reforme['statut']}\n";
        }
    } catch (Exception $e) {
        echo "  ❌ Erreur : " . $e->getMessage() . "\n";
    }
    
    echo "\n🔹 Base NOUVELLE (database_new.sqlite) :\n";
    try {
        $reformes2 = $db2->query("SELECT id, titre, statut FROM reformes LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
        foreach ($reformes2 as $reforme) {
            echo "  • ID {$reforme['id']}: {$reforme['titre']} - Statut: {$reforme['statut']}\n";
        }
    } catch (Exception $e) {
        echo "  ❌ Erreur : " . $e->getMessage() . "\n";
    }
    
    // Dates de création pour identifier la plus récente
    echo "\n📅 DATES DE DERNIÈRE MODIFICATION :\n";
    echo "===================================\n";
    
    try {
        $lastUser1 = $db1->query("SELECT MAX(created_at) as last_date FROM users")->fetchColumn();
        echo "• Dernier utilisateur créé (actuelle) : $lastUser1\n";
    } catch (Exception $e) {
        echo "• Dernier utilisateur créé (actuelle) : Erreur\n";
    }
    
    try {
        $lastUser2 = $db2->query("SELECT MAX(created_at) as last_date FROM users")->fetchColumn();
        echo "• Dernier utilisateur créé (nouvelle) : $lastUser2\n";
    } catch (Exception $e) {
        echo "• Dernier utilisateur créé (nouvelle) : Erreur\n";
    }
    
    echo "\n🎯 RECOMMANDATION :\n";
    echo "===================\n";
    
    if ($size2 > $size1) {
        echo "➡️  La base 'database_new.sqlite' semble contenir plus de données.\n";
        echo "    Vous devriez probablement migrer DE database_new.sqlite VERS database.sqlite\n";
    } elseif ($size1 > $size2) {
        echo "➡️  La base 'database.sqlite' semble contenir plus de données.\n";
        echo "    Vos données actuelles sont déjà dans la bonne base.\n";
    } else {
        echo "➡️  Les deux bases ont une taille similaire.\n";
        echo "    Vérifiez manuellement laquelle contient vos vraies données.\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERREUR : " . $e->getMessage() . "\n";
}

echo "\n✅ COMPARAISON TERMINÉE\n";

?>
