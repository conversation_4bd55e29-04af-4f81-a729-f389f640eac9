<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AhoRolePermissionsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Exécuter les seeders nécessaires
        $this->artisan('db:seed', ['--class' => 'RoleSeeder']);
        $this->artisan('db:seed', ['--class' => 'PermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'MenuSeeder']);
        $this->artisan('db:seed', ['--class' => 'SyncAhoRoleSeeder']);
    }

    public function test_aho_role_exists_and_has_permissions()
    {
        $ahoRole = Role::where('role_name', 'aho')->first();
        
        $this->assertNotNull($ahoRole, 'Le rôle "aho" doit exister');
        $this->assertGreater<PERSON>han(0, $ahoRole->permissionMenus->count(), 'Le rôle "aho" doit avoir des permissions');
    }

    public function test_aho_user_can_access_menus()
    {
        // Créer un utilisateur avec le rôle aho
        $user = User::factory()->create([
            'name' => 'Test Aho User',
            'email' => '<EMAIL>',
        ]);
        
        $ahoRole = Role::where('role_name', 'aho')->first();
        $user->roles()->attach($ahoRole);
        
        // Tester l'accès aux menus
        $accessibleMenus = $user->getAccessibleMenus();
        
        $this->assertGreaterThan(0, $accessibleMenus->count(), 'L\'utilisateur aho doit avoir accès à des menus');
        
        // Vérifier l'accès aux menus spécifiques
        $menuUrls = $accessibleMenus->pluck('url')->toArray();
        
        $expectedUrls = ['/dashboard', '/activites', '/reforme', '/indicateurs'];
        foreach ($expectedUrls as $url) {
            $this->assertContains($url, $menuUrls, "L'utilisateur aho doit avoir accès au menu {$url}");
        }
    }

    public function test_aho_user_has_crud_permissions()
    {
        // Créer un utilisateur avec le rôle aho
        $user = User::factory()->create([
            'name' => 'Test Aho User',
            'email' => '<EMAIL>',
        ]);
        
        $ahoRole = Role::where('role_name', 'aho')->first();
        $user->roles()->attach($ahoRole);
        
        // Tester les permissions CRUD sur différents modules
        $testUrls = ['/dashboard', '/activites', '/reforme', '/indicateurs'];
        $permissions = ['Créer', 'Lire', 'Modifier', 'Supprimer'];
        
        foreach ($testUrls as $url) {
            foreach ($permissions as $permission) {
                $hasPermission = $user->hasPermissionForUrl($url, $permission);
                $this->assertTrue(
                    $hasPermission, 
                    "L'utilisateur aho doit avoir la permission '{$permission}' sur '{$url}'"
                );
            }
        }
    }

    public function test_aho_user_can_access_activities_page()
    {
        // Créer un utilisateur avec le rôle aho
        $user = User::factory()->create([
            'name' => 'Test Aho User',
            'email' => '<EMAIL>',
        ]);
        
        $ahoRole = Role::where('role_name', 'aho')->first();
        $user->roles()->attach($ahoRole);
        
        // Simuler la connexion
        $this->actingAs($user);
        
        // Tester l'accès à la page des activités
        $response = $this->get('/activites');
        
        $response->assertStatus(200);
        $response->assertDontSee('Accès refusé');
    }

    public function test_aho_user_can_access_reforms_page()
    {
        // Créer un utilisateur avec le rôle aho
        $user = User::factory()->create([
            'name' => 'Test Aho User',
            'email' => '<EMAIL>',
        ]);
        
        $ahoRole = Role::where('role_name', 'aho')->first();
        $user->roles()->attach($ahoRole);
        
        // Simuler la connexion
        $this->actingAs($user);
        
        // Tester l'accès à la page des réformes
        $response = $this->get('/reforme');
        
        $response->assertStatus(200);
        $response->assertDontSee('Accès refusé');
    }

    public function test_menu_component_shows_menus_for_aho_user()
    {
        // Créer un utilisateur avec le rôle aho
        $user = User::factory()->create([
            'name' => 'Test Aho User',
            'email' => '<EMAIL>',
        ]);
        
        $ahoRole = Role::where('role_name', 'aho')->first();
        $user->roles()->attach($ahoRole);
        
        // Simuler la connexion
        $this->actingAs($user);
        
        // Tester le composant de menu
        $component = new \App\Livewire\MenuComponent();
        $component->mount();
        
        $this->assertGreaterThan(0, $component->menus->count(), 'Le composant menu doit afficher des menus pour l\'utilisateur aho');
    }
}
