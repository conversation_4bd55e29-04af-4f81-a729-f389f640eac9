<?php

/**
 * Script d'exportation complète avec toutes les relations
 */

echo "🗄️  EXPORTATION COMPLÈTE DE TOUTES VOS DONNÉES\n";
echo "===============================================\n\n";

try {
    $pdo = new PDO('sqlite:database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 1. PERSONNES (informations détaillées des utilisateurs)
    echo "👤 VOS PERSONNES :\n";
    echo "==================\n";
    try {
        $stmt = $pdo->query("SELECT * FROM personne");
        $personnes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($personnes) > 0) {
            foreach ($personnes as $personne) {
                echo "• Personne ID: {$personne['id']}\n";
                foreach ($personne as $key => $value) {
                    echo "  $key: $value\n";
                }
                echo "  ---\n";
            }
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 2. UTILISATEURS AVEC INFORMATIONS PERSONNELLES
    echo "👥 VOS UTILISATEURS COMPLETS :\n";
    echo "==============================\n";
    try {
        $stmt = $pdo->query("SELECT u.*, p.nom, p.prenom, p.email, p.telephone 
                            FROM users u 
                            LEFT JOIN personne p ON u.personne_id = p.id");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($users) > 0) {
            foreach ($users as $user) {
                echo "• Utilisateur ID: {$user['id']}\n";
                echo "  Nom complet: {$user['nom']} {$user['prenom']}\n";
                echo "  Email: {$user['email']}\n";
                echo "  Téléphone: {$user['telephone']}\n";
                echo "  Statut: " . ($user['status'] ? 'Actif' : 'Inactif') . "\n";
                echo "  Créé le: {$user['created_at']}\n";
                echo "  ---\n";
            }
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 3. ASSOCIATIONS UTILISATEUR-RÔLE CORRIGÉES
    echo "👤 ASSOCIATIONS UTILISATEUR-RÔLE :\n";
    echo "==================================\n";
    try {
        $stmt = $pdo->query("SELECT ur.*, p.nom, p.prenom, p.email, r.role_name 
                            FROM user_role ur 
                            LEFT JOIN users u ON ur.user_id = u.id 
                            LEFT JOIN personne p ON u.personne_id = p.id
                            LEFT JOIN role r ON ur.role_id = r.id");
        $userRoles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($userRoles) > 0) {
            foreach ($userRoles as $ur) {
                echo "• Association ID: {$ur['id']}\n";
                echo "  Utilisateur: {$ur['nom']} {$ur['prenom']} ({$ur['email']})\n";
                echo "  Rôle: {$ur['role_name']}\n";
                echo "  ---\n";
            }
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 4. ASSOCIATIONS RÔLE-PERMISSION CORRIGÉES
    echo "🔗 ASSOCIATIONS RÔLE-PERMISSION :\n";
    echo "=================================\n";
    try {
        $stmt = $pdo->query("SELECT rp.*, r.role_name, p.permission_name 
                            FROM role_permission rp 
                            LEFT JOIN role r ON rp.role_id = r.id 
                            LEFT JOIN permission p ON rp.permission_id = p.id");
        $associations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($associations) > 0) {
            // Grouper par rôle
            $rolePermissions = [];
            foreach ($associations as $assoc) {
                $rolePermissions[$assoc['role_name']][] = $assoc['permission_name'];
            }
            
            foreach ($rolePermissions as $role => $permissions) {
                echo "• Rôle: $role\n";
                echo "  Permissions: " . implode(', ', $permissions) . "\n";
                echo "  ---\n";
            }
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 5. MENUS
    echo "📋 VOS MENUS :\n";
    echo "==============\n";
    try {
        $stmt = $pdo->query("SELECT * FROM menu ORDER BY id");
        $menus = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($menus) > 0) {
            foreach ($menus as $menu) {
                echo "• Menu ID: {$menu['id']}\n";
                foreach ($menu as $key => $value) {
                    echo "  $key: $value\n";
                }
                echo "  ---\n";
            }
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 6. PERMISSIONS PAR MENU
    echo "🔑 PERMISSIONS PAR MENU :\n";
    echo "=========================\n";
    try {
        $stmt = $pdo->query("SELECT pm.*, m.libelle as menu_libelle, p.permission_name 
                            FROM permission_menu pm 
                            LEFT JOIN menu m ON pm.menu_id = m.id 
                            LEFT JOIN permission p ON pm.permission_id = p.id 
                            ORDER BY m.libelle, p.permission_name");
        $permissionMenus = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($permissionMenus) > 0) {
            $menuPermissions = [];
            foreach ($permissionMenus as $pm) {
                $menuPermissions[$pm['menu_libelle']][] = $pm['permission_name'];
            }
            
            foreach ($menuPermissions as $menu => $permissions) {
                echo "• Menu: $menu\n";
                echo "  Permissions disponibles: " . implode(', ', $permissions) . "\n";
                echo "  ---\n";
            }
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 7. SESSIONS UTILISATEURS
    echo "🔐 SESSIONS UTILISATEURS :\n";
    echo "=========================\n";
    try {
        $stmt = $pdo->query("SELECT us.*, p.nom, p.prenom, p.email 
                            FROM user_sessions us 
                            LEFT JOIN users u ON us.user_id = u.id 
                            LEFT JOIN personne p ON u.personne_id = p.id 
                            ORDER BY us.login_time DESC");
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($sessions) > 0) {
            foreach ($sessions as $session) {
                echo "• Session ID: {$session['id']}\n";
                echo "  Utilisateur: {$session['nom']} {$session['prenom']} ({$session['email']})\n";
                echo "  Connexion: {$session['login_time']}\n";
                echo "  IP: {$session['ip_address']}\n";
                echo "  User Agent: {$session['user_agent']}\n";
                echo "  ---\n";
            }
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 8. RÉFORMES AVEC STRUCTURES
    echo "🔄 RÉFORMES ET STRUCTURES :\n";
    echo "===========================\n";
    try {
        $stmt = $pdo->query("SELECT rs.*, r.titre as reforme_titre, s.lib_court as structure_nom 
                            FROM reformes_structure rs 
                            LEFT JOIN reformes r ON rs.reforme_id = r.id 
                            LEFT JOIN structure s ON rs.structure_id = s.id");
        $reformeStructures = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($reformeStructures) > 0) {
            foreach ($reformeStructures as $rs) {
                echo "• Association ID: {$rs['id']}\n";
                echo "  Réforme: {$rs['reforme_titre']}\n";
                echo "  Structure: {$rs['structure_nom']}\n";
                echo "  ---\n";
            }
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 9. RÉSUMÉ FINAL DÉTAILLÉ
    echo "📊 RÉSUMÉ DÉTAILLÉ DE VOS DONNÉES :\n";
    echo "===================================\n";
    
    // Compter les utilisateurs actifs
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE status = 1");
    $activeUsers = $stmt->fetchColumn();
    
    // Compter les réformes par statut
    $stmt = $pdo->query("SELECT statut, COUNT(*) as count FROM reformes GROUP BY statut");
    $reformeStatuts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Compter les activités par statut
    $stmt = $pdo->query("SELECT statut, COUNT(*) as count FROM activites_reformes GROUP BY statut");
    $activiteStatuts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "• Utilisateurs actifs: $activeUsers sur " . $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn() . "\n";
    
    echo "• Réformes par statut:\n";
    foreach ($reformeStatuts as $statut) {
        $statutLabel = $statut['statut'] == 'C' ? 'Complétées' : 'En cours';
        echo "  - $statutLabel: {$statut['count']}\n";
    }
    
    echo "• Activités par statut:\n";
    foreach ($activiteStatuts as $statut) {
        $statutLabel = $statut['statut'] == 'A' ? 'Achevées' : 'En cours';
        echo "  - $statutLabel: {$statut['count']}\n";
    }
    
    // Dernière activité
    $stmt = $pdo->query("SELECT MAX(created_at) FROM suivi_activites");
    $lastActivity = $stmt->fetchColumn();
    echo "• Dernière activité de suivi: $lastActivity\n";
    
    // Dernière connexion
    $stmt = $pdo->query("SELECT MAX(login_time) FROM user_sessions");
    $lastLogin = $stmt->fetchColumn();
    echo "• Dernière connexion: $lastLogin\n";
    
} catch (Exception $e) {
    echo "❌ Erreur de connexion : " . $e->getMessage() . "\n";
}

echo "\n🎉 EXPORTATION COMPLÈTE TERMINÉE\n";
echo "=================================\n";
echo "Toutes vos données ont été exportées avec succès !\n";

?>
