@extends('layouts.app')

@section('title', 'Suivi des Indicateurs')

@section('content')
<div class="container-fluid">
    <style>
        /* Styles spécifiques pour le suivi des indicateurs */
        .suivi-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .suivi-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .suivi-header .subtitle {
            margin-top: 10px;
            font-size: 1.1em;
            opacity: 0.9;
        }

        .indicator-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #3498db;
        }

        .indicator-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .indicator-card.active {
            border-left-color: #2ecc71;
        }

        .indicator-card.warning {
            border-left-color: #f39c12;
        }

        .indicator-card.danger {
            border-left-color: #e74c3c;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .trend-indicator {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }

        .trend-hausse {
            background: #d4edda;
            color: #155724;
        }

        .trend-baisse {
            background: #f8d7da;
            color: #721c24;
        }

        .trend-stable {
            background: #fff3cd;
            color: #856404;
        }

        .evolution-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .evolution-table .table {
            margin-bottom: 0;
        }

        .evolution-table .table th {
            background: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #495057;
        }

        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .refresh-btn.spinning {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .last-update {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .suivi-header h1 {
                font-size: 2em;
            }
            
            .indicator-card {
                margin-bottom: 15px;
            }
            
            .refresh-btn {
                bottom: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
                font-size: 1.2em;
            }
        }
    </style>

    <!-- Header -->
    <div class="suivi-header">
        <h1><i class="fa fa-line-chart"></i> Suivi des Indicateurs</h1>
        <div class="subtitle">
            Tableau de bord complet pour le suivi et l'analyse des indicateurs de performance
        </div>
    </div>

    <!-- Statistiques Principales -->
    <div class="row">
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <div class="indicator-card active">
                <div class="stat-number" style="color: #2ecc71;">
                    {{ $statistiques['total_indicateurs'] }}
                </div>
                <div class="stat-label">
                    <i class="fa fa-dashboard"></i> Total Indicateurs
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <div class="indicator-card active">
                <div class="stat-number" style="color: #3498db;">
                    {{ $statistiques['indicateurs_actifs'] }}
                </div>
                <div class="stat-label">
                    <i class="fa fa-play-circle"></i> Indicateurs Actifs
                </div>
                <div class="trend-indicator trend-stable">
                    {{ $statistiques['pourcentage_actifs'] }}% du total
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <div class="indicator-card">
                <div class="stat-number" style="color: #f39c12;">
                    {{ $statistiques['total_mesures'] }}
                </div>
                <div class="stat-label">
                    <i class="fa fa-bar-chart"></i> Total Mesures
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
            <div class="indicator-card warning">
                <div class="stat-number" style="color: #e74c3c;">
                    {{ $statistiques['mesures_recentes'] }}
                </div>
                <div class="stat-label">
                    <i class="fa fa-clock-o"></i> Mesures Récentes (30j)
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques et Analyses -->
    <div class="row">
        <div class="col-lg-8 col-md-12">
            <div class="chart-container">
                <h4><i class="fa fa-line-chart"></i> Tendances des Indicateurs (6 derniers mois)</h4>
                <canvas id="tendancesChart" height="100"></canvas>
            </div>
        </div>

        <div class="col-lg-4 col-md-12">
            <div class="chart-container">
                <h4><i class="fa fa-pie-chart"></i> Répartition par Réforme</h4>
                <canvas id="repartitionChart" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Indicateurs les Plus Actifs -->
    <div class="row">
        <div class="col-lg-6 col-md-12">
            <div class="evolution-table">
                <div class="panel-heading" style="background: #f8f9fa; padding: 15px; border-bottom: 1px solid #dee2e6;">
                    <h4 style="margin: 0;"><i class="fa fa-trophy"></i> Indicateurs les Plus Actifs</h4>
                </div>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Indicateur</th>
                            <th>Unité</th>
                            <th class="text-center">Nb Mesures</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($indicateursActifs as $indicateur)
                        <tr>
                            <td>
                                <strong>{{ $indicateur->libelle }}</strong>
                            </td>
                            <td>
                                <span class="label label-info">{{ $indicateur->unite }}</span>
                            </td>
                            <td class="text-center">
                                <span class="label label-success">{{ $indicateur->nb_mesures }}</span>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="3" class="text-center text-muted">
                                <i class="fa fa-info-circle"></i> Aucun indicateur actif trouvé
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <div class="col-lg-6 col-md-12">
            <div class="evolution-table">
                <div class="panel-heading" style="background: #f8f9fa; padding: 15px; border-bottom: 1px solid #dee2e6;">
                    <h4 style="margin: 0;"><i class="fa fa-history"></i> Évolutions Récentes</h4>
                </div>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Indicateur</th>
                            <th>Valeur</th>
                            <th>Tendance</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($evolutionsRecentes->take(8) as $evolution)
                        <tr>
                            <td>{{ $evolution['date'] }}</td>
                            <td>
                                <small class="text-muted">{{ Str::limit($evolution['indicateur'], 20) }}</small>
                            </td>
                            <td>
                                <strong>{{ number_format($evolution['valeur'], 2, ',', ' ') }}</strong>
                                <small class="text-muted">{{ $evolution['unite'] }}</small>
                            </td>
                            <td>
                                <span class="trend-indicator trend-{{ $evolution['tendance'] }}">
                                    @if($evolution['tendance'] == 'hausse')
                                        <i class="fa fa-arrow-up"></i>
                                    @elseif($evolution['tendance'] == 'baisse')
                                        <i class="fa fa-arrow-down"></i>
                                    @else
                                        <i class="fa fa-minus"></i>
                                    @endif
                                    {{ ucfirst($evolution['tendance']) }}
                                </span>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="4" class="text-center text-muted">
                                <i class="fa fa-info-circle"></i> Aucune évolution récente
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Bouton de rafraîchissement -->
    <button class="refresh-btn" onclick="refreshData()" title="Actualiser les données">
        <i class="fa fa-refresh"></i>
    </button>

    <!-- Dernière mise à jour -->
    <div class="last-update">
        <i class="fa fa-clock-o"></i> Dernière mise à jour : <span id="lastUpdate">{{ now()->format('d/m/Y H:i:s') }}</span>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Configuration des graphiques
let tendancesChart, repartitionChart;

document.addEventListener('DOMContentLoaded', function() {
    initCharts();
    
    // Auto-refresh toutes les 5 minutes
    setInterval(refreshData, 300000);
});

function initCharts() {
    // Graphique des tendances
    const tendancesCtx = document.getElementById('tendancesChart').getContext('2d');
    const tendancesData = @json($tendancesIndicateurs);
    
    // Graphique de répartition
    const repartitionCtx = document.getElementById('repartitionChart').getContext('2d');
    const repartitionData = @json($repartitionParReforme);
    
    // Initialiser les graphiques avec les données
    createTendancesChart(tendancesCtx, tendancesData);
    createRepartitionChart(repartitionCtx, repartitionData);
}

function createTendancesChart(ctx, data) {
    // Logique pour créer le graphique des tendances
    // (Implémentation simplifiée pour l'exemple)
    tendancesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
            datasets: [{
                label: 'Évolution Moyenne',
                data: [65, 59, 80, 81, 56, 55],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function createRepartitionChart(ctx, data) {
    // Graphique en secteurs pour la répartition
    repartitionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.map(item => item.titre.substring(0, 20) + '...'),
            datasets: [{
                data: data.map(item => item.nb_indicateurs),
                backgroundColor: [
                    '#3498db', '#2ecc71', '#f39c12', '#e74c3c', '#9b59b6',
                    '#1abc9c', '#34495e', '#f1c40f', '#e67e22', '#95a5a6'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function refreshData() {
    const btn = document.querySelector('.refresh-btn');
    btn.classList.add('spinning');
    
    fetch('{{ route("dashboard.suivi-indicateurs-api") }}')
        .then(response => response.json())
        .then(data => {
            // Mettre à jour les statistiques
            updateStatistics(data.statistiques);
            
            // Mettre à jour l'heure
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString('fr-FR');
            
            btn.classList.remove('spinning');
        })
        .catch(error => {
            console.error('Erreur lors du rafraîchissement:', error);
            btn.classList.remove('spinning');
        });
}

function updateStatistics(stats) {
    // Mettre à jour les valeurs affichées
    // (Implémentation à compléter selon les besoins)
}
</script>
@endsection
