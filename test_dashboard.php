<?php

/**
 * Script de test pour vérifier les fonctionnalités du dashboard
 * Exécuter avec : php test_dashboard.php
 */

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Session;
use App\Models\Reforme;
use App\Models\Activitesreformes;
use App\Models\Indicateur;
use Carbon\Carbon;

// Initialiser Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TEST DU DASHBOARD AMÉLIORÉ ===\n\n";

try {
    // Test 1: Vérifier la connexion à la base de données
    echo "1. Test de connexion à la base de données...\n";
    $userCount = User::count();
    echo "   ✓ Connexion réussie. Nombre d'utilisateurs : $userCount\n\n";

    // Test 2: Vérifier les sessions utilisateur
    echo "2. Test des sessions utilisateur...\n";
    $activeSessions = Session::active()->count();
    $totalSessions = Session::count();
    echo "   ✓ Sessions actives : $activeSessions\n";
    echo "   ✓ Total sessions : $totalSessions\n\n";

    // Test 3: Calculer les utilisateurs connectés
    echo "3. Test du calcul des utilisateurs connectés...\n";
    $connectedUsers = Session::active()
        ->where('last_activity', '>=', Carbon::now()->subMinutes(30))
        ->distinct('user_id')
        ->count('user_id');
    echo "   ✓ Utilisateurs connectés (dernières 30 min) : $connectedUsers\n\n";

    // Test 4: Vérifier les activités et leur validation
    echo "4. Test des activités et validation...\n";
    $totalActivites = Activitesreformes::count();
    $activitesValidees = Activitesreformes::where('statut', 'A')->count();
    $pourcentageActivites = $totalActivites > 0 ? round(($activitesValidees / $totalActivites) * 100, 1) : 0;
    echo "   ✓ Total activités : $totalActivites\n";
    echo "   ✓ Activités validées : $activitesValidees\n";
    echo "   ✓ Pourcentage : $pourcentageActivites%\n\n";

    // Test 5: Vérifier les réformes et leur validation
    echo "5. Test des réformes et validation...\n";
    $totalReformes = Reforme::count();
    $reformesValidees = Reforme::where(function($query) {
        $query->where('statut_manuel', 'Achevé')
              ->orWhere(function($q) {
                  $q->whereNull('statut_manuel')
                    ->whereNotNull('date_fin');
              });
    })->count();
    $pourcentageReformes = $totalReformes > 0 ? round(($reformesValidees / $totalReformes) * 100, 1) : 0;
    echo "   ✓ Total réformes : $totalReformes\n";
    echo "   ✓ Réformes validées : $reformesValidees\n";
    echo "   ✓ Pourcentage : $pourcentageReformes%\n\n";

    // Test 6: Vérifier les indicateurs
    echo "6. Test des indicateurs...\n";
    $totalIndicateurs = Indicateur::count();
    echo "   ✓ Total indicateurs : $totalIndicateurs\n\n";

    // Test 7: Simuler une requête dashboard
    echo "7. Test de simulation d'une requête dashboard...\n";
    $dashboardData = [
        'utilisateursConnectes' => $connectedUsers,
        'totalActivites' => $totalActivites,
        'activitesValidees' => $activitesValidees,
        'pourcentageActivites' => $pourcentageActivites,
        'totalReformes' => $totalReformes,
        'reformesValidees' => $reformesValidees,
        'pourcentageReformes' => $pourcentageReformes,
        'totalIndicateurs' => $totalIndicateurs,
    ];
    
    echo "   ✓ Données du dashboard générées avec succès :\n";
    foreach ($dashboardData as $key => $value) {
        echo "     - $key: $value\n";
    }
    echo "\n";

    // Test 8: Vérifier les activités récentes
    echo "8. Test des activités récentes...\n";
    $activitesRecentes = Activitesreformes::with(['reforme', 'creator.personne'])
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get();
    echo "   ✓ Activités récentes trouvées : " . $activitesRecentes->count() . "\n";
    foreach ($activitesRecentes as $activite) {
        echo "     - " . ($activite->libelle ?? 'Sans titre') . " (" . $activite->created_at->format('d/m/Y') . ")\n";
    }
    echo "\n";

    // Test 9: Vérifier les réformes par type
    echo "9. Test des réformes par type...\n";
    $reformesParType = DB::table('reformes')
        ->join('type_reforme', 'reformes.type_reforme', '=', 'type_reforme.id')
        ->select('type_reforme.lib', DB::raw('count(*) as total'))
        ->groupBy('type_reforme.lib')
        ->get();
    echo "   ✓ Types de réformes trouvés : " . $reformesParType->count() . "\n";
    foreach ($reformesParType as $type) {
        echo "     - {$type->lib}: {$type->total}\n";
    }
    echo "\n";

    echo "=== TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS ! ===\n";
    echo "Le dashboard est prêt à être utilisé.\n\n";

    echo "Pour accéder au dashboard :\n";
    echo "1. Démarrez le serveur : php artisan serve\n";
    echo "2. Ouvrez votre navigateur : http://127.0.0.1:8000/dashboard\n";
    echo "3. Connectez-vous avec un utilisateur existant\n\n";

} catch (Exception $e) {
    echo "❌ ERREUR : " . $e->getMessage() . "\n";
    echo "Trace : " . $e->getTraceAsString() . "\n";
    exit(1);
}
