<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PersonneSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('personne')->insert([
            [
                'id' => 1,
                'nom' => 'Admin',
                'prenom' => 'Système',
                'fonction' => 'Administrateur',
                'tel' => '0123456789',
                'email' => '<EMAIL>',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 2,
                'nom' => 'Utilisateur',
                'prenom' => 'Test',
                'fonction' => 'Utilisateur',
                'tel' => '0123456790',
                'email' => '<EMAIL>',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 3,
                'nom' => 'Gestionnaire',
                'prenom' => 'Projet',
                'fonction' => 'Chef de projet',
                'tel' => '0123456791',
                'email' => '<EMAIL>',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 4,
                'nom' => 'Superviseur',
                'prenom' => 'Général',
                'fonction' => 'Superviseur',
                'tel' => '0123456792',
                'email' => '<EMAIL>',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
