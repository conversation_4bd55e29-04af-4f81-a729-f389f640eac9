<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Pour la table 'suivi_activites'
        Schema::table('suivi_activites', function (Blueprint $table) {
            // Supprimer l'ancienne contrainte
            $table->dropForeign(['activite_reforme_id']);

            // Recréer la contrainte avec la suppression en cascade
            $table->foreign('activite_reforme_id')
                  ->references('id')->on('activites_reformes')
                  ->onDelete('cascade');
        });

        // Pour la table 'activites_reformes' (auto-référence)
        Schema::table('activites_reformes', function (Blueprint $table) {
            // Supprimer l'ancienne contrainte
            $table->dropForeign(['parent']);

            // Recréer la contrainte avec la suppression en cascade
            $table->foreign('parent')
                  ->references('id')->on('activites_reformes')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Pour la table 'suivi_activites'
        Schema::table('suivi_activites', function (Blueprint $table) {
            $table->dropForeign(['activite_reforme_id']);
            $table->foreign('activite_reforme_id')->references('id')->on('activites_reformes');
        });

        // Pour la table 'activites_reformes'
        Schema::table('activites_reformes', function (Blueprint $table) {
            $table->dropForeign(['parent']);
            $table->foreign('parent')->references('id')->on('activites_reformes');
        });
    }
};
