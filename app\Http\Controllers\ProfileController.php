<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Personne;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class ProfileController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Afficher le profil de l'utilisateur connecté
     */
    public function show()
    {
        $user = Auth::user()->load(['personne', 'roles']);
        return view('profile', compact('user'));
    }

    /**
     * Mettre à jour le profil de l'utilisateur
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        
        // Validation des données
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|email|unique:personne,email,' . $user->personne->id,
            'fonction' => 'nullable|string|max:255',
            'tel' => 'nullable|string|max:20',
            'current_password' => 'required|string',
            'new_password' => 'nullable|string|min:8|confirmed',
        ]);
        
        // Vérifier que le mot de passe actuel est correct
        if (!Hash::check($request->current_password, $user->pwd)) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['current_password' => 'Le mot de passe actuel est incorrect.']);
        }

        try {
            // Mise à jour des informations de la personne
            $personne = $user->personne;
            $personne->nom = $validated['nom'];
            $personne->prenom = $validated['prenom'];
            $personne->email = $validated['email'];
            $personne->fonction = $validated['fonction'];
            $personne->tel = $validated['tel'];
            $personne->save();

            // Mise à jour du mot de passe si demandé
            if ($request->filled('new_password')) {
                $user->pwd = Hash::make($request->new_password);
                $user->save();
            }

            return redirect()->route('profile.show')->with('success', 'Profil mis à jour avec succès !');
            
        } catch (\Exception $e) {
            \Log::error('Profile update error: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Erreur lors de la mise à jour du profil: ' . $e->getMessage()]);
        }
    }
} 