<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class RestoreOldDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Début de la restauration des données...');

        // Chemin vers l'ancienne base de données
        $oldDbPath = database_path('database.sqlite');
        
        if (!file_exists($oldDbPath)) {
            $this->command->error('Ancienne base de données non trouvée : ' . $oldDbPath);
            return;
        }

        // Connexion à l'ancienne base de données
        $oldDb = new \PDO('sqlite:' . $oldDbPath);
        $oldDb->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

        try {
            // Restaurer les utilisateurs
            $this->restoreUsers($oldDb);
            
            // Restaurer les types de réforme
            $this->restoreTypeReformes($oldDb);
            
            // Restaurer les structures
            $this->restoreStructures($oldDb);
            
            // Restaurer les réformes
            $this->restoreReformes($oldDb);
            
            // Restaurer les indicateurs
            $this->restoreIndicateurs($oldDb);
            
            // Restaurer les relations réformes-structures
            $this->restoreReformeStructures($oldDb);
            
            // Restaurer les relations réformes-indicateurs
            $this->restoreReformeIndicateurs($oldDb);
            
            // Restaurer les activités
            $this->restoreActivites($oldDb);
            
            // Restaurer les menus et permissions
            $this->restoreMenusAndPermissions($oldDb);
            
            $this->command->info('Restauration des données terminée avec succès !');
            
        } catch (\Exception $e) {
            $this->command->error('Erreur lors de la restauration : ' . $e->getMessage());
        }
    }

    private function restoreUsers($oldDb)
    {
        $this->command->info('Restauration des utilisateurs...');
        
        $users = $oldDb->query("SELECT * FROM users")->fetchAll(\PDO::FETCH_ASSOC);
        
        foreach ($users as $user) {
            DB::table('users')->updateOrInsert(
                ['id' => $user['id']],
                $user
            );
        }
        
        $this->command->info(count($users) . ' utilisateurs restaurés.');
    }

    private function restoreTypeReformes($oldDb)
    {
        $this->command->info('Restauration des types de réforme...');
        
        try {
            $types = $oldDb->query("SELECT * FROM type_reforme")->fetchAll(\PDO::FETCH_ASSOC);
            
            foreach ($types as $type) {
                DB::table('type_reforme')->updateOrInsert(
                    ['id' => $type['id']],
                    $type
                );
            }
            
            $this->command->info(count($types) . ' types de réforme restaurés.');
        } catch (\Exception $e) {
            $this->command->warn('Table type_reforme non trouvée dans l\'ancienne base.');
        }
    }

    private function restoreStructures($oldDb)
    {
        $this->command->info('Restauration des structures...');
        
        try {
            $structures = $oldDb->query("SELECT * FROM structure")->fetchAll(\PDO::FETCH_ASSOC);
            
            foreach ($structures as $structure) {
                DB::table('structure')->updateOrInsert(
                    ['id' => $structure['id']],
                    $structure
                );
            }
            
            $this->command->info(count($structures) . ' structures restaurées.');
        } catch (\Exception $e) {
            $this->command->warn('Table structure non trouvée dans l\'ancienne base.');
        }
    }

    private function restoreReformes($oldDb)
    {
        $this->command->info('Restauration des réformes...');
        
        try {
            $reformes = $oldDb->query("SELECT * FROM reformes")->fetchAll(\PDO::FETCH_ASSOC);
            
            foreach ($reformes as $reforme) {
                // Ajouter le nouveau champ statut_manuel comme null
                $reforme['statut_manuel'] = null;
                
                DB::table('reformes')->updateOrInsert(
                    ['id' => $reforme['id']],
                    $reforme
                );
            }
            
            $this->command->info(count($reformes) . ' réformes restaurées.');
        } catch (\Exception $e) {
            $this->command->warn('Table reformes non trouvée dans l\'ancienne base.');
        }
    }

    private function restoreIndicateurs($oldDb)
    {
        $this->command->info('Restauration des indicateurs...');
        
        try {
            $indicateurs = $oldDb->query("SELECT * FROM indicateurs")->fetchAll(\PDO::FETCH_ASSOC);
            
            foreach ($indicateurs as $indicateur) {
                DB::table('indicateurs')->updateOrInsert(
                    ['id' => $indicateur['id']],
                    $indicateur
                );
            }
            
            $this->command->info(count($indicateurs) . ' indicateurs restaurés.');
        } catch (\Exception $e) {
            $this->command->warn('Table indicateurs non trouvée dans l\'ancienne base.');
        }
    }

    private function restoreReformeStructures($oldDb)
    {
        $this->command->info('Restauration des relations réformes-structures...');
        
        try {
            $relations = $oldDb->query("SELECT * FROM reformes_structure")->fetchAll(\PDO::FETCH_ASSOC);
            
            foreach ($relations as $relation) {
                DB::table('reformes_structure')->updateOrInsert(
                    [
                        'reforme_id' => $relation['reforme_id'],
                        'structure_id' => $relation['structure_id']
                    ],
                    $relation
                );
            }
            
            $this->command->info(count($relations) . ' relations réformes-structures restaurées.');
        } catch (\Exception $e) {
            $this->command->warn('Table reformes_structure non trouvée dans l\'ancienne base.');
        }
    }

    private function restoreReformeIndicateurs($oldDb)
    {
        $this->command->info('Restauration des relations réformes-indicateurs...');
        
        try {
            $relations = $oldDb->query("SELECT * FROM reformes_indicateurs")->fetchAll(\PDO::FETCH_ASSOC);
            
            foreach ($relations as $relation) {
                DB::table('reformes_indicateurs')->updateOrInsert(
                    [
                        'reforme_id' => $relation['reforme_id'],
                        'indicateur_id' => $relation['indicateur_id']
                    ],
                    $relation
                );
            }
            
            $this->command->info(count($relations) . ' relations réformes-indicateurs restaurées.');
        } catch (\Exception $e) {
            $this->command->warn('Table reformes_indicateurs non trouvée dans l\'ancienne base.');
        }
    }

    private function restoreActivites($oldDb)
    {
        $this->command->info('Restauration des activités...');
        
        try {
            $activites = $oldDb->query("SELECT * FROM activites_reformes")->fetchAll(\PDO::FETCH_ASSOC);
            
            foreach ($activites as $activite) {
                DB::table('activites_reformes')->updateOrInsert(
                    ['id' => $activite['id']],
                    $activite
                );
            }
            
            $this->command->info(count($activites) . ' activités restaurées.');
        } catch (\Exception $e) {
            $this->command->warn('Table activites_reformes non trouvée dans l\'ancienne base.');
        }
    }

    private function restoreMenusAndPermissions($oldDb)
    {
        $this->command->info('Restauration des menus et permissions...');
        
        try {
            // Restaurer les menus
            $menus = $oldDb->query("SELECT * FROM menu")->fetchAll(\PDO::FETCH_ASSOC);
            foreach ($menus as $menu) {
                DB::table('menu')->updateOrInsert(['id' => $menu['id']], $menu);
            }
            
            // Restaurer les permissions
            $permissions = $oldDb->query("SELECT * FROM permission")->fetchAll(\PDO::FETCH_ASSOC);
            foreach ($permissions as $permission) {
                DB::table('permission')->updateOrInsert(['id' => $permission['id']], $permission);
            }
            
            // Restaurer les rôles
            $roles = $oldDb->query("SELECT * FROM role")->fetchAll(\PDO::FETCH_ASSOC);
            foreach ($roles as $role) {
                DB::table('role')->updateOrInsert(['id' => $role['id']], $role);
            }
            
            $this->command->info('Menus, permissions et rôles restaurés.');
        } catch (\Exception $e) {
            $this->command->warn('Certaines tables de permissions non trouvées dans l\'ancienne base.');
        }
    }
}
