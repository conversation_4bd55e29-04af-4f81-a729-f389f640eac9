# Système de Notifications Dynamiques

## Vue d'ensemble

Le système de notifications dynamiques permet d'afficher des notifications en temps réel dans la barre supérieure de l'application. Les notifications sont stockées en base de données et s'affichent automatiquement pour chaque utilisateur.

## Fonctionnalités

- ✅ Affichage des notifications en temps réel
- ✅ Compteur de notifications non lues
- ✅ Marquage automatique comme lue lors du clic
- ✅ Bouton "Marquer toutes comme lues"
- ✅ Notifications personnalisées par utilisateur
- ✅ Support des URLs de redirection
- ✅ Styles différenciés (lues/non lues)
- ✅ Helper pour créer facilement des notifications

## Structure des fichiers

### Modèles
- `app/Models/Notification.php` - Modèle Eloquent pour les notifications

### Contrôleurs
- `app/Http/Controllers/NotificationController.php` - API pour gérer les notifications

### Helpers
- `app/Helpers/NotificationHelper.php` - Helper pour créer des notifications

### Vues
- `resources/views/layout/app.blade.php` - Layout principal avec notifications
- `public/css/notifications-dynamiques.css` - Styles CSS

### Base de données
- `database/migrations/2025_04_19_042335_create_notifications_table.php` - Migration
- `database/seeders/NotificationSeeder.php` - Seeder pour les tests

### Routes
- `routes/web.php` - Routes pour les notifications

## Utilisation

### 1. Créer une notification simple

```php
use App\Helpers\NotificationHelper;

// Pour l'utilisateur connecté
NotificationHelper::create('Votre message ici');

// Avec URL de redirection
NotificationHelper::create('Nouvelle réforme ajoutée', '/reforme');
```

### 2. Créer des notifications pour plusieurs utilisateurs

```php
// Pour tous les utilisateurs
NotificationHelper::createForAll('Message pour tous', '/dashboard');

// Pour des utilisateurs spécifiques
NotificationHelper::createForUsers([1, 2, 3], 'Message spécifique', '/profile');

// Pour les utilisateurs avec un rôle spécifique
NotificationHelper::createForRole('admin', 'Message pour les admins', '/admin');
```

### 3. Exemple d'intégration dans un contrôleur

```php
public function store(Request $request)
{
    // ... logique de création ...
    
    // Créer une notification
    NotificationHelper::createForAll(
        'Nouvelle réforme ajoutée : ' . $request->titre,
        '/reforme'
    );
    
    return redirect()->back();
}
```

## API Endpoints

### GET /notifications
Récupère les notifications de l'utilisateur connecté

### POST /notifications/{id}/marquer-lue
Marque une notification comme lue

### POST /notifications/marquer-toutes-lues
Marque toutes les notifications comme lues

### GET /notifications/count-non-lues
Récupère le nombre de notifications non lues

## Installation

1. **Exécuter la migration** (si pas déjà fait) :
```bash
php artisan migrate
```

2. **Ajouter le helper dans composer.json** (déjà fait) :
```json
"autoload": {
    "files": [
        "app/Helpers/NotificationHelper.php"
    ]
}
```

3. **Recharger l'autoloader** :
```bash
composer dump-autoload
```

4. **Ajouter des notifications de test** :
```bash
php artisan db:seed --class=NotificationSeeder
```

## Personnalisation

### Styles CSS
Modifiez `public/css/notifications-dynamiques.css` pour personnaliser l'apparence.

### Comportement JavaScript
Modifiez le script dans `resources/views/layout/app.blade.php` pour changer le comportement.

### Fréquence de rafraîchissement
Par défaut : 30 secondes. Modifiez `setInterval(chargerNotifications, 30000)` pour changer.

## Base de données

### Table `notifications`
- `id` - Clé primaire
- `user_id` - ID de l'utilisateur (foreign key)
- `message` - Message de la notification
- `url` - URL de redirection (optionnel)
- `date_notification` - Date de création
- `statut` - 'N' (non lue) ou 'L' (lue)
- `created_at` / `updated_at` - Timestamps Laravel

## Sécurité

- Toutes les routes sont protégées par le middleware `auth`
- Les utilisateurs ne peuvent voir que leurs propres notifications
- Validation CSRF pour toutes les requêtes AJAX

## Support

Pour toute question ou problème, consultez :
1. Les logs Laravel (`storage/logs/laravel.log`)
2. La console du navigateur pour les erreurs JavaScript
3. Les requêtes réseau dans les outils de développement 