@extends('layout.app')

@section('title', 'Suivi des Indicateurs')

@section('content')
<div class="container-fluid">
    @include('components.page-header', [
        'title' => 'Suivi des Indicateurs',
        'breadcrumb' => [
            ['label' => 'Accueil', 'url' => route('dashboard')],
            ['label' => 'Suivi des Indicateurs']
        ]
    ])

    <!-- Statistiques générales -->
    <div class="row" style="margin-bottom: 30px;">
        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12" style="margin-bottom: 20px;">
            <div class="analytics-sparkle-line" style="padding: 25px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); background: white; border-left: 4px solid #2ecc71;">
                <div class="analytics-content">
                    <h5 style="margin-bottom: 15px; color: #666; font-weight: 600;">Total Indicateurs</h5>
                    <h2 style="margin-bottom: 10px;"><span class="counter" style="color: #2ecc71; font-weight: 700;">{{ $totalIndicateurs }}</span></h2>
                    <span class="text-success" style="font-size: 0.9em;">Indicateurs définis</span>
                    <div class="progress" style="margin-top: 15px; height: 6px; background: #f0f0f0;">
                        <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%; background: #2ecc71;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12" style="margin-bottom: 20px;">
            <div class="analytics-sparkle-line" style="padding: 25px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); background: white; border-left: 4px solid #3498db;">
                <div class="analytics-content">
                    <h5 style="margin-bottom: 15px; color: #666; font-weight: 600;">Indicateurs Actifs</h5>
                    <h2 style="margin-bottom: 10px;"><span class="counter" style="color: #3498db; font-weight: 700;">{{ $indicateursActifs }}</span></h2>
                    <span class="text-info" style="font-size: 0.9em;">Avec mesures</span>
                    <div class="progress" style="margin-top: 15px; height: 6px; background: #f0f0f0;">
                        <div class="progress-bar progress-bar-info" role="progressbar" aria-valuenow="{{ $totalIndicateurs > 0 ? ($indicateursActifs / $totalIndicateurs) * 100 : 0 }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ $totalIndicateurs > 0 ? ($indicateursActifs / $totalIndicateurs) * 100 : 0 }}%; background: #3498db;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12" style="margin-bottom: 20px;">
            <div class="analytics-sparkle-line" style="padding: 25px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); background: white; border-left: 4px solid #f39c12;">
                <div class="analytics-content">
                    <h5 style="margin-bottom: 15px; color: #666; font-weight: 600;">Total Mesures</h5>
                    <h2 style="margin-bottom: 10px;"><span class="counter" style="color: #f39c12; font-weight: 700;">{{ $totalEvolutions }}</span></h2>
                    <span class="text-warning" style="font-size: 0.9em;">Évolutions enregistrées</span>
                    <div class="progress" style="margin-top: 15px; height: 6px; background: #f0f0f0;">
                        <div class="progress-bar progress-bar-warning" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%; background: #f39c12;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12" style="margin-bottom: 20px;">
            <div class="analytics-sparkle-line" style="padding: 25px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); background: white; border-left: 4px solid #e74c3c;">
                <div class="analytics-content">
                    <h5 style="margin-bottom: 15px; color: #666; font-weight: 600;">Réformes</h5>
                    <h2 style="margin-bottom: 10px;"><span class="counter" style="color: #e74c3c; font-weight: 700;">{{ $reformes->count() }}</span></h2>
                    <span class="text-primary" style="font-size: 0.9em;">Avec indicateurs</span>
                    <div class="progress" style="margin-top: 15px; height: 6px; background: #f0f0f0;">
                        <div class="progress-bar progress-bar-primary" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%; background: #e74c3c;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des réformes avec indicateurs -->
    <div class="row" style="margin-top: 30px;">
        <div class="col-lg-8 col-md-8 col-sm-8 col-xs-12">
            <div class="sparkline13-list" style="background: white; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 30px;">
                <div class="sparkline13-hd" style="padding: 20px 25px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 8px 8px 0 0;">
                    <div class="main-sparkline13-hd">
                        <h1 style="margin: 0; color: #333; font-size: 1.5em; font-weight: 600;">
                            <i class="fa fa-flag" style="color: #3498db; margin-right: 10px;"></i>
                            Réformes et leurs Indicateurs
                        </h1>
                    </div>
                </div>
                <div class="sparkline13-graph" style="padding: 25px;">
                    <div class="datatable-dashv1-list custom-datatable-overright">
                        @if($reformes->count() > 0)
                            @foreach($reformes as $reforme)
                                <div class="panel panel-default" style="margin-bottom: 20px; border: 1px solid #ddd; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                                    <div class="panel-heading" style="background: #f8f9fa; border-bottom: 1px solid #ddd; padding: 15px 20px; border-radius: 6px 6px 0 0;">
                                        <h4 class="panel-title" style="margin: 0;">
                                            <a data-toggle="collapse" href="#reforme-{{ $reforme->id }}" style="text-decoration: none; color: #333; font-weight: 600; display: flex; justify-content: space-between; align-items: center;">
                                                <span>
                                                    <i class="fa fa-flag" style="color: #3498db; margin-right: 10px;"></i>
                                                    {{ $reforme->titre }}
                                                </span>
                                                <span class="badge" style="background: #3498db; color: white; padding: 6px 12px; border-radius: 12px; font-size: 0.85em;">
                                                    {{ $reforme->indicateurs->count() }} indicateur{{ $reforme->indicateurs->count() > 1 ? 's' : '' }}
                                                </span>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="reforme-{{ $reforme->id }}" class="panel-collapse collapse">
                                        <div class="panel-body" style="padding: 25px;">
                                            @if($reforme->indicateurs->count() > 0)
                                                <div class="table-responsive" style="margin-top: 15px;">
                                                    <table class="table table-striped table-hover" style="margin-bottom: 0;">
                                                        <thead style="background: #f8f9fa;">
                                                            <tr>
                                                                <th style="padding: 15px; border-top: none; font-weight: 600; color: #555;">Indicateur</th>
                                                                <th style="padding: 15px; border-top: none; font-weight: 600; color: #555;">Unité</th>
                                                                <th style="padding: 15px; border-top: none; font-weight: 600; color: #555;">Dernière mesure</th>
                                                                <th style="padding: 15px; border-top: none; font-weight: 600; color: #555; text-align: center;">Actions</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach($reforme->indicateurs as $indicateur)
                                                                @php
                                                                    $reformeIndicateur = $reforme->reformeIndicateurs->where('indicateur_id', $indicateur->id)->first();
                                                                    $derniereEvolution = $reformeIndicateur ? $reformeIndicateur->evolutions->sortByDesc('date_evolution')->first() : null;
                                                                @endphp
                                                                <tr style="border-bottom: 1px solid #eee;">
                                                                    <td style="padding: 15px; vertical-align: middle;">
                                                                        <strong style="color: #333;">{{ $indicateur->libelle }}</strong>
                                                                    </td>
                                                                    <td style="padding: 15px; vertical-align: middle;">
                                                                        <span style="color: #666; font-size: 0.9em;">{{ $indicateur->unite }}</span>
                                                                    </td>
                                                                    <td style="padding: 15px; vertical-align: middle;">
                                                                        @if($derniereEvolution)
                                                                            <span class="label label-success" style="padding: 6px 12px; border-radius: 12px; font-size: 0.85em;">
                                                                                {{ $derniereEvolution->valeur }} {{ $indicateur->unite }}
                                                                            </span>
                                                                            <br><small class="text-muted" style="margin-top: 5px; display: inline-block;">{{ $derniereEvolution->date_evolution->format('d/m/Y') }}</small>
                                                                        @else
                                                                            <span class="label label-default" style="padding: 6px 12px; border-radius: 12px; font-size: 0.85em;">Aucune mesure</span>
                                                                        @endif
                                                                    </td>
                                                                    <td style="padding: 15px; vertical-align: middle; text-align: center;">
                                                                        <a href="{{ route('suivi-indicateurs.index', $reforme->id) }}"
                                                                           class="btn btn-primary btn-sm"
                                                                           style="padding: 8px 16px; border-radius: 4px; font-size: 0.9em; text-decoration: none; display: inline-flex; align-items: center; gap: 6px;">
                                                                            <i class="fa fa-line-chart"></i>
                                                                            Voir le suivi
                                                                        </a>
                                                                    </td>
                                                                </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            @else
                                                <p class="text-muted">Aucun indicateur associé à cette réforme.</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i> Aucune réforme avec indicateurs trouvée.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Évolutions récentes -->
        <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
            <div class="sparkline13-list" style="background: white; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 30px;">
                <div class="sparkline13-hd" style="padding: 20px 25px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 8px 8px 0 0;">
                    <div class="main-sparkline13-hd">
                        <h1 style="margin: 0; color: #333; font-size: 1.5em; font-weight: 600;">
                            <i class="fa fa-clock-o" style="color: #f39c12; margin-right: 10px;"></i>
                            Évolutions Récentes
                        </h1>
                    </div>
                </div>
                <div class="sparkline13-graph" style="padding: 25px;">
                    <div class="datatable-dashv1-list custom-datatable-overright">
                        @if($evolutionsRecentes->count() > 0)
                            @foreach($evolutionsRecentes as $evolution)
                                <div class="alert alert-info" style="margin-bottom: 15px; border: 1px solid #d1ecf1; border-radius: 6px; background: #f8f9fa; padding: 15px;">
                                    <div class="alert-title" style="margin-bottom: 8px;">
                                        <strong style="color: #333; font-size: 0.95em;">{{ $evolution->reformeIndicateur->indicateur->libelle }}</strong>
                                    </div>
                                    <div class="alert-text">
                                        <small class="text-muted" style="display: block; margin-bottom: 8px; font-size: 0.85em;">{{ $evolution->reformeIndicateur->reforme->titre }}</small>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span class="label label-primary" style="padding: 6px 12px; border-radius: 12px; font-size: 0.85em; background: #3498db;">
                                                {{ $evolution->valeur }} {{ $evolution->reformeIndicateur->indicateur->unite }}
                                            </span>
                                            <small class="text-muted" style="font-size: 0.8em;">{{ $evolution->date_evolution->format('d/m/Y') }}</small>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="alert alert-warning" style="border: 1px solid #ffeaa7; background: #fdcb6e; color: #2d3436; border-radius: 6px; padding: 20px; text-align: center;">
                                <i class="fa fa-exclamation-triangle" style="margin-right: 8px; color: #e17055;"></i>
                                <strong>Aucune évolution récente</strong>
                                <br><small style="margin-top: 5px; display: inline-block;">Les nouvelles mesures apparaîtront ici</small>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Animation des compteurs
    $('.counter').each(function() {
        var $this = $(this);
        var countTo = $this.text();
        
        $({ countNum: 0 }).animate({
            countNum: countTo
        }, {
            duration: 2000,
            easing: 'linear',
            step: function() {
                $this.text(Math.floor(this.countNum));
            },
            complete: function() {
                $this.text(this.countNum);
            }
        });
    });
});
</script>
@endsection
