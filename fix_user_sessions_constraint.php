<?php

/**
 * Script pour résoudre le problème de contrainte UNIQUE sur user_sessions
 */

echo "🔧 RÉSOLUTION DU PROBLÈME USER_SESSIONS CONSTRAINT\n";
echo "==================================================\n\n";

try {
    $pdo = new PDO('sqlite:database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔍 ANALYSE DU PROBLÈME :\n";
    echo "========================\n";
    
    // Vérifier la structure de la table user_sessions
    $tableInfo = $pdo->query("PRAGMA table_info(user_sessions)")->fetchAll(PDO::FETCH_ASSOC);
    echo "📋 Structure de la table user_sessions :\n";
    foreach ($tableInfo as $column) {
        $unique = $column['pk'] ? ' (PRIMARY KEY)' : '';
        echo "  • {$column['name']} ({$column['type']})$unique\n";
    }
    
    // Vérifier les index et contraintes
    $indexes = $pdo->query("PRAGMA index_list(user_sessions)")->fetchAll(PDO::FETCH_ASSOC);
    echo "\n🔑 Index sur la table :\n";
    foreach ($indexes as $index) {
        $unique = $index['unique'] ? ' (UNIQUE)' : '';
        echo "  • {$index['name']}$unique\n";
    }
    
    // Compter les sessions en double
    $duplicates = $pdo->query("
        SELECT session_id, COUNT(*) as count 
        FROM user_sessions 
        GROUP BY session_id 
        HAVING COUNT(*) > 1
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n📊 Sessions en double :\n";
    if (empty($duplicates)) {
        echo "✅ Aucune session en double trouvée\n";
    } else {
        foreach ($duplicates as $dup) {
            echo "  • Session ID: {$dup['session_id']} - {$dup['count']} occurrences\n";
        }
    }
    
    // Compter le total des sessions
    $totalSessions = $pdo->query("SELECT COUNT(*) FROM user_sessions")->fetchColumn();
    echo "• Total des sessions : $totalSessions\n";
    
    echo "\n🧹 NETTOYAGE DES SESSIONS :\n";
    echo "===========================\n";
    
    // Supprimer les sessions en double (garder la plus récente)
    if (!empty($duplicates)) {
        foreach ($duplicates as $dup) {
            $sessionId = $dup['session_id'];
            echo "🔄 Nettoyage des doublons pour session: $sessionId\n";
            
            // Garder seulement la session la plus récente
            $pdo->exec("
                DELETE FROM user_sessions 
                WHERE session_id = '$sessionId' 
                AND id NOT IN (
                    SELECT id FROM user_sessions 
                    WHERE session_id = '$sessionId' 
                    ORDER BY created_at DESC 
                    LIMIT 1
                )
            ");
        }
        echo "✅ Doublons supprimés\n";
    }
    
    // Supprimer les sessions très anciennes (plus de 24h)
    $yesterday = date('Y-m-d H:i:s', strtotime('-24 hours'));
    $oldSessions = $pdo->exec("DELETE FROM user_sessions WHERE created_at < '$yesterday'");
    echo "🗑️  Sessions anciennes supprimées : $oldSessions\n";
    
    // Supprimer les sessions inactives (plus de 2h sans activité)
    $inactiveTime = date('Y-m-d H:i:s', strtotime('-2 hours'));
    $inactiveSessions = $pdo->exec("DELETE FROM user_sessions WHERE last_activity < '$inactiveTime' AND status = 'active'");
    echo "💤 Sessions inactives supprimées : $inactiveSessions\n";
    
    echo "\n🔧 CORRECTION DE LA CONTRAINTE :\n";
    echo "================================\n";
    
    // Vérifier si on peut recréer la table avec une meilleure structure
    $newTableSql = "
    CREATE TABLE user_sessions_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        session_id VARCHAR(255) UNIQUE NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        login_at DATETIME NOT NULL,
        last_activity DATETIME NOT NULL,
        status VARCHAR(20) DEFAULT 'active',
        created_at DATETIME,
        updated_at DATETIME,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    // Sauvegarder les données existantes
    $existingSessions = $pdo->query("SELECT * FROM user_sessions ORDER BY created_at DESC")->fetchAll(PDO::FETCH_ASSOC);
    echo "💾 Sauvegarde de {count($existingSessions)} sessions existantes\n";
    
    // Recréer la table avec la bonne structure
    $pdo->exec("DROP TABLE IF EXISTS user_sessions_new");
    $pdo->exec($newTableSql);
    echo "🔨 Nouvelle table user_sessions_new créée\n";
    
    // Réinsérer les données uniques
    $insertStmt = $pdo->prepare("
        INSERT OR IGNORE INTO user_sessions_new 
        (user_id, session_id, ip_address, user_agent, login_at, last_activity, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $inserted = 0;
    $seenSessions = [];
    
    foreach ($existingSessions as $session) {
        // Éviter les doublons
        if (!isset($seenSessions[$session['session_id']])) {
            $insertStmt->execute([
                $session['user_id'],
                $session['session_id'],
                $session['ip_address'],
                $session['user_agent'],
                $session['login_at'],
                $session['last_activity'],
                $session['status'],
                $session['created_at'],
                $session['updated_at']
            ]);
            $seenSessions[$session['session_id']] = true;
            $inserted++;
        }
    }
    
    echo "📥 $inserted sessions uniques réinsérées\n";
    
    // Remplacer l'ancienne table
    $pdo->exec("DROP TABLE user_sessions");
    $pdo->exec("ALTER TABLE user_sessions_new RENAME TO user_sessions");
    echo "🔄 Table user_sessions remplacée\n";
    
    echo "\n🧪 TEST DE LA NOUVELLE STRUCTURE :\n";
    echo "==================================\n";
    
    // Test d'insertion d'une nouvelle session
    $testSessionId = 'test_unique_' . uniqid();
    $testInsert = $pdo->prepare("
        INSERT INTO user_sessions 
        (user_id, session_id, ip_address, user_agent, login_at, last_activity, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $currentTime = date('Y-m-d H:i:s');
    $testInsert->execute([
        1,
        $testSessionId,
        '127.0.0.1',
        'Test User Agent',
        $currentTime,
        $currentTime,
        'active',
        $currentTime,
        $currentTime
    ]);
    
    echo "✅ Test d'insertion réussi\n";
    
    // Test de contrainte unique
    try {
        $testInsert->execute([
            2,
            $testSessionId, // Même session_id
            '127.0.0.1',
            'Test User Agent 2',
            $currentTime,
            $currentTime,
            'active',
            $currentTime,
            $currentTime
        ]);
        echo "❌ ERREUR: La contrainte unique ne fonctionne pas!\n";
    } catch (Exception $e) {
        echo "✅ Contrainte unique fonctionne correctement\n";
    }
    
    // Nettoyer le test
    $pdo->exec("DELETE FROM user_sessions WHERE session_id = '$testSessionId'");
    echo "🧹 Session de test supprimée\n";
    
    echo "\n📊 STATISTIQUES FINALES :\n";
    echo "=========================\n";
    
    $finalCount = $pdo->query("SELECT COUNT(*) FROM user_sessions")->fetchColumn();
    $activeCount = $pdo->query("SELECT COUNT(*) FROM user_sessions WHERE status = 'active'")->fetchColumn();
    $uniqueUsers = $pdo->query("SELECT COUNT(DISTINCT user_id) FROM user_sessions")->fetchColumn();
    
    echo "• Total des sessions : $finalCount\n";
    echo "• Sessions actives : $activeCount\n";
    echo "• Utilisateurs uniques : $uniqueUsers\n";
    
    echo "\n🎉 PROBLÈME RÉSOLU !\n";
    echo "====================\n";
    echo "✅ Contrainte UNIQUE corrigée\n";
    echo "✅ Sessions dupliquées supprimées\n";
    echo "✅ Table restructurée avec succès\n";
    echo "✅ Tests de contrainte validés\n";
    
    echo "\n🚀 VOTRE APPLICATION EST PRÊTE !\n";
    echo "================================\n";
    echo "Vous pouvez maintenant accéder au dashboard sans erreur de contrainte.\n";
    
} catch (Exception $e) {
    echo "❌ ERREUR : " . $e->getMessage() . "\n";
    echo "Trace : " . $e->getTraceAsString() . "\n";
}

?>
