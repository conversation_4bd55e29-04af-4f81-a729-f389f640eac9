<?php

/**
 * Script d'exportation complète des données de la base de données
 * 
 * Ce script extrait toutes les données de votre application Laravel
 * et les affiche de manière organisée.
 */

require_once 'vendor/autoload.php';

// Initialiser Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Reforme;
use App\Models\Indicateur;
use App\Models\ReformeIndicateur;
use App\Models\EvolutionIndicateur;
use App\Models\Structure;
use App\Models\Typereforme;
use App\Models\Role;
use App\Models\Permission;
use App\Models\Notification;

echo "🗄️  EXPORTATION COMPLÈTE DES DONNÉES DE LA BASE DE DONNÉES\n";
echo "=========================================================\n\n";

try {
    // 1. UTILISATEURS
    echo "👥 UTILISATEURS :\n";
    echo "================\n";
    $users = User::with('role')->get();
    if ($users->count() > 0) {
        foreach ($users as $user) {
            echo "• ID: {$user->id}\n";
            echo "  Nom: {$user->nom} {$user->prenom}\n";
            echo "  Email: {$user->email}\n";
            echo "  Rôle: " . ($user->role ? $user->role->nom : 'Aucun') . "\n";
            echo "  Créé le: {$user->created_at}\n";
            echo "  Dernière connexion: " . ($user->last_login_at ?? 'Jamais') . "\n";
            echo "  ---\n";
        }
    } else {
        echo "Aucun utilisateur trouvé.\n";
    }
    echo "\n";

    // 2. RÔLES ET PERMISSIONS
    echo "🔐 RÔLES ET PERMISSIONS :\n";
    echo "========================\n";
    $roles = Role::with('permissions')->get();
    foreach ($roles as $role) {
        echo "• Rôle: {$role->nom}\n";
        echo "  Description: {$role->description}\n";
        echo "  Permissions: ";
        if ($role->permissions->count() > 0) {
            echo "\n";
            foreach ($role->permissions as $permission) {
                echo "    - {$permission->nom} ({$permission->menu})\n";
            }
        } else {
            echo "Aucune\n";
        }
        echo "  ---\n";
    }
    echo "\n";

    // 3. STRUCTURES
    echo "🏢 STRUCTURES :\n";
    echo "===============\n";
    $structures = Structure::all();
    if ($structures->count() > 0) {
        foreach ($structures as $structure) {
            echo "• ID: {$structure->id}\n";
            echo "  Nom: {$structure->nom}\n";
            echo "  Description: {$structure->description}\n";
            echo "  ---\n";
        }
    } else {
        echo "Aucune structure trouvée.\n";
    }
    echo "\n";

    // 4. TYPES DE RÉFORMES
    echo "📋 TYPES DE RÉFORMES :\n";
    echo "=====================\n";
    $typesReformes = Typereforme::all();
    if ($typesReformes->count() > 0) {
        foreach ($typesReformes as $type) {
            echo "• ID: {$type->id}\n";
            echo "  Nom: {$type->nom}\n";
            echo "  Description: {$type->description}\n";
            echo "  ---\n";
        }
    } else {
        echo "Aucun type de réforme trouvé.\n";
    }
    echo "\n";

    // 5. RÉFORMES
    echo "🔄 RÉFORMES :\n";
    echo "============\n";
    $reformes = Reforme::with(['typeReforme', 'indicateurs'])->get();
    if ($reformes->count() > 0) {
        foreach ($reformes as $reforme) {
            echo "• ID: {$reforme->id}\n";
            echo "  Titre: {$reforme->titre}\n";
            echo "  Description: {$reforme->description}\n";
            echo "  Type: " . ($reforme->typeReforme ? $reforme->typeReforme->nom : 'Non défini') . "\n";
            echo "  Date début: {$reforme->date_debut}\n";
            echo "  Date fin: {$reforme->date_fin}\n";
            echo "  Statut: {$reforme->statut_manuel}\n";
            echo "  Indicateurs associés: {$reforme->indicateurs->count()}\n";
            if ($reforme->indicateurs->count() > 0) {
                foreach ($reforme->indicateurs as $indicateur) {
                    echo "    - {$indicateur->libelle}\n";
                }
            }
            echo "  ---\n";
        }
    } else {
        echo "Aucune réforme trouvée.\n";
    }
    echo "\n";

    // 6. INDICATEURS
    echo "📊 INDICATEURS :\n";
    echo "===============\n";
    $indicateurs = Indicateur::with('reformes')->get();
    if ($indicateurs->count() > 0) {
        foreach ($indicateurs as $indicateur) {
            echo "• ID: {$indicateur->id}\n";
            echo "  Libellé: {$indicateur->libelle}\n";
            echo "  Description: {$indicateur->description}\n";
            echo "  Unité: {$indicateur->unite}\n";
            echo "  Type: {$indicateur->type}\n";
            echo "  Fréquence: {$indicateur->frequence_collecte}\n";
            echo "  Réformes associées: {$indicateur->reformes->count()}\n";
            if ($indicateur->reformes->count() > 0) {
                foreach ($indicateur->reformes as $reforme) {
                    echo "    - {$reforme->titre}\n";
                }
            }
            echo "  ---\n";
        }
    } else {
        echo "Aucun indicateur trouvé.\n";
    }
    echo "\n";

    // 7. ASSOCIATIONS RÉFORME-INDICATEUR
    echo "🔗 ASSOCIATIONS RÉFORME-INDICATEUR :\n";
    echo "===================================\n";
    $associations = ReformeIndicateur::with(['reforme', 'indicateur'])->get();
    if ($associations->count() > 0) {
        foreach ($associations as $association) {
            echo "• ID: {$association->id}\n";
            echo "  Réforme: " . ($association->reforme ? $association->reforme->titre : 'Non trouvée') . "\n";
            echo "  Indicateur: " . ($association->indicateur ? $association->indicateur->libelle : 'Non trouvé') . "\n";
            echo "  Valeur cible: {$association->valeur_cible}\n";
            echo "  Période: {$association->date_debut_suivi} → {$association->date_fin_suivi}\n";
            echo "  Responsable: {$association->responsable}\n";
            echo "  ---\n";
        }
    } else {
        echo "Aucune association trouvée.\n";
    }
    echo "\n";

    // 8. ÉVOLUTIONS DES INDICATEURS
    echo "📈 ÉVOLUTIONS DES INDICATEURS :\n";
    echo "==============================\n";
    $evolutions = EvolutionIndicateur::with(['reformeIndicateur.reforme', 'reformeIndicateur.indicateur'])
                                    ->orderBy('date_evolution', 'desc')
                                    ->limit(20)
                                    ->get();
    if ($evolutions->count() > 0) {
        echo "Affichage des 20 dernières évolutions :\n\n";
        foreach ($evolutions as $evolution) {
            echo "• Date: {$evolution->date_evolution}\n";
            echo "  Réforme: " . ($evolution->reformeIndicateur->reforme ? $evolution->reformeIndicateur->reforme->titre : 'Non trouvée') . "\n";
            echo "  Indicateur: " . ($evolution->reformeIndicateur->indicateur ? $evolution->reformeIndicateur->indicateur->libelle : 'Non trouvé') . "\n";
            echo "  Valeur: {$evolution->valeur} " . ($evolution->reformeIndicateur->indicateur ? $evolution->reformeIndicateur->indicateur->unite : '') . "\n";
            echo "  Tendance: " . ($evolution->tendance ?? 'Non calculée') . "\n";
            echo "  Source: {$evolution->source}\n";
            echo "  ---\n";
        }
    } else {
        echo "Aucune évolution trouvée.\n";
    }
    echo "\n";

    // 9. NOTIFICATIONS
    echo "🔔 NOTIFICATIONS :\n";
    echo "=================\n";
    $notifications = Notification::with('user')->orderBy('created_at', 'desc')->limit(10)->get();
    if ($notifications->count() > 0) {
        echo "Affichage des 10 dernières notifications :\n\n";
        foreach ($notifications as $notification) {
            echo "• ID: {$notification->id}\n";
            echo "  Utilisateur: " . ($notification->user ? $notification->user->nom . ' ' . $notification->user->prenom : 'Non trouvé') . "\n";
            echo "  Titre: {$notification->titre}\n";
            echo "  Message: {$notification->message}\n";
            echo "  Type: {$notification->type}\n";
            echo "  Lu: " . ($notification->lu ? 'Oui' : 'Non') . "\n";
            echo "  Date: {$notification->created_at}\n";
            echo "  ---\n";
        }
    } else {
        echo "Aucune notification trouvée.\n";
    }
    echo "\n";

    // 10. STATISTIQUES GÉNÉRALES
    echo "📊 STATISTIQUES GÉNÉRALES :\n";
    echo "===========================\n";
    echo "• Nombre total d'utilisateurs: " . User::count() . "\n";
    echo "• Nombre de rôles: " . Role::count() . "\n";
    echo "• Nombre de permissions: " . Permission::count() . "\n";
    echo "• Nombre de structures: " . Structure::count() . "\n";
    echo "• Nombre de types de réformes: " . Typereforme::count() . "\n";
    echo "• Nombre de réformes: " . Reforme::count() . "\n";
    echo "• Nombre d'indicateurs: " . Indicateur::count() . "\n";
    echo "• Nombre d'associations réforme-indicateur: " . ReformeIndicateur::count() . "\n";
    echo "• Nombre d'évolutions d'indicateurs: " . EvolutionIndicateur::count() . "\n";
    echo "• Nombre de notifications: " . Notification::count() . "\n";

    echo "\n🎉 EXPORTATION TERMINÉE AVEC SUCCÈS !\n";
    echo "=====================================\n";

} catch (Exception $e) {
    echo "❌ ERREUR lors de l'exportation : " . $e->getMessage() . "\n";
    echo "Trace : " . $e->getTraceAsString() . "\n";
}

?>
