<?php

/**
 * R<PERSON>umé final complet de toutes vos données
 */

echo "🎯 RÉSUMÉ COMPLET DE TOUTES VOS DONNÉES\n";
echo "=======================================\n\n";

try {
    $pdo = new PDO('sqlite:database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Base de données: database.sqlite\n";
    echo "📊 Taille: " . number_format(filesize('database/database.sqlite') / 1024, 2) . " KB\n\n";
    
    // 1. UTILISATEURS COMPLETS
    echo "👥 VOS UTILISATEURS COMPLETS :\n";
    echo "==============================\n";
    try {
        $stmt = $pdo->query("SELECT u.*, p.nom, p.prenom, p.email, p.tel, p.fonction 
                            FROM users u 
                            LEFT JOIN personne p ON u.personne_id = p.id");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($users) > 0) {
            foreach ($users as $user) {
                echo "• ID: {$user['id']} - {$user['nom']} {$user['prenom']}\n";
                echo "  Email: {$user['email']}\n";
                echo "  Fonction: {$user['fonction']}\n";
                echo "  Téléphone: {$user['tel']}\n";
                echo "  Statut: " . ($user['status'] ? '✅ Actif' : '❌ Inactif') . "\n";
                echo "  Créé le: {$user['created_at']}\n";
                echo "  ---\n";
            }
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 2. ASSOCIATIONS UTILISATEUR-RÔLE CORRIGÉES
    echo "👤 QUI A QUEL RÔLE :\n";
    echo "====================\n";
    try {
        // D'abord découvrir la structure de user_role
        $stmt = $pdo->query("PRAGMA table_info(user_role)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $columnNames = array_column($columns, 'name');
        echo "Colonnes de user_role: " . implode(', ', $columnNames) . "\n\n";
        
        $stmt = $pdo->query("SELECT * FROM user_role");
        $userRoles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($userRoles as $ur) {
            // Récupérer les infos utilisateur
            $stmt2 = $pdo->query("SELECT p.nom, p.prenom, p.email FROM users u 
                                 LEFT JOIN personne p ON u.personne_id = p.id 
                                 WHERE u.id = " . $ur['users_id']);
            $userInfo = $stmt2->fetch(PDO::FETCH_ASSOC);
            
            // Récupérer les infos rôle
            $stmt3 = $pdo->query("SELECT role_name FROM role WHERE id = " . $ur['role_id']);
            $roleInfo = $stmt3->fetch(PDO::FETCH_ASSOC);
            
            echo "• {$userInfo['nom']} {$userInfo['prenom']} ({$userInfo['email']})\n";
            echo "  Rôle: {$roleInfo['role_name']}\n";
            echo "  ---\n";
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 3. SESSIONS UTILISATEURS CORRIGÉES
    echo "🔐 HISTORIQUE DES CONNEXIONS :\n";
    echo "==============================\n";
    try {
        // Découvrir la structure de user_sessions
        $stmt = $pdo->query("PRAGMA table_info(user_sessions)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $columnNames = array_column($columns, 'name');
        echo "Colonnes de user_sessions: " . implode(', ', $columnNames) . "\n\n";
        
        $stmt = $pdo->query("SELECT * FROM user_sessions ORDER BY created_at DESC");
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($sessions) > 0) {
            foreach ($sessions as $session) {
                // Récupérer les infos utilisateur
                $stmt2 = $pdo->query("SELECT p.nom, p.prenom, p.email FROM users u 
                                     LEFT JOIN personne p ON u.personne_id = p.id 
                                     WHERE u.id = " . $session['user_id']);
                $userInfo = $stmt2->fetch(PDO::FETCH_ASSOC);
                
                echo "• {$userInfo['nom']} {$userInfo['prenom']}\n";
                echo "  Date: {$session['created_at']}\n";
                echo "  IP: {$session['ip_address']}\n";
                echo "  Navigateur: " . substr($session['user_agent'], 0, 50) . "...\n";
                echo "  ---\n";
            }
        }
    } catch (Exception $e) {
        echo "Erreur : " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 4. RÉFORMES DÉTAILLÉES
    echo "🔄 VOS RÉFORMES DÉTAILLÉES :\n";
    echo "============================\n";
    foreach ([1, 2] as $reformeId) {
        $stmt = $pdo->query("SELECT * FROM reformes WHERE id = $reformeId");
        $reforme = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($reforme) {
            echo "• RÉFORME #{$reforme['id']}: {$reforme['titre']}\n";
            echo "  Objectifs: {$reforme['objectifs']}\n";
            echo "  Budget: " . number_format($reforme['budget']) . " €\n";
            echo "  Période: {$reforme['date_debut']} → {$reforme['date_fin_prevue']}\n";
            echo "  Statut: " . ($reforme['statut'] == 'C' ? '✅ Complétée' : '🔄 En cours') . "\n";
            
            // Activités de cette réforme
            $stmt2 = $pdo->query("SELECT * FROM activites_reformes WHERE reforme_id = $reformeId");
            $activites = $stmt2->fetchAll(PDO::FETCH_ASSOC);
            
            echo "  Activités (" . count($activites) . ") :\n";
            foreach ($activites as $activite) {
                echo "    - {$activite['libelle']} (Poids: {$activite['poids']}%)\n";
                echo "      Statut: " . ($activite['statut'] == 'A' ? '✅ Achevée' : '🔄 En cours') . "\n";
                
                // Suivis de cette activité
                $stmt3 = $pdo->query("SELECT COUNT(*) FROM suivi_activites WHERE activite_reforme_id = {$activite['id']}");
                $nbSuivis = $stmt3->fetchColumn();
                echo "      Suivis: $nbSuivis\n";
            }
            echo "  ---\n";
        }
    }
    echo "\n";
    
    // 5. STATISTIQUES FINALES
    echo "📊 STATISTIQUES GÉNÉRALES :\n";
    echo "===========================\n";
    
    $stats = [
        'Personnes' => $pdo->query("SELECT COUNT(*) FROM personne")->fetchColumn(),
        'Utilisateurs' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
        'Utilisateurs actifs' => $pdo->query("SELECT COUNT(*) FROM users WHERE status = 1")->fetchColumn(),
        'Rôles' => $pdo->query("SELECT COUNT(*) FROM role")->fetchColumn(),
        'Permissions' => $pdo->query("SELECT COUNT(*) FROM permission")->fetchColumn(),
        'Menus' => $pdo->query("SELECT COUNT(*) FROM menu")->fetchColumn(),
        'Structures' => $pdo->query("SELECT COUNT(*) FROM structure")->fetchColumn(),
        'Types de réformes' => $pdo->query("SELECT COUNT(*) FROM type_reforme")->fetchColumn(),
        'Réformes' => $pdo->query("SELECT COUNT(*) FROM reformes")->fetchColumn(),
        'Réformes complétées' => $pdo->query("SELECT COUNT(*) FROM reformes WHERE statut = 'C'")->fetchColumn(),
        'Indicateurs' => $pdo->query("SELECT COUNT(*) FROM indicateurs")->fetchColumn(),
        'Activités' => $pdo->query("SELECT COUNT(*) FROM activites_reformes")->fetchColumn(),
        'Activités achevées' => $pdo->query("SELECT COUNT(*) FROM activites_reformes WHERE statut = 'A'")->fetchColumn(),
        'Suivis d\'activités' => $pdo->query("SELECT COUNT(*) FROM suivi_activites")->fetchColumn(),
        'Sessions de connexion' => $pdo->query("SELECT COUNT(*) FROM user_sessions")->fetchColumn(),
        'Associations rôle-permission' => $pdo->query("SELECT COUNT(*) FROM role_permission")->fetchColumn(),
        'Associations utilisateur-rôle' => $pdo->query("SELECT COUNT(*) FROM user_role")->fetchColumn(),
        'Associations réforme-structure' => $pdo->query("SELECT COUNT(*) FROM reformes_structure")->fetchColumn(),
    ];
    
    foreach ($stats as $label => $count) {
        echo "• $label: $count\n";
    }
    
    echo "\n";
    
    // 6. INFORMATIONS SYSTÈME
    echo "🔧 INFORMATIONS SYSTÈME :\n";
    echo "=========================\n";
    
    // Dernières modifications
    $lastReformeUpdate = $pdo->query("SELECT MAX(updated_at) FROM reformes")->fetchColumn();
    $lastActiviteUpdate = $pdo->query("SELECT MAX(updated_at) FROM activites_reformes")->fetchColumn();
    $lastSuiviCreate = $pdo->query("SELECT MAX(created_at) FROM suivi_activites")->fetchColumn();
    $lastUserCreate = $pdo->query("SELECT MAX(created_at) FROM users")->fetchColumn();
    
    echo "• Dernière modification de réforme: $lastReformeUpdate\n";
    echo "• Dernière modification d'activité: $lastActiviteUpdate\n";
    echo "• Dernier suivi créé: $lastSuiviCreate\n";
    echo "• Dernier utilisateur créé: $lastUserCreate\n";
    
    // Taille des tables principales
    echo "\n• Tailles des tables principales:\n";
    $mainTables = ['users', 'reformes', 'activites_reformes', 'suivi_activites', 'indicateurs'];
    foreach ($mainTables as $table) {
        $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
        echo "  - $table: $count enregistrements\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur : " . $e->getMessage() . "\n";
}

echo "\n🎉 RÉSUMÉ COMPLET TERMINÉ\n";
echo "=========================\n";
echo "Voici toutes vos données de la base de données !\n";

?>
