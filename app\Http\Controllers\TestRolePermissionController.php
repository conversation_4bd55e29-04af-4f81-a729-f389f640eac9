<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\Role;
use App\Models\Menu;
use App\Models\Permission;
use App\Services\PermissionService;

class TestRolePermissionController extends Controller
{
    protected $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->middleware('auth');
        $this->permissionService = $permissionService;
    }

    /**
     * Page de test du système de permissions
     */
    public function index()
    {
        $user = auth()->user();
        
        // Statistiques générales
        $stats = [
            'total_users' => User::count(),
            'total_roles' => Role::where('is_active', true)->count(),
            'total_menus' => Menu::where('is_active', true)->count(),
            'total_permissions' => Permission::where('is_active', true)->count(),
        ];

        // Informations sur l'utilisateur connecté
        $userInfo = [
            'id' => $user->id,
            'email' => $user->email,
            'roles' => $user->roles->pluck('role_name')->toArray(),
            'has_admin_role' => $user->hasRole('Administrateur'),
            'has_gestionnaire_role' => $user->hasRole('Gestionnaire'),
            'has_utilisateur_role' => $user->hasRole('Utilisateur'),
        ];

        // Test d'accès aux URLs principales
        $urlTests = [
            '/dashboard' => $this->permissionService->userCanAccessUrl($user, '/dashboard'),
            '/reforme' => $this->permissionService->userCanAccessUrl($user, '/reforme'),
            '/activites' => $this->permissionService->userCanAccessUrl($user, '/activites'),
            '/role' => $this->permissionService->userCanAccessUrl($user, '/role'),
            '/utilisateurs' => $this->permissionService->userCanAccessUrl($user, '/utilisateurs'),
        ];

        // Test des permissions CRUD
        $crudTests = [
            'reforme_create' => $this->permissionService->userHasPermissionForUrl($user, '/reforme', 'Créer'),
            'reforme_read' => $this->permissionService->userHasPermissionForUrl($user, '/reforme', 'Lire'),
            'reforme_update' => $this->permissionService->userHasPermissionForUrl($user, '/reforme', 'Modifier'),
            'reforme_delete' => $this->permissionService->userHasPermissionForUrl($user, '/reforme', 'Supprimer'),
            'activites_create' => $this->permissionService->userHasPermissionForUrl($user, '/activites', 'Créer'),
            'activites_read' => $this->permissionService->userHasPermissionForUrl($user, '/activites', 'Lire'),
            'activites_update' => $this->permissionService->userHasPermissionForUrl($user, '/activites', 'Modifier'),
            'activites_delete' => $this->permissionService->userHasPermissionForUrl($user, '/activites', 'Supprimer'),
        ];

        // Rapport complet de permissions
        $permissionReport = $this->permissionService->generateUserPermissionReport($user);

        return view('test.role-permission', compact(
            'stats', 
            'userInfo', 
            'urlTests', 
            'crudTests', 
            'permissionReport'
        ));
    }

    /**
     * Test AJAX des permissions
     */
    public function testPermission(Request $request)
    {
        $request->validate([
            'url' => 'required|string',
            'permission' => 'required|string'
        ]);

        $user = auth()->user();
        $hasPermission = $this->permissionService->userHasPermissionForUrl(
            $user, 
            $request->url, 
            $request->permission
        );

        return response()->json([
            'user_id' => $user->id,
            'user_email' => $user->email,
            'url' => $request->url,
            'permission' => $request->permission,
            'has_permission' => $hasPermission,
            'user_roles' => $user->roles->pluck('role_name')->toArray(),
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Simuler une connexion avec un rôle spécifique pour les tests
     */
    public function simulateLogin(Request $request)
    {
        if (!auth()->user()->hasRole('Administrateur')) {
            return response()->json(['error' => 'Accès refusé'], 403);
        }

        $request->validate([
            'user_id' => 'required|exists:users,id'
        ]);

        $user = User::findOrFail($request->user_id);
        Auth::login($user);

        Log::info('Simulation de connexion pour test', [
            'admin_user' => auth()->id(),
            'simulated_user' => $user->id,
            'simulated_roles' => $user->roles->pluck('role_name')->toArray()
        ]);

        return response()->json([
            'success' => true,
            'message' => "Connecté en tant que {$user->email}",
            'user' => [
                'id' => $user->id,
                'email' => $user->email,
                'roles' => $user->roles->pluck('role_name')->toArray()
            ]
        ]);
    }

    /**
     * Vérifier l'état du système de permissions
     */
    public function systemCheck()
    {
        $checks = [];

        // Vérifier que les méthodes du modèle User existent
        $user = auth()->user();
        $checks['user_methods'] = [
            'hasRole' => method_exists($user, 'hasRole'),
            'hasAnyRole' => method_exists($user, 'hasAnyRole'),
            'hasAllRoles' => method_exists($user, 'hasAllRoles'),
            'hasPermission' => method_exists($user, 'hasPermission'),
            'canAccessMenu' => method_exists($user, 'canAccessMenu'),
            'hasPermissionByName' => method_exists($user, 'hasPermissionByName'),
        ];

        // Vérifier les relations
        $checks['user_relations'] = [
            'roles_relation' => $user->roles()->exists(),
            'roles_count' => $user->roles()->count(),
        ];

        // Vérifier les directives Blade
        $checks['blade_directives'] = [
            'canCreateUrl_exists' => class_exists('App\Providers\BladeServiceProvider'),
            'permission_service_exists' => class_exists('App\Services\PermissionService'),
        ];

        // Vérifier la configuration
        $checks['configuration'] = [
            'permissions_config_exists' => config('permissions') !== null,
            'default_roles_defined' => !empty(config('permissions.default_role_permissions')),
            'available_permissions_defined' => !empty(config('permissions.available_permissions')),
        ];

        // Vérifier les tables de base de données
        try {
            $checks['database'] = [
                'users_table' => User::count() > 0,
                'roles_table' => Role::count() > 0,
                'menus_table' => Menu::count() > 0,
                'permissions_table' => Permission::count() > 0,
            ];
        } catch (\Exception $e) {
            $checks['database'] = [
                'error' => $e->getMessage()
            ];
        }

        // Vérifier le middleware
        $checks['middleware'] = [
            'role_permission_middleware_exists' => class_exists('App\Http\Middleware\RolePermissionMiddleware'),
        ];

        return response()->json([
            'system_status' => 'checked',
            'timestamp' => now()->toISOString(),
            'checks' => $checks,
            'overall_health' => $this->calculateOverallHealth($checks)
        ]);
    }

    /**
     * Calculer l'état général du système
     */
    private function calculateOverallHealth($checks)
    {
        $totalChecks = 0;
        $passedChecks = 0;

        foreach ($checks as $category => $categoryChecks) {
            if ($category === 'database' && isset($categoryChecks['error'])) {
                $totalChecks += 1;
                continue;
            }

            foreach ($categoryChecks as $check => $result) {
                $totalChecks++;
                if ($result === true || (is_numeric($result) && $result > 0)) {
                    $passedChecks++;
                }
            }
        }

        $healthPercentage = $totalChecks > 0 ? ($passedChecks / $totalChecks) * 100 : 0;

        if ($healthPercentage >= 90) {
            return 'excellent';
        } elseif ($healthPercentage >= 75) {
            return 'good';
        } elseif ($healthPercentage >= 50) {
            return 'warning';
        } else {
            return 'critical';
        }
    }

    /**
     * Générer un rapport détaillé pour débogage
     */
    public function debugReport()
    {
        $user = auth()->user();
        
        $report = [
            'user_info' => [
                'id' => $user->id,
                'email' => $user->email,
                'created_at' => $user->created_at,
            ],
            'roles' => $user->roles->map(function ($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->role_name,
                    'description' => $role->description,
                    'is_active' => $role->is_active,
                    'permissions_count' => $role->permissionMenus()->count()
                ];
            }),
            'accessible_menus' => Menu::where('is_active', true)->get()->map(function ($menu) use ($user) {
                return [
                    'id' => $menu->id,
                    'libelle' => $menu->libelle,
                    'url' => $menu->url,
                    'can_access' => $user->canAccessMenu($menu->id),
                    'permissions' => Permission::where('is_active', true)->get()->map(function ($permission) use ($user, $menu) {
                        return [
                            'permission' => $permission->permission_name,
                            'has_permission' => $user->hasPermission($menu->id, $permission->permission_name)
                        ];
                    })
                ];
            }),
            'system_config' => [
                'available_permissions' => config('permissions.available_permissions'),
                'default_role_permissions' => config('permissions.default_role_permissions'),
                'url_action_mappings' => config('permissions.url_action_mappings'),
            ]
        ];

        return response()->json($report, 200, [], JSON_PRETTY_PRINT);
    }
}
