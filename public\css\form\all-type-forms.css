/* Default
=============================== */
.wrapper {
	margin:0 auto;
	outline:none;
	padding:40px 15px;
	-webkit-box-sizing:content-box;
	-moz-box-sizing:content-box;
	box-sizing:content-box;
	max-width: 640px;
}
.ts-forms .input { position:relative; }

.ts-forms .tsbox { position:relative; margin-bottom:25px; }

.ts-forms .link {
	border-bottom:1px solid #90caf9;
	color:#1e88e5;
	font-size:14px;
	line-height:inherit;
	text-decoration:none;
}
.ts-forms .link:hover { border-bottom:none; }

.ts-forms .inline-group { display:inline-block; }

.ts-forms .hidden { display:none; }

/* Reset for -webkit / -moz browser
=============================== */
.ts-forms input[type="search"]::-webkit-search-decoration,
.ts-forms input[type="search"]::-webkit-search-cancel-button,
.ts-forms input[type="search"]::-webkit-search-results-button,
.ts-forms input[type="search"]::-webkit-search-results-decoration { display:none; }

.ts-forms select,
.ts-forms input[type="button"],
.ts-forms input[type="submit"],
.ts-forms input[type="search"] {
	-webkit-tap-highlight-color:transparent;
	-webkit-tap-highlight-color:rgba(0,0,0,0);
	-webkit-appearance:none;
	-moz-appearance:none;
	appearance:none;
	-webkit-border-radius:0px;
	border-radius:0px;
}

/* Header
=============================== */
.ts-forms .header {
background-color: #ff0000;
border-bottom: 5px solid #e00000;
border-radius: 3px 3px 0 0;
display: block;
position: relative;
}
.ts-forms .header > i {
	color:#fff;
	font-size:31px;
	float:left;
	padding:31px 15px 0 25px;
}
.ts-forms .header p {
font-size: 30px;
margin: 0 auto;
padding: 30px 25px;
text-align: center;
text-transform: uppercase;

color:#FFF;
text-shadow:#ccc 0 1px 0, #c9c9c9 0 2px 0, #bbb 0 3px 0, #b9b9b9 0 4px 0, #aaa 0 5px 0,rgba(0,0,0,.1) 0 6px 1px, rgba(0,0,0,.1) 0 0 5px, rgba(0,0,0,.3) 0 1px 3px, rgba(0,0,0,.15) 0 3px 5px, rgba(0,0,0,.2) 0 5px 10px, rgba(0,0,0,.2) 0 10px 10px, rgba(0,0,0,.1) 0 20px 20px;







}

/* Content
=============================== */
.ts-forms .content { padding:25px 25px 0; }

.ts-forms .content:after {
	clear:both;
	content:".";
	display:block;
	height:0;
	visibility:hidden;
}

/* Footer
=============================== */
.ts-forms .footer {
	background-color:#e8eaf6;
	-webkit-border-radius:0 0 3px 3px;
	-moz-border-radius:0 0 3px 3px;
	-o-border-radius:0 0 3px 3px;
	border-radius:0 0 3px 3px;
	display:block;
	padding:10px 25px;
}
.ts-forms .footer:after {
	clear:both;
	content:".";
	display:block;
	height:0;
	visibility:hidden;
}

/* Dividers
=============================== */
.ts-forms .divider,
.ts-forms .divider-text { border-top:1px solid rgba(0,0,0,.12); height:0; }

.ts-forms .divider-text { text-align:center; }

.ts-forms .divider-text span {
	border:1px solid rgba(0,0,0,.12);
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	background-color:#f9fafd;
	color:#FF0000;
	font-size:16px;
	padding:2px 15px;
	position:relative;
	top:-9px;
	white-space:nowrap;
}

/* Gap-top / gap-bottom classes
=============================== */
.ts-forms .gap-top-20 { margin-top:20px; } /* text-divider top gap after "content"/"ts-row" classes */

.ts-forms .gap-top-45 { margin-top:45px; } /* text-divider top gap after "tsbox" class */

.ts-forms .gap-bottom-45 { margin-bottom:45px; } /* text-divider bottom gap */

.ts-forms .gap-bottom-25 { margin-bottom:25px; } /* line-divider bottom gap */

/* Labels
=============================== */
.ts-forms label {
	display:block;
	color:inherit;
	font-weight:normal;
	text-align:left;
	margin-bottom:0;
}
.ts-forms .label { font-size:14px; margin-bottom:15px; line-height:14px; height:14px; }

.ts-forms .label-center { height:48px; line-height:48px; text-align:center; margin-bottom:0; }

.ts-forms .ts-row > .label{ padding-left:10px; }

/* Radio and checkbox
=============================== */
.ts-forms .radio,
.ts-forms .checkbox,
.ts-forms .radio-toggle,
.ts-forms .checkbox-toggle {
	color:rgba(0,0,0,.87);
	cursor:pointer;
	font-size:15px;
	height:15px;
	margin-bottom:4px;
	position:relative;
	line-height:15px;
}
.ts-forms .radio,
.ts-forms .checkbox,
.ts-forms .inline-group .radio,
.ts-forms .inline-group .checkbox { padding:9px 0 8px 32px; }

.ts-forms .radio-toggle,
.ts-forms .checkbox-toggle,
.ts-forms .inline-group .radio-toggle,
.ts-forms .inline-group .checkbox-toggle { padding:9px 0 8px 58px; }

.ts-forms .radio:last-child,
.ts-forms .checkbox:last-child,
.ts-forms .radio-toggle:last-child,
.ts-forms .checkbox-toggle:last-child { margin-bottom:0; }

.ts-forms .inline-group .radio,
.ts-forms .inline-group .checkbox,
.ts-forms .inline-group .radio-toggle,
.ts-forms .inline-group .checkbox-toggle { display:inline-block; margin-right:25px; }

.ts-forms .radio input,
.ts-forms .checkbox input,
.ts-forms .radio-toggle input,
.ts-forms .checkbox-toggle input { position:absolute; left:-9999px; }

.ts-forms .radio i,
.ts-forms .checkbox i,
.ts-forms .checkbox-toggle i,
.ts-forms .radio-toggle i {
	background-color:#fff;
	border:2px solid rgba(0,0,0,.26);
	display:block;
	height:18px;
	left:0;
	outline:none;
	position:absolute;
	top:5px;
	-webkit-transition:border-color.2s;
	-moz-transition:border-color.2s;
	-ms-transition:border-color.2s;
	-o-transition:border-color.2s;
	transition:border-color.2s;
}
.ts-forms .radio i,
.ts-forms .checkbox i { width:18px; }

.ts-forms .checkbox-toggle i,
.ts-forms .radio-toggle i { width:44px; }

.ts-forms .checkbox i,
.ts-forms .checkbox-toggle i {
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
}
.ts-forms .radio i,
.ts-forms .radio i:after,
.ts-forms .radio-toggle i:before {
	-webkit-border-radius:50%;
	-moz-border-radius:50%;
	-o-border-radius:50%;
	border-radius:50%;
}
.ts-forms .radio-toggle i {
	-webkit-border-radius:13px;
	-moz-border-radius:13px;
	-o-border-radius:13px;
	border-radius:13px;
}
.ts-forms .checkbox-toggle i:before {
	-webkit-border-radius:2px;
	-moz-border-radius:2px;
	-o-border-radius:2px;
	border-radius:2px;
}
.ts-forms .radio i:after {
	background-color:#FF0000;
	content:"";
	height:8px;
	top:5px;
	left:5px;
	opacity:0;
	position:absolute;
	width:8px;
}
.ts-forms .checkbox i:after {
	border-width:0 0 3px 3px;
	border-bottom:solid #f00;
	border-left:solid #f00;
	content:"";
	height:5px;
	top:3px;
	-webkit-transform:rotate(-45deg);
	-moz-transform:rotate(-45deg);
	-ms-transform:rotate(-45deg);
	-o-transform:rotate(-45deg);
	transform:rotate(-45deg);
	left:3px;
	opacity:0;
	position:absolute;
	width:10px;
}
.ts-forms .radio input:checked + i:after,
.ts-forms .checkbox input:checked + i:after { opacity:1; }

.ts-forms .checkbox-toggle i:before,
.ts-forms .radio-toggle i:before {
	border:none;
	background-color:#03a9f4;
	content:"";
	display:block;
	height:14px;
	left:2px;
	position:absolute;
	top:2px;
	width:14px;
}
.ts-forms .checkbox-toggle input:checked + i:before,
.ts-forms .radio-toggle input:checked + i:before { left:28px; }

.ts-forms .checkbox-toggle i:after,
.ts-forms .radio-toggle i:after,
.ts-forms .checkbox-toggle input:checked + i:after,
.ts-forms .radio-toggle input:checked + i:after {
	font-size:10px;
	font-style:normal;
	font-weight:bold;
	line-height:10px;
	position:absolute;
	top:4px;
}
.ts-forms .checkbox-toggle i:after,
.ts-forms .radio-toggle i:after { content:"OFF"; left:22px; }

.ts-forms .checkbox-toggle input:checked + i:after,
.ts-forms .radio-toggle input:checked + i:after { content:"ON"; left:6px; }

.ts-forms .checkbox:hover i,
.ts-forms .radio:hover i,
.ts-forms .checkbox-toggle:hover i,
.ts-forms .radio-toggle:hover i { border:2px solid rgba(48,63,159,.6); }

.ts-forms .radio input:checked + i,
.ts-forms .checkbox input:checked + i,
.ts-forms .radio-toggle input:checked + i,
.ts-forms .checkbox-toggle input:checked + i { border:2px solid #03a9f4; }

.ts-forms .radio input:checked + i,
.ts-forms .checkbox input:checked + i { color:rgba(48,63,159,.9); }

.ts-forms .checkbox-toggle input:checked + i,
.ts-forms .radio-toggle input:checked + i { background-color:#e8eaf6; }

/* Widget
=============================== */
.ts-forms .widget { position: relative; }

.ts-forms .widget .addon,
.ts-forms .widget .addon-btn {
	background:#e0e0e0;
	border:none;
	color:rgba(0,0,0,.56);
	display:block;
	font:16px 'Open Sans',Helvetica,Arial,sans-serif;
	height:48px;
	line-height:48px;
	padding:0;
	position:absolute;
	outline:none;
	overflow:hidden;
	text-align:center;
	top:0;
	z-index:5;
}
.ts-forms .widget .addon-btn,
.ts-forms .widget .addon-btn i {
	cursor:pointer;
	-webkit-transition:all.2s;
	-moz-transition:all.2s;
	-ms-transition:all.2s;
	-o-transition:all.2s;
	transition:all.2s;
}
.ts-forms .widget .addon-btn:hover,
.ts-forms .widget .addon-btn:focus { background-color:#d6d6d6; color:rgba(0,0,0,.87); }

.ts-forms .widget .addon-btn:hover i,
.ts-forms .widget .addon-btn:focus i { color:rgba(0,0,0,.61); }

.ts-forms .widget .adn-left { left:0; }

.ts-forms .widget .adn-right { right:0; }

.ts-forms .widget .addon i,
.ts-forms .widget .addon-btn i { color:rgba(0,0,0,.34); font-size:17px; z-index:2; }

.ts-forms .widget .adn-50 { width:50px; }

.ts-forms .widget .adn-130 { width:130px; }

.ts-forms .widget.right-50 .input { padding-right:50px; }

.ts-forms .widget.left-50 .input { padding-left:50px; }

.ts-forms .widget.right-130 .input { padding-right:130px; }

.ts-forms .widget.left-130 .input { padding-left:130px; }

.ts-forms .widget .adn-left,
.ts-forms .widget.right-50 .input input,
.ts-forms .widget.right-130 .input input {
	-webkit-border-radius:3px 0 0 3px;
	-moz-border-radius:3px 0 0 3px;
	-o-border-radius:3px 0 0 3px;
	border-radius:3px 0 0 3px;
}
.ts-forms .widget .adn-right,
.ts-forms .widget.left-50 .input input,
.ts-forms .widget.left-130 .input input {
	-webkit-border-radius:0 3px 3px 0;
	-moz-border-radius:0 3px 3px 0;
	-o-border-radius:0 3px 3px 0;
	border-radius:0 3px 3px 0;
}
.ts-forms .widget.left-50.right-50 .input input,
.ts-forms .widget.left-50.right-130 .input input,
.ts-forms .widget.left-130.right-50 .input input,
.ts-forms .widget.left-130.right-130 .input input {
	-webkit-border-radius:0;
	-moz-border-radius:0;
	-o-border-radius:0;
	border-radius:0;
}

/* Inputs
=============================== */
.ts-forms input[type="text"],
.ts-forms input[type="password"],
.ts-forms input[type="email"],
.ts-forms input[type="search"],
.ts-forms input[type="url"],
.ts-forms textarea,
.ts-forms select {
	background:#fff;
	border:2px solid rgba(0,0,0,.12);
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	color:rgba(0,0,0,.87);
	display:block;
	font-family:inherit;
	font-size:16px;
	height:48px;
	padding:10px 15px;
	width:100%;
	outline:none;
	-webkit-appearance:none;
	-moz-appearance:none;
	appearance:none;
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box;
	-webkit-transition:all.4s;
	-moz-transition:all.4s;
	-ms-transition:all.4s;
	-o-transition:all.4s;
	transition:all.4s;
}
.ts-forms input[type="text"]:hover,
.ts-forms input[type="password"]:hover,
.ts-forms input[type="email"]:hover,
.ts-forms input[type="search"]:hover,
.ts-forms input[type="url"]:hover,
.ts-forms textarea:hover,
.ts-forms select:hover { border:2px solid #FF0000; }

.ts-forms input[type="text"]:focus,
.ts-forms input[type="password"]:focus,
.ts-forms input[type="email"]:focus,
.ts-forms input[type="search"]:focus,
.ts-forms input[type="url"]:focus,
.ts-forms textarea:focus,
.ts-forms select:focus { border:2px solid #F00; }

.ts-forms .input textarea {
	height:112px;
	overflow:auto;
	min-height:52px;
	resize:vertical;
}

.ts-forms .input textarea:focus { height:128px; }

/* Placeholders
=============================== */
.ts-forms input::-webkit-input-placeholder,
.ts-forms textarea::-webkit-input-placeholder { color:rgba(0,0,0,.54); }

.ts-forms input::-moz-placeholder,
.ts-forms textarea::-moz-placeholder { color:rgba(0,0,0,.54); }

.ts-forms input:-moz-placeholder,
.ts-forms textarea:-moz-placeholder { color:rgba(0,0,0,.54); }

.ts-forms input:-ms-input-placeholder,
.ts-forms textarea:-ms-input-placeholder { color:rgba(0,0,0,.54); }

.ts-forms input:focus::-webkit-input-placeholder,
.ts-forms textarea:focus::-webkit-input-placeholder { color:rgba(0,0,0,.36); }

.ts-forms input:focus::-moz-placeholder,
.ts-forms textarea:focus::-moz-placeholder { color:rgba(0,0,0,.36); }

.ts-forms input:focus:-moz-placeholder,
.ts-forms textarea:focus:-moz-placeholder { color:rgba(0,0,0,.36); }

.ts-forms input:focus:-ms-input-placeholder,
.ts-forms textarea:focus:-ms-input-placeholder { color:rgba(0,0,0,.36); }

/* Select
=============================== */
.ts-forms select { padding-left:13px; }

.ts-forms .multiple-select select { height:auto; }

.ts-forms .select i {
	background:#fff;
	-webkit-box-shadow:0 0 0 11px #fff;
	-moz-box-shadow:0 0 0 11px #fff;
	-o-box-shadow:0 0 0 11px #fff;
	box-shadow:0 0 0 11px #fff;
	height:20px;
	position:absolute;
	pointer-events:none;
	top:14px;
	right:14px;
	width:14px;
}
.ts-forms .select i:after,
.ts-forms .select i:before {
	border-right:4px solid transparent;
	border-left:4px solid transparent;
	content:'';
	position:absolute;
	right:3px;
}
.ts-forms .select i:after { border-top:6px solid rgba(0,0,0,.4); bottom:1px; }

.ts-forms .select i:before { border-bottom:6px solid rgba(0,0,0,.4); top:3px; }

.ts-forms .select { position:relative; }

/* Icons
=============================== */
.ts-forms .icon-left,
.ts-forms .icon-right {
	color:rgba(0,0,0,.54);
	font-size:17px;
	height:38px;
	line-height:38px !important;
	opacity:.6;
	position:absolute;
	text-align:center;
	top:5px;
	width:42px;
	z-index:2;
}
.ts-forms .icon-left {  left:3px; }

.ts-forms .icon-right {  right:3px; }

.ts-forms .icon-left ~ input,
.ts-forms .icon-left ~ textarea { padding-left:58px; }

.ts-forms .icon-right ~ input,
.ts-forms .icon-right ~ textarea { padding-right:58px; }

/* File for upload
=============================== */
.ts-forms .file-button input {
	bottom:-1px;
	font-size:34px;
	opacity:0;
	position:absolute;
	width:108px;
	z-index:0;
}
.ts-forms .prepend-small-btn .file-button input,
.ts-forms .prepend-big-btn .file-button input { left:0; }

.ts-forms .append-small-btn .file-button input,
.ts-forms .append-big-btn .file-button input { right:0; }

.ts-forms .prepend-small-btn .file-button,
.ts-forms .append-small-btn .file-button { width:64px; }

.ts-forms .prepend-big-btn .file-button,
.ts-forms .append-big-btn .file-button { width:106px; }

.ts-forms .prepend-small-btn .file-button,
.ts-forms .prepend-big-btn .file-button { left:4px; }

.ts-forms .append-small-btn .file-button,
.ts-forms .append-big-btn .file-button { right:4px; }

.ts-forms .append-small-btn .file-button,
.ts-forms .append-big-btn .file-button,
.ts-forms .prepend-small-btn .file-button,
.ts-forms .prepend-big-btn .file-button {
	-webkit-border-radius:2px;
	-moz-border-radius:2px;
	-o-border-radius:2px;
	border-radius:2px;
}

.ts-forms .prepend-big-btn input[type="text"] { padding-left:123px; }

.ts-forms .append-big-btn input[type="text"] { padding-right:123px; }

.ts-forms .prepend-small-btn input[type="text"] { padding-left:81px; }

.ts-forms .append-small-btn input[type="text"] { padding-right:81px; }

.ts-forms .input input[type="file"] { cursor:pointer; }

/* Buttons
=============================== */
.ts-forms .primary-btn,
.ts-forms .secondary-btn {
border: medium none;
border-radius: 3px;
color: #fff;
cursor: pointer;
display: block;
font: 16px "Open Sans",Helvetica,Arial,sans-serif;
height: 48px;
margin: 10px auto;
outline: medium none;
padding: 0 60px;
text-align: center;
white-space: nowrap;
}
.ts-forms .primary-btn { position:relative; }

.ts-forms .content .primary-btn,
.ts-forms .content .secondary-btn { margin:0 0 20px 20px; }

.ts-forms .file-button {
	color:#fff;
	display:block;
	font-family:'Open Sans',Helvetica,Arial,sans-serif;
	font-size:14px;
	height:40px;
	line-height:40px;
	outline:none;
	overflow:hidden;
	position:absolute;
	text-align:center;
	top:4px;
	z-index:1;
}
.ts-forms .primary-btn,
.ts-forms .file-button,
.ts-forms .secondary-btn {
	background:#FF0000;
	-webkit-transition:background.2s;
	-moz-transition:background.2s;
	-ms-transition:background.2s;
	-o-transition:background.2s;
	transition:background.2s;
}
.ts-forms .primary-btn:hover,
.ts-forms .file-button:hover,
.ts-forms .secondary-btn:hover { background:#3f51b5; }

.ts-forms .primary-btn:hover.processing { background:#303f9f; cursor:wait; }

.ts-forms .file-button:hover + input { border:2px solid rgba(48,63,159,.6); }

.ts-forms .secondary-btn,
.ts-forms .secondary-btn:hover,
.ts-forms .secondary-btn:active { opacity:.5; }

.ts-forms .primary-btn.processing:before {
	background:rgba(255,255,255,.4);
	content:'';
	height:100%;
	position:absolute;
	top:0;
	left:0;
	width:100%;
	-webkit-animation:processing 3s ease-in-out infinite;
	-moz-animation:processing 3s ease-in-out infinite;;
	-ms-animation:processing 3s ease-in-out infinite;
	-o-animation:processing 3s ease-in-out infinite;
	animation:processing 3s ease-in-out infinite;
}
@-webkit-keyframes processing {
	0% { width:0; }
	100% { width:100%; }
}
@-moz-keyframes processing {
	0% { width:0; }
	100% { width:100%; }
}
@-ms-keyframes processing {
	0% { width:0; }
	100% { width:100%; }
}
@-o-keyframes processing {
	0% { width:0; }
	100% { width:100%; }
}
@keyframes processing {
	0% { width:0; }
	100% { width:100%; }
}

/* Tooltip
=============================== */
.ts-forms .tooltip,
.ts-forms .tooltip-image {
	background-color:#FF0000;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	display:block;
	left:-9999px;
	opacity:0;
	position:absolute;
	z-index:20px;
}
.ts-forms .tooltip {
	color:#fff;
	font:600 13px 'Open Sans',Helvetica,Arial,sans-serif;
	line-height:20px;
	padding:5px 10px;
}
.ts-forms .tooltip-image { padding:2px 2px 1px; }

.ts-forms .input input:focus + .tooltip,
.ts-forms .input textarea:focus + .tooltip,
.ts-forms .select select:focus + .tooltip,
.ts-forms .input input:focus + .tooltip-image,
.ts-forms .input textarea:focus + .tooltip-image,
.ts-forms .select select:focus + .tooltip-image { opacity:1; z-index:5; }

.ts-forms .tooltip-left-top { bottom:100%; margin-bottom:8px; }

.ts-forms .tooltip-left-top:before {
	border-color:#1a237e transparent;
	border-style:solid;
	border-width:8px 7px 0;
	bottom:-6px;
	content:"";
	left:16px;
	position:absolute;
}
.ts-forms .input input:focus + .tooltip-left-top,
.ts-forms .input textarea:focus + .tooltip-left-top,
.ts-forms .select select:focus + .tooltip-left-top { left:0; right:auto; }

.ts-forms .tooltip-right-top { bottom:100%; margin-bottom:8px; }

.ts-forms .tooltip-right-top:before {
	border-color:#FF0000 transparent;
	border-style:solid;
	border-width:8px 7px 0;
	bottom:-6px;
	content:"";
	position:absolute;
	right:16px;
}
.ts-forms .input input:focus + .tooltip-right-top,
.ts-forms .input textarea:focus + .tooltip-right-top,
.ts-forms .select select:focus + .tooltip-right-top { left:auto; right:0; }

.ts-forms .tooltip-left-bottom { margin-top:8px; top:100%; }

.ts-forms .tooltip-left-bottom:before {
	border-color:#1a237e transparent;
	border-style:solid;
	border-width:0 7px 8px;
	top:-6px;
	content:"";
	left:16px;
	position:absolute;
}
.ts-forms .input input:focus + .tooltip-left-bottom,
.ts-forms .input textarea:focus + .tooltip-left-bottom,
.ts-forms .select select:focus + .tooltip-left-bottom { left:0; right:auto; }

.ts-forms .tooltip-right-bottom { margin-top:8px; top:100%; }

.ts-forms .tooltip-right-bottom:before {
	border-color:#1a237e transparent;
	border-style:solid;
	border-width:0 7px 8px;
	top:-6px;
	content:"";
	right:16px;
	position:absolute;
}
.ts-forms .input input:focus + .tooltip-right-bottom,
.ts-forms .input textarea:focus + .tooltip-right-bottom,
.ts-forms .select select:focus + .tooltip-right-bottom { left:auto; right:0; }

.ts-forms .tooltip-right-side { margin-left:8px; top:8px; white-space:nowrap; }

.ts-forms .tooltip-right-side:before {
	border-color:transparent #1a237e;
	border-style:solid;
	border-width:7px 8px 7px 0;
	content:"";
	left:-6px;
	position:absolute;
	top:8px;
}
.ts-forms .input input:focus + .tooltip-right-side,
.ts-forms .input textarea:focus + .tooltip-right-side,
.ts-forms .select select:focus + .tooltip-right-side { left:100%; }

.ts-forms .tooltip-left-side { margin-right:8px; top:8px; white-space:nowrap; }

.ts-forms .tooltip-left-side:before {
	border-color:transparent #1a237e;
	border-style:solid;
	border-width:7px 0 7px 8px;
	content:"";
	right:-6px;
	position:absolute;
	top:8px;
}
.ts-forms .input input:focus + .tooltip-left-side,
.ts-forms .input textarea:focus + .tooltip-left-side,
.ts-forms .select select:focus + .tooltip-left-side { left:auto; right:100%; }

/* Status message
=============================== */
.ts-forms .error-message,
.ts-forms .success-message,
.ts-forms .info-message,
.ts-forms .warning-message {
	border:2px solid;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	display:block;
	font:16px/24px 'Open Sans',Helvetica,Arial,sans-serif;
	padding:15px;
}
.ts-forms .error-message i,
.ts-forms .success-message i,
.ts-forms .info-message i,
.ts-forms .warning-message i {
	font-size:18px;
	float:left;
	height:24px;
	line-height:24px;
	padding-right:10px;
}
.ts-forms .error-message ul,
.ts-forms .success-message ul,
.ts-forms .info-message ul,
.ts-forms .warning-message ul { margin:0; }

.ts-forms span.error-view,
.ts-forms span.success-view,
.ts-forms span.warning-view,
.ts-forms span.info-view {
	display:block;
	font-size:14px;
	height:14px;
	line-height:14px;
	margin-top:5px;
	padding:0 2px;
}
.ts-forms span.hint {
	display:block;
	font-size:13px;
	color:inherit;
	height:13px;
	line-height:13px;
	margin-top:5px;
	padding:0 2px;
}

/* Disabled state
=============================== */
.ts-forms .widget.disabled-view,
.ts-forms .input.disabled-view,
.ts-forms .select.disabled-view,
.ts-forms .checkbox.disabled-view,
.ts-forms .radio.disabled-view,
.ts-forms .checkbox-toggle.disabled-view,
.ts-forms .radio-toggle.disabled-view,
.ts-forms .primary-btn.disabled-view,
.ts-forms .secondary-btn.disabled-view,
.ts-forms .file-button.disabled-view { cursor:default; opacity:.5; }

.ts-forms .input.disabled-view input[type="file"] { cursor:default; }

.ts-forms .widget.disabled-view input,
.ts-forms .input.disabled-view input,
.ts-forms .input.disabled-view textarea,
.ts-forms .select.disabled-view select { border-color:rgba(0,0,0,.12) !important; }

.ts-forms .checkbox.disabled-view i,
.ts-forms .radio.disabled-view i,
.ts-forms .checkbox-toggle.disabled-view i,
.ts-forms .radio-toggle.disabled-view i { border-color:rgba(0,0,0,.26) !important; }

.ts-forms .primary-btn.disabled-view,
.ts-forms .secondary-btn.disabled-view,
.ts-forms .disabled-view .file-button { background:#303f9f; }

.ts-forms .widget.disabled-view .addon-btn:hover,
.ts-forms .widget.disabled-view .addon-btn:focus { background:#e0e0e0; cursor:default; color:rgba(0,0,0,.56); }

.ts-forms .widget.disabled-view .addon-btn i { color:rgba(0,0,0,.24) !important; }

/* Error state
=============================== */
.ts-forms .error-view .checkbox i,
.ts-forms .error-view .radio i,
.ts-forms .error-view .checkbox-toggle i,
.ts-forms .error-view .radio-toggle i,
.ts-forms .error-view input,
.ts-forms .error-view select,
.ts-forms .error-view textarea { background:#ffebee !important; }

.ts-forms .select.error-view i {
	background-color:#ffebee;
	-webkit-box-shadow:0 0 0 12px #ffebee;
	-moz-box-shadow:0 0 0 12px #ffebee;
	-o-box-shadow:0 0 0 12px #ffebee;
	box-shadow:0 0 0 12px #ffebee;
}
.ts-forms .error-view .icon-left,
.ts-forms .error-view .icon-right { border-color:#e57373; }

.ts-forms .error-view .icon-left,
.ts-forms .error-view .icon-right,
.ts-forms span.error-view,
.ts-forms .error-message i { color:#b71c1c; }

.ts-forms .error-message { background:#ffebee; border-color:#b71c1c; color:#b71c1c; }

/* Success state
=============================== */
.ts-forms .success-view .checkbox i,
.ts-forms .success-view .radio i,
.ts-forms .success-view .checkbox-toggle i,
.ts-forms .success-view .radio-toggle i,
.ts-forms .success-view input,
.ts-forms .success-view select,
.ts-forms .success-view textarea { background:#e8f5e9 !important; }

.ts-forms .select.success-view i {
	background-color:#e8f5e9;
	-webkit-box-shadow:0 0 0 12px #e8f5e9;
	-moz-box-shadow:0 0 0 12px #e8f5e9;
	-o-box-shadow:0 0 0 12px #e8f5e9;
	box-shadow:0 0 0 12px #e8f5e9;
}
.ts-forms .success-view .icon-left,
.ts-forms .success-view .icon-right { border-color:#81c784; }

.ts-forms .success-view .icon-left,
.ts-forms .success-view .icon-right,
.ts-forms span.success-view,
.ts-forms .success-message i { color:#1b5e20; }

.ts-forms .success-message { background:#e8f5e9; border-color:#1b5e20; color:#1b5e20; }

/* Warning state
=============================== */
.ts-forms .warning-view .checkbox i,
.ts-forms .warning-view .radio i,
.ts-forms .warning-view .checkbox-toggle i,
.ts-forms .warning-view .radio-toggle i,
.ts-forms .warning-view input,
.ts-forms .warning-view select,
.ts-forms .warning-view textarea { background:#fff8e1 !important; }

.ts-forms .select.warning-view i {
	background-color:#fff8e1;
	-webkit-box-shadow:0 0 0 12px #fff8e1;
	-moz-box-shadow:0 0 0 12px #fff8e1;
	-o-box-shadow:0 0 0 12px #fff8e1;
	box-shadow:0 0 0 12px #fff8e1;
}
.ts-forms .warning-view .icon-left,
.ts-forms .warning-view .icon-right { border-color:#f9a825; }

.ts-forms .warning-view .icon-left,
.ts-forms .warning-view .icon-right,
.ts-forms span.warning-view,
.ts-forms .warning-message i { color:#f57f17; }

.ts-forms .warning-message { background:#fff8e1; border-color:#f57f17; color:#f57f17; }

/* Info state
=============================== */
.ts-forms .info-view .checkbox i,
.ts-forms .info-view .radio i,
.ts-forms .info-view .checkbox-toggle i,
.ts-forms .info-view .radio-toggle i,
.ts-forms .info-view input,
.ts-forms .info-view select,
.ts-forms .info-view textarea { background:#e1f5fe !important; }

.ts-forms .select.info-view i {
	background-color:#e1f5fe;
	-webkit-box-shadow:0 0 0 12px #e1f5fe;
	-moz-box-shadow:0 0 0 12px #e1f5fe;
	-o-box-shadow:0 0 0 12px #e1f5fe;
	box-shadow:0 0 0 12px #e1f5fe;
}
.ts-forms .info-view .icon-left,
.ts-forms .info-view .icon-right { border-color:#0288d1; }

.ts-forms .info-view .icon-left,
.ts-forms .info-view .icon-right,
.ts-forms span.info-view,
.ts-forms .info-message i { color:#01579b; }

.ts-forms .info-message { background:#e1f5fe; border-color:#01579b; color:#01579b; }

/* Ratings
==================================== */
.ts-forms .rating-group { color:rgba(0,0,0,.87); height:30px; line-height:30px; margin-bottom:4px; }

.ts-forms .rating-group:last-child { margin-bottom:0; }

.ts-forms .rating-group .label { float:left; font-size:16px; height:30px; line-height:30px; margin-bottom:0; }

.ts-forms .rating-group .ratings { float:right; height:30px; line-height:30px; }

.ts-forms .ratings input { left:-9999px; position:absolute; }

.ts-forms .ratings input + label {
	color:rgba(0,0,0,.26);
	cursor:pointer;
	font-size:20px;
	float:right;
	padding:0 2px;
	-webkit-transition:color.2s;
	-moz-transition:color.2s;
	-ms-transition:color.2s;
	-o-transition:color.2s;
	transition:color.2s;
}
.ts-forms .ratings input + label:hover,
.ts-forms .ratings input + label:hover ~ label,
.ts-forms .ratings input:checked + label,
.ts-forms .ratings input:checked + label ~ label { color:#303f9f; }

/* Social links
==================================== */
.ts-forms .social-btn,
.ts-forms .social-icon { margin-bottom:6px; position:relative; }

.ts-forms .social-icon { display:inline-block; margin-left:2px; margin-right:2px; }

.ts-forms .social-center { text-align:center; }

.ts-forms .social-btn i,
.ts-forms .social-icon i {
	background-color:rgba(0,0,0,.15);
	color:#fff;
	cursor:pointer;
	font-size:22px;
	left:0;
	line-height:48px;
	position:absolute;
	text-align:center;
	width:48px;
	z-index:2;
}
.ts-forms .social-btn i {
	-webkit-border-radius:3px 0 0 3px;
	-moz-border-radius:3px 0 0 3px;
	-o-border-radius:3px 0 0 3px;
	border-radius:3px 0 0 3px;
}
.ts-forms .social-icon i {
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
}
.ts-forms .social-btn button,
.ts-forms .social-icon button {
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	border:none;
	color:#fff;
	cursor:pointer;
	font:16px 'Open Sans',Helvetica,Arial,sans-serif;
	padding:0 0 0 48px;
	outline:none;
	overflow:hidden;
	height:48px;
	white-space:nowrap;
	-webkit-transition:background.2s;
	-moz-transition:background.2s;
	-ms-transition:background.2s;
	-o-transition:background.2s;
	transition:background.2s;
}
.ts-forms .social-btn button { width:100%; }

.ts-forms .social-icon button { width:48px; }

.ts-forms .social-btn.vk button,
.ts-forms .social-icon.vk button { background:rgb(47,80,112); }
.ts-forms .social-btn.vk:hover button,
.ts-forms .social.vk:hover button { background:rgba(47,80,112,.85); }

.ts-forms .social-btn.skype button,
.ts-forms .social-icon.skype button { background:rgb(19,176,237); }
.ts-forms .social-btn.skype:hover button,
.ts-forms .social-icon.skype:hover button { background:rgba(19,176,237,.85); }

.ts-forms .social-btn.yahoo button,
.ts-forms .social-icon.yahoo button { background:rgb(112,14,156); }
.ts-forms .social-btn.yahoo:hover button,
.ts-forms .social-icon.yahoo:hover button { background:rgba(112,14,156,.85); }

.ts-forms .social-btn.flickr button,
.ts-forms .social-icon.flickr button { background:rgb(254,59,147); }
.ts-forms .social-btn.flickr:hover button,
.ts-forms .social-icon.flickr:hover button { background:rgba(254,59,147,.85); }

.ts-forms .social-btn.tumblr button,
.ts-forms .social-icon.tumblr button { background:rgb(56,72,83); }
.ts-forms .social-btn.tumblr:hover button,
.ts-forms .social-icon.tumblr:hover button { background:rgba(56,72,83,.85); }

.ts-forms .social-btn.google button,
.ts-forms .social-icon.google button { background:rgb(8,104,185); }
.ts-forms .social-btn.google:hover button,
.ts-forms .social-icon.google:hover button { background:rgba(8,104,185,.85); }

.ts-forms .social-btn.twitter button,
.ts-forms .social-icon.twitter button { background:rgb(44,168,210); }
.ts-forms .social-btn.twitter:hover button,
.ts-forms .social-icon.twitter:hover button { background:rgba(44,168,210,.85); }

.ts-forms .social-btn.youtube button,
.ts-forms .social-icon.youtube button { background:rgb(206,51,44); }
.ts-forms .social-btn.youtube:hover button,
.ts-forms .social-icon.youtube:hover button { background:rgba(206,51,44,.85); }

.ts-forms .social-btn.facebook button,
.ts-forms .social-icon.facebook button { background:rgb(48,88,145); }
.ts-forms .social-btn.facebook:hover button,
.ts-forms .social-icon.facebook:hover button { background:rgba(48,88,145,.85); }

.ts-forms .social-btn.linkedin button,
.ts-forms .social-icon.linkedin button { background:rgb(68,152,200); }
.ts-forms .social-btn.linkedin:hover button,
.ts-forms .social-icon.linkedin:hover button { background:rgba(68,152,200,.85); }

.ts-forms .social-btn.pinterest button,
.ts-forms .social-icon.pinterest button { background:rgb(200,40,40); }
.ts-forms .social-btn.pinterest:hover button,
.ts-forms .social-icon.pinterest:hover button { background:rgba(200,40,40,.85); }

.ts-forms .social-btn.google-plus button,
.ts-forms .social-icon.google-plus button { background:rgb(206,77,57); }
.ts-forms .social-btn.google-plus:hover button,
.ts-forms .social-icon.google-plus:hover button { background:rgba(206,77,57,.85); }

/* Captcha
=============================== */
.ts-forms .captcha-group { position: relative; }

.ts-forms .captcha-group .captcha {
	background-color:#e0e0e0;
	border:none;
	-webkit-border-radius:3px 0 0 3px;
	-moz-border-radius:3px 0 0 3px;
	-o-border-radius:3px 0 0 3px;
	border-radius:3px 0 0 3px;
	height:48px;
	line-height:48px;
	position:absolute;
	outline:none;
	text-align:center;
	top:0;
	width:90px;
}
.ts-forms .captcha-group .input { padding-left:90px; }

.ts-forms .captcha-group .input input {
	-webkit-border-radius:0 3px 3px 0;
	-moz-border-radius:0 3px 3px 0;
	-o-border-radius:0 3px 3px 0;
	border-radius:0 3px 3px 0;
}

/* Stepper
=============================== */
.ts-forms .stepper { position:relative; padding-right:40px; }

.ts-forms .stepper input {
	-webkit-border-radius:3px 0 0 3px;
	-moz-border-radius:3px 0 0 3px;
	-o-border-radius:3px 0 0 3px;
	border-radius:3px 0 0 3px;
}

.ts-forms .stepper .stepper-wrapper {
	-webkit-border-radius:0 3px 3px 0;
	-moz-border-radius:0 3px 3px 0;
	-o-border-radius:0 3px 3px 0;
	border-radius:0 3px 3px 0;
	bottom:0;
	outline:none;
	position:absolute;
	right:0;
	top:0;
	overflow:hidden;
	width:40px;
}
.ts-forms .stepper input::-webkit-inner-spin-button,
.ts-forms .stepper input::-webkit-outer-spin-button { -webkit-appearance:none; margin:0; }

.ts-forms .stepper .stepper-arrow {
	background-color:#e0e0e0;
	cursor:pointer;
	display:block;
	height:50%;
	-webkit-transition:background-color.4s;
	-moz-transition:background-color.4s;
	-ms-transition:background-color.4s;
	-o-transition:background-color.4s;
	transition:background-color.4s;
}
.ts-forms .stepper .stepper-arrow:hover { background-color:#d6d6d6; }

.ts-forms .stepper .stepper-arrow.down { bottom: 0; }

.ts-forms .stepper .stepper-arrow.up:after,
.ts-forms .stepper .stepper-arrow.down:after {
	border-right:5px solid transparent;
	border-left:5px solid transparent;
	content:'';
	position:absolute;
	right:16px;
	-webkit-transition:all.4s;
	-moz-transition:all.4s;
	-ms-transition:all.4s;
	-o-transition:all.4s;
	transition:all.4s;
}
.ts-forms .stepper .stepper-arrow.down:after { border-top:7px solid rgba(0,0,0,.56); bottom:13px; }

.ts-forms .stepper .stepper-arrow.up:after { border-bottom: 7px solid rgba(0,0,0,.56); top:13px; }

.ts-forms .stepper .stepper-arrow:hover.down:after { border-top:7px solid rgba(0,0,0,.87); }

.ts-forms .stepper .stepper-arrow:hover.up:after { border-bottom: 7px solid rgba(0,0,0,.87); }

/* Datapicker and Timepicker
=============================== */
.ui-datepicker {
	background-color:#fff;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	border:1px solid rgba(0,0,0,.26);
	-webkit-box-shadow:0 0 2px rgba(0,0,0,.5);
	-moz-box-shadow:0 0 2px rgba(0,0,0,.5);
	-o-box-shadow:0 0 2px rgba(0,0,0,.5);
	box-shadow:0 0 2px rgba(0,0,0,.5);
	color:rgba(0,0,0,.54);
	display:none;
	text-align:center;
	padding:10px 0;
	width:240px;
	z-index:1100 !important;
}
.ui-datepicker-header {
	background-color:#f0f0f0;
	line-height:1.5;
	margin:-2px 0 12px;
	padding:10px;
	position:relative;
}
.ui-datepicker-prev,
.ui-datepicker-next {
	cursor:pointer;
	display:block;
	font-size:18px;
	height:30px;
	position:absolute;
	text-decoration:none;
	top:6px;
	width:30px;
}
.ui-datepicker-prev { border-right:1px solid #FF0000; left:0; background:url(../images/left.png) no-repeat center; }

.ui-datepicker-next { border-left:1px solid #FF0000; right:0; background:url(../images/right.png) no-repeat center; }

.ui-datepicker-calendar { border-collapse:collapse; line-height:1.5; width:100%; }

.ui-datepicker-calendar th span { color:rgba(0,0,0,.26); font-weight:lighter; }

.ui-datepicker-calendar a,
.ui-datepicker-calendar span {
	color:rgba(0,0,0,.54);
	display:block;
	font-size:16px;
	margin:0 auto;
	text-decoration:none;
	width:28px;
}
.ui-datepicker-calendar a:hover,
.ui-datepicker-calendar .ui-state-active { background-color:#FF0000; color:#FFF; font-weight:bold; }

.ui-datepicker-today a { outline:1px solid #FF0000; }

.ui-datepicker-inline {
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box;
	border:2px solid rgba(0,0,0,.12);
	-webkit-box-shadow:none;
	-moz-box-shadow:none;
	-o-box-shadow:none;
	box-shadow:none;
	width:100%;
}
.ui-state-disabled span { color:rgba(0,0,0,.26); }

.ui-timepicker-div .ui-widget-header { background-color:#f0f0f0; margin-bottom:8px; padding:10px 0; }

.ui-timepicker-div dl { text-align:left; }

.ui-timepicker-div dl dt { float:left; clear:left; padding:0 0 0 5px; }

.ui-timepicker-div td { font-size:90%; }

.ui-tpicker-grid-label { background:none; border:none; margin:0; padding:0; }

.ui-timepicker-rtl{ direction:rtl; }

.ui-timepicker-rtl dl { text-align:right; padding:0 5px 0 0; }

.ui-timepicker-rtl dl dt{ float:right; clear:right; }

.ui-timepicker-rtl dl dd { margin:0 40% 10px 10px; }

.ui-timepicker-div { font-size:15px; }

.ui-timepicker-div dl {
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box;
	border-top:1px solid rgba(0,0,0,.26);
	padding:16px 5px;
	margin:16px 0 0;
}
.ui-timepicker-div .ui_tpicker_time { margin:0 10px 10px 40%; }

.ui-timepicker-div .ui_tpicker_hour,
.ui-timepicker-div .ui_tpicker_minute { margin:16px 10px 10px 40%; }

.ui-datepicker-buttonpane { border-top:1px solid rgba(0,0,0,.26); }

.ui-datepicker-buttonpane button {
	background:#e0e0e0;
	border:none;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	color:rgba(0,0,0,.56);
	cursor:pointer;
	font:14px 'Open Sans',Helvetica,Arial,sans-serif;
	padding:5px 10px;
	margin:10px 5px 0;
	-webkit-transition:all.15s;
	-moz-transition:all.15s;
	-ms-transition:all.15s;
	-o-transition:all.15s;
	transition:all.15s;
	outline:none;
}
.ui-datepicker-buttonpane button:hover { background:#d6d6d6; color:rgba(0,0,0,.87); }

/* jQuery Slider
=============================== */
.ui-slider { position:relative; }

.ui-slider .ui-slider-range {
	border:none;
	display:block;
	font-size:11px;
	position:absolute;
	overflow:hidden;
	z-index:1;
}
.ui-slider .ui-slider-handle {
	background-color:#f00;
	border:1px solid #f00;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	cursor:pointer;
	height:16px;
	position:absolute;
	outline:none;
	left:-5px;
	width:16px;
	z-index:2;
}
.ui-slider-horizontal { height:7px; }

.ui-slider-vertical { height:100px; width:7px; }

.ui-slider-horizontal .ui-slider-handle { top:-5px; margin-left:-10px; }

.ui-slider-horizontal .ui-slider-range { top:0; height:100%; }

.ui-slider-horizontal .ui-slider-range-min { left:0; }

.ui-slider-horizontal .ui-slider-range-max { right:0; }

.ui-slider-vertical .ui-slider-range-min { bottom:0; }

.ui-slider-vertical .ui-slider-range { left:0; width:100%; }

.ui-slider.ui-widget-content {
	background-color:#fff;
	border:2px solid #e0e0e0;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
}
.ui-slider-vertical .ui-widget-header,
.ui-slider-horizontal .ui-widget-header { background-color:#f0f0f0; }

.ts-forms .slider-group {
	font:15px 'Open Sans',Helvetica,Arial,sans-serif;
	height:48px;
	line-height:48px;
	padding:0 2px;
	margin-bottom:5px;
	white-space:nowrap;
}
.ts-forms .slider-group label { display:inline-block; color:rgba(0,0,0,.87); padding:0 4px; }

/* Multistep form
=============================== */
.ts-forms fieldset {
	border:none;
	outline:none;
	margin:0;
	padding:0;
	position:absolute;
	opacity:0;
	left:-9999px;
	top:0;
	-webkit-transform:translateY(-4%);
	-moz-transform:translateY(-4%);
	-ms-transform:translateY(-4%);
	-o-transform:translateY(-4%);
	transform:translateY(-4%);
	-webkit-transition:opacity.3s, -webkit-transform.3s;
	-moz-transition:opacity.3s, -moz-transform.3s;
	-ms-transition:opacity.3s, -ms-transform.3s;
	-o-transition:opacity.3s, -o-transform.3s;
	transition:opacity.3s, transform.3s;
}
.ts-forms .steps {
	border:1px solid rgba(0,0,0,.12);
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	margin-bottom:25px;
	text-align:center;
	-webkit-transition:all.3s;
	-moz-transition:all.3s;
	-ms-transition:all.3s;
	-o-transition:all.3s;
	transition:all.3s;
	padding:4px 0;
}
.ts-forms .active-fieldset {
	left:0;
	position:relative;
	opacity:1;
	-webkit-transform:translateY(0);
	-moz-transform:translateY(0);
	-ms-transform:translateY(0);
	-o-transform:translateY(0);
	transform:translateY(0);
}
.ts-forms fieldset .tsbox,
.ts-forms fieldset .ts-row { display:none; }

.ts-forms .active-fieldset .tsbox,
.ts-forms .active-fieldset .ts-row { display:block; }

.ts-forms .steps p { color:rgba(0,0,0,.56); font-size:16px; height:36px; line-height:36px; margin:0; padding:0; }

.ts-forms .active-step span {font-size:13px; height:13px; line-height:13px; color:#FFF !important; }

.ts-forms .steps span { color:rgba(0,0,0,.56); font-size:13px; height:13px; line-height:13px; }

.ts-forms .active-step .steps p {color:#FFF; }

.ts-forms .active-step .steps { background-color:#f00; border:1px solid #e8eaf6;  }

.ts-forms .passed-step .steps { border:1px solid #e8eaf6; background-color:#CCC; }

.ts-forms.j-multistep .input textarea:focus { height:112px; }

/* Modal form
=============================== */
/* Settings for block with links */
.modal-block {
	background-color:#fff;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	-webkit-box-shadow:0 0 15px rgba(0,0,0,.4);
	-moz-box-shadow:0 0 15px rgba(0,0,0,.4);
	-o-box-shadow:0 0 15px rgba(0,0,0,.4);
	box-shadow:0 0 15px rgba(0,0,0,.4);
	color:rgba(0,0,0,.54);
	font-family:'Open Sans',Helvetica,Arial,sans-serif;
	font-size:15px;
	margin:0 auto;
	max-width:320px;
	outline:medium none;
	padding:20px;
}
.modal-block .modal-link {
	border-bottom:1px solid #90caf9;
	color:#1e88e5;
	font-size:14px;
	line-height:inherit;
	text-decoration:none;
}
.modal-block .modal-link:hover { border-bottom:none; }

/* Settings for modal form directly */
.modal-form { display:none; position:fixed; width:100%; z-index:1200; }

.modal-fill {
	background-color:rgba(103,119,129,.5);
	display:none;
	height:100%;
	left:0;
	position:fixed;
	top:0;
	width:100%;
	z-index:1100;
}
.ts-forms .modal-close {
	background-color:rgba(0,0,0,.3);
	-webkit-border-radius:2px;
	-moz-border-radius:2px;
	-o-border-radius:2px;
	border-radius:2px;
	cursor:pointer;
	position:absolute;
	right:8px;
	top:11px;
	-webkit-transition:background-color.15s;
	-moz-transition:background-color.15s;
	-ms-transition:background-color.15s;
	-o-transition:background-color.15s;
	transition:background-color.15s;
}
.ts-forms .modal-close:hover,
.ts-forms .modal-close:focus { background-color:rgba(0,0,0,.6); }

.ts-forms .modal-close i { display:block; height:22px; width:23px; }

.ts-forms .modal-close i:before,
.ts-forms .modal-close i:after {
	background-color:#fff;
	content:'';
	height:3px;
	position:absolute;
	right:1px;
	top:10px;
	width:21px;
}
.ts-forms .modal-close i:before{
	-webkit-transform:rotate(45deg);
	-moz-transform:rotate(45deg);
	-ms-transform:rotate(45deg);
	-o-transform:rotate(45deg);
	transform:rotate(45deg);
}
.ts-forms .modal-close i:after{
	-webkit-transform:rotate(-45deg);
	-moz-transform:rotate(-45deg);
	-ms-transform:rotate(-45deg);
	-o-transform:rotate(-45deg);
	transform:rotate(-45deg);
}

/* Pop-up form
=============================== */
/* Popup menu forms */
.popup-menu { padding:0 15px; }

.popup-list {
	background-color:#f9fafd;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	-webkit-box-shadow:0 0 15px rgba(0,0,0,.4);
	-moz-box-shadow:0 0 15px rgba(0,0,0,.4);
	-o-box-shadow:0 0 15px rgba(0,0,0,.4);
	box-shadow:0 0 15px rgba(0,0,0,.4);
	max-width:100%;
	position:relative;
}
.popup-list:after {
	clear:both;
	content:".";
	display:block;
	height:0;
	visibility:hidden;
}

.popup-list > ul { font-size:0; float:right; outline:none; padding:5px; }

.popup-list > ul > li {
	border-left:1px solid rgba(0,0,0,.12);
	display:inline-block;
	font-family:'Open Sans',Helvetica,Arial,sans-serif;
	font-size:16px;
	line-height:45px;
	padding:0 20px;
	list-style-type:none;
}
.popup-list > ul > li:hover { background-color:#e8eaf6; }

.popup-list-open { position:relative; }

.popup-list-open .ts-forms { margin:10px auto 0; z-index:999; }

.popup-list-open .ts-forms .input textarea:focus { height:112px; }

.popup-list-open .popup-list-wrapper {
	display:none;
	opacity:0;
	position:absolute;
	left:-9999px;
	width:400px;
	-webkit-animation:popup-list-open.4s both;
	-moz-animation:popup-list-open.4s both;
	-ms-animation:popup-list-open.4s both;
	-o-animation:popup-list-open.4s both;
	animation:popup-list-open.4s both;
}
@-webkit-keyframes popup-list-open {
	from { -webkit-transform:translate(0,-10px); transform:translate(0,-10px); }
	to { -webkit-transform:translate(0,0); transform:translate(0,0); }
}
@-moz-keyframes popup-list-open {
	from { -moz-transform:translate(0,-10px); transform:translate(0,-10px); }
	to { -moz-transform:translate(0,0); transform:translate(0,0); }
}
@-ms-keyframes popup-list-open {
	from { -ms-transform:translate(0,-10px); transform:translate(0,-10px); }
	to { -ms-transform:translate(0,0); transform:translate(0,0); }
}
@-o-keyframes popup-list-open {
	from { -o-transform:translate(0,-10px); transform:translate(0,-10px); }
	to { -o-transform:translate(0,0); transform:translate(0,0); }
}
@-keyframes popup-list-open {
	from { transform:translate(0,-10px); }
	to { transform:translate(0,0); }
}

/* Popup bottom form */
.popup-btm-400,
.popup-btm-640 {
	bottom:0;
	position:fixed;
	-webkit-transition:width.3s;
	-moz-transition:width.3s;
	-ms-transition:width.3s;
	-o-transition:width.3s;
	transition:width.3s;
	right:1%;
	z-index:1000;
}
.popup-btm-400 { width:400px; }

.popup-btm-640 { width:640px; }

.popup-btm-400 #popup-input-open,
.popup-btm-400 #popup-input-close,
.popup-btm-640 #popup-input-open,
.popup-btm-640 #popup-input-close { display:none; }

.popup-btm-400 .popup-btm-wrapper,
.popup-btm-640 .popup-btm-wrapper {
	bottom:-500px;
	height:auto;
	position:absolute;
	right:0;
	-webkit-transition:all.4s ease-in-out;
	-moz-transition:all.4s ease-in-out;
	-ms-transition:all.4s ease-in-out;
	-o-transition:all.4s ease-in-out;
	transition:all.4s ease-in-out;
	width:100%;
	z-index:1000;
}
.popup-btm-400 input#popup-input-open:checked ~ .popup-btm-label,
.popup-btm-640 input#popup-input-open:checked ~ .popup-btm-label { opacity:0; cursor:default; }

.popup-btm-400 input#popup-input-close:checked ~ .popup-btm-wrapper,
.popup-btm-640 input#popup-input-close:checked ~ .popup-btm-wrapper,
.popup-btm-400 .popup-btm-wrapper,
.popup-btm-640 .popup-btm-wrapper {
	-webkit-transform:translateY(100%);
	-moz-transform:translateY(100%);
	-ms-transform:translateY(100%);
	-o-transform:translateY(100%);
	transform:translateY(100%);
}
.popup-btm-400 input#popup-input-open:checked ~ .popup-btm-wrapper,
.popup-btm-640 input#popup-input-open:checked ~ .popup-btm-wrapper {
	bottom:5px;
	-webkit-transform:translateY(0);
	-moz-transform:translateY(0);
	-ms-transform:translateY(0);
	-o-transform:translateY(0);
	transform:translateY(0);
}
.popup-btm-400 .ts-forms .input textarea:focus,
.popup-btm-640 .ts-forms .input textarea:focus { height:112px; }

.popup-btm-400 .popup-btm-label,
.popup-btm-640 .popup-btm-label {
	background-color:#f9fafd;
	-webkit-border-radius:3px;
	-moz-border-radius:3px;
	-o-border-radius:3px;
	border-radius:3px;
	-webkit-box-shadow:0 0 15px rgba(0,0,0,.4);
	-moz-box-shadow:0 0 15px rgba(0,0,0,.4);
	-o-box-shadow:0 0 15px rgba(0,0,0,.4);
	box-shadow:0 0 15px rgba(0,0,0,.4);
	bottom:0;
	cursor:pointer;
	color:rgba(0,0,0,.87);
	display:block;
	font:16px 'Open Sans',Helvetica,Arial,sans-serif;
	height:35px;
	text-align:center;
	opacity:1;
	line-height:35px;
	padding:0 30px;
	position:fixed;
	right:1%;
	-webkit-transition:opacity.4s ease-in-out.05s;
	-moz-transition:opacity.4s ease-in-out.05s;
	-ms-transition:opacity.4s ease-in-out.05s;
	-o-transition:opacity.4s ease-in-out.05s;
	transition:opacity.4s ease-in-out.05s;
	white-space:nowrap;
	z-index: 9999;
}
.popup-btm-400 .popup-btm-close,
.popup-btm-640 .popup-btm-close {
	background-color:rgba(0,0,0,.6);
	-webkit-border-radius:2px;
	-moz-border-radius:2px;
	-o-border-radius:2px;
	border-radius:2px;
	cursor:pointer;
	position:absolute;
	right:0;
	top:-25px;
	-webkit-transition:background-color.15s;
	-moz-transition:background-color.15s;
	-ms-transition:background-color.15s;
	-o-transition:background-color.15s;
	transition:background-color.15s;
}
.popup-btm-400 .popup-btm-close:hover,
.popup-btm-400 .popup-btm-close:focus,
.popup-btm-640 .popup-btm-close:hover,
.popup-btm-640 .popup-btm-close:focus { background-color:rgba(0,0,0,.8); }

.popup-btm-400 .popup-btm-close i,
.popup-btm-640 .popup-btm-close i { display:block; height:22px; width:23px; }

.popup-btm-400 .popup-btm-close i:before,
.popup-btm-400 .popup-btm-close i:after,
.popup-btm-640 .popup-btm-close i:before,
.popup-btm-640 .popup-btm-close i:after {
	background-color:#fff;
	content:'';
	height:3px;
	position:absolute;
	right:1px;
	top:10px;
	width:21px;
}
.popup-btm-400 .popup-btm-close i:before,
.popup-btm-640 .popup-btm-close i:before {
	-webkit-transform:rotate(45deg);
	-moz-transform:rotate(45deg);
	-ms-transform:rotate(45deg);
	-o-transform:rotate(45deg);
	transform:rotate(45deg);
}
.popup-btm-400 .popup-btm-close i:after,
.popup-btm-640 .popup-btm-close i:after {
	-webkit-transform:rotate(-45deg);
	-moz-transform:rotate(-45deg);
	-ms-transform:rotate(-45deg);
	-o-transform:rotate(-45deg);
	transform:rotate(-45deg);
}

/*=================================================================*/
/* Grid layout */
/*=================================================================*/
.ts-forms [class*="span"] {
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box;
	float:left;
	padding-left:10px;
	padding-right:10px;
	position:relative;
}
.ts-forms .span1 { width:8.3333%; }
.ts-forms .span2 { width:16.6666%; }
.ts-forms .span3 { width:25%; }
.ts-forms .span4 { width:33.3333%; }
.ts-forms .span5 { width:41.6666%; }
.ts-forms .span6 { width:50%; }
.ts-forms .span7 { width:58.3333%; }
.ts-forms .span8 { width:66.6666%; }
.ts-forms .span9 { width:75%; }
.ts-forms .span10 { width:83.3333%; }
.ts-forms .span11 { width:91.6666%; }
.ts-forms .span12 { width:100%; }

.ts-forms .offset1 { margin-left:8.3333%; }
.ts-forms .offset2 { margin-left:16.6666%; }
.ts-forms .offset3 { margin-left:25%; }
.ts-forms .offset4 { margin-left:33.3333%; }
.ts-forms .offset5 { margin-left:41.6666%; }
.ts-forms .offset6 { margin-left:50%; }
.ts-forms .offset7 { margin-left:58.3333%; }
.ts-forms .offset8 { margin-left:66.6666%; }
.ts-forms .offset9 { margin-left:75%; }
.ts-forms .offset10 { margin-left:83.3333%; }
.ts-forms .offset11 { margin-left:91.6666%; }
.ts-forms .offset12 { margin-left:100%; }

.ts-forms .ts-row{ margin:0 -10px; }

.ts-forms .ts-row:after {
	clear:both;
	content:".";
	display:block;
	height:0;
	visibility:hidden;
}

/* Responsiveness
==================================== */
/* Wrapper-640 */
@media all and (max-width:620px) {

	.wrapper-640 .ts-forms [class*="span"] { margin-right:0; width:100%; }

	.wrapper-640 .ts-forms [class*="offset"] { margin-left:0; }

	.wrapper-640 .ts-forms .label-center { height:14px; line-height:14px; text-align:left; padding-bottom:3px; }

	.wrapper-640 .ts-forms .radio:last-child,
	.wrapper-640 .ts-forms .checkbox:last-child,
	.wrapper-640 .ts-forms .radio-toggle:last-child,
	.wrapper-640 .ts-forms .checkbox-toggle:last-child { margin-bottom:4px; }

	/* Popup menu forms*/
	.popup-list-open > .popup-list-wrapper { width:100%; }
	.popup-list-open { position:static; }
}

/* Wrapper-400 */
@media all and (max-width:380px) {

	.wrapper-400 .ts-forms [class*="span"] { margin-right:0; width:100%; }

	.wrapper-400 [class*="offset"] { margin-left:0;	}

	.wrapper-400 .ts-forms .label-center { height:14px; line-height:14px; text-align:left; padding-bottom:3px; }

	.wrapper-400 .ts-forms .radio:last-child,
	.wrapper-400 .ts-forms .checkbox:last-child,
	.wrapper-400 .ts-forms .radio-toggle:last-child,
	.wrapper-400 .ts-forms .checkbox-toggle:last-child { margin-bottom:4px; }

	/* Responsiveness inside popup menu forms */
	.popup-list-wrapper .ts-forms [class*="span"] { margin-right:0; width:100%; }

	.popup-list-wrapper .ts-forms [class*="offset"] { margin-left:0; }

	.popup-list-wrapper .ts-forms .label-center { height:14px; line-height:14px; text-align:left; padding-bottom:3px; }
}

/* Popup bottom form 400 px*/
@media all and (max-width:410px) {

	.popup-btm-400 { width: 320px; }

	.popup-btm-400 .ts-forms [class*="span"] { margin-right:0; width:100%; }

	.popup-btm-400 .ts-forms [class*="offset"] { margin-left:0; }

	.popup-btm-400 .ts-forms .label-center { height:14px; line-height:14px; text-align:left; padding-bottom:3px; }
}

/* Popup bottom form 640 px*/
@media all and (max-width:650px) {

	.popup-btm-640 { width: 320px; }

	.popup-btm-640 .ts-forms [class*="span"] { margin-right:0; width:100%; }

	.popup-btm-640 .ts-forms [class*="offset"] { margin-left:0; }

	.popup-btm-640 .ts-forms .label-center { height:14px; line-height:14px; text-align:left; padding-bottom:3px; }
}

/* Bootstrap compatibility
=============================== */
.ts-forms .radio,
.ts-forms .checkbox,
.ts-forms .radio-toggle,
.ts-forms .checkbox-toggle { margin-top:0; }

.ts-forms .label {
	padding:0;
	-webkit-border-radius:0;
	-moz-border-radius:0;
	-o-border-radius:0;
	border-radius:0;
}
.ts-forms .radio,
.ts-forms .checkbox,
.ts-forms .radio-toggle,
.ts-forms .checkbox-toggle,
.ts-forms .radio *,
.ts-forms .checkbox *,
.ts-forms .radio-toggle *,
.ts-forms .checkbox-toggle *,
.ts-forms .radio i:after,
.ts-forms .checkbox i:after,
.ts-forms .radio-toggle i:after,
.ts-forms .checkbox-toggle i:after,
.ts-forms .radio i:before,
.ts-forms .checkbox i:before,
.ts-forms .radio-toggle i:before,
.ts-forms .checkbox-toggle i:before {
	-webkit-box-sizing:content-box;
	-moz-box-sizing:content-box;
	box-sizing:content-box;
}

span.error-view{
background: rgba(0, 0, 0, 0) url("../images/Error-128.png") no-repeat scroll right center;
float: right;
height: 26px !important;
margin: 0 !important;
padding: 0;
position: relative;
right: 10px;
text-indent: -99999px;
top: -37px;
width: 26px !important;

}

.error_none span.error-view{
	display:none;
}

.cloneya .primary-btn, .cloneya .secondary-btn {
  padding: 0 20px;
}


/* Cloned elements
=============================== */
.ts-forms .content .clone-btn-right,
.ts-forms .content .clone-btn-left {
	font-size:14px;
	height:48px;
	padding:0;
	position:absolute;
	margin:0;
	width:47px;
}
.ts-forms .ts-row>.clone-btn-right { bottom:25px; right:10px; }

.ts-forms .ts-row>.clone-btn-right.delete { right:60px; }

.ts-forms .tsbox>.clone-btn-right { bottom:0; right:0; }

.ts-forms .tsbox>.clone-btn-right.delete { right:50px; }

.ts-forms .ts-row>.clone-btn-left { bottom:25px; left:10px; }

.ts-forms .ts-row>.clone-btn-left.delete { left:60px; }

.ts-forms .tsbox>.clone-btn-left { bottom:0; left:0; }

.ts-forms .tsbox>.clone-btn-left.delete { left:50px; }

.toclone-widget-right { padding-right:100px; position:relative; }

.toclone-widget-left { padding-left:100px; position:relative; }

.ts-forms .toclone .link { display:inline-block; padding-bottom:3px; margin:0 5px 5px 0; }

.cloneya a.clone{
	background:#ff0000;
	color:#fff;
	padding:15px 12px !important;
}

.cloneya a.delete{
	background:#ff0000;
	color:#fff;
	padding:15px 12px !important;
}

.sp-replacer {
  top: 29px !important;
   padding-left: 10px !important;

}


.datepic .fa.fa-caret-left {
  color: #f00;
}

.datepic .fa.fa-caret-right{
  color: #f00;
}

.fa.fa-caret-left {
  color: #f00;
}

 .fa.fa-caret-right{
  color: #f00;
}

.ui-datepicker{
	background:#006DF0;
	width: 300px;
	padding:10px;
}
.ui-datepicker-header{
	background:#006DF0;
	margin:0px;
}
.ui-datepicker-prev, .ui-datepicker-next{
	font-size:14px;
	color:#fff;
	border-left: 1px solid #006DF0;
	border-right: 1px solid #006DF0;
	line-height: 33px;
}
.ui-datepicker-header a{
	color:#fff;
}
.ui-datepicker-header a:hover{
	color:#fff;
}
.ui-datepicker-title span{
	font-size:16px;
	color:#fff;
}
.ui-datepicker-calendar th span{
	font-size:14px;
	font-weight:700;
	color:#fff;
}
.ui-datepicker-calendar tbody{
	margin-top:10px;
}
.ui-datepicker-calendar a{
	font-size:14px;
	height:30px;
	width:30px;
	line-height:30px;
}
.ui-datepicker-calendar a:hover, .ui-datepicker-calendar .ui-state-active{
	background-color: #999;
}
.ui-datepicker-today a{
	color:#fff;
	outline:1px solid #fff;
}
.ui-datepicker-calendar a{
	color:#fff;
}